using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;

namespace RxjhServer.AOI
{
    /// <summary>
    /// Performance monitoring for the AOI system
    /// Tracks metrics and provides performance insights
    /// </summary>
    public class AOIPerformanceMonitor
    {
        #region Singleton Pattern
        
        private static AOIPerformanceMonitor _instance;
        private static readonly object _lock = new object();
        
        /// <summary>
        /// Get the singleton instance of AOIPerformanceMonitor
        /// </summary>
        public static AOIPerformanceMonitor Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new AOIPerformanceMonitor();
                        }
                    }
                }
                return _instance;
            }
        }
        
        #endregion
        
        #region Performance Metrics
        
        /// <summary>
        /// Performance metrics for AOI operations
        /// </summary>
        public class AOIMetrics
        {
            public long TotalUpdates { get; set; }
            public long TotalUpdateTime { get; set; } // in milliseconds
            public double AverageUpdateTime => TotalUpdates > 0 ? (double)TotalUpdateTime / TotalUpdates : 0;
            public long MaxUpdateTime { get; set; }
            public long MinUpdateTime { get; set; } = long.MaxValue;
            public DateTime LastUpdate { get; set; }
            public long UpdatesPerSecond { get; set; }
            public long MemoryUsage { get; set; }
        }
        
        #endregion
        
        #region Private Fields
        
        private readonly ConcurrentDictionary<string, AOIMetrics> _metrics;
        private readonly Timer _reportTimer;
        private readonly Stopwatch _globalStopwatch;
        private long _lastTotalUpdates;
        private DateTime _lastReportTime;
        
        #endregion
        
        #region Constructor
        
        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private AOIPerformanceMonitor()
        {
            _metrics = new ConcurrentDictionary<string, AOIMetrics>();
            _globalStopwatch = Stopwatch.StartNew();
            _lastReportTime = DateTime.Now;
            
            // Initialize metrics
            _metrics["PlayerUpdates"] = new AOIMetrics();
            _metrics["NPCUpdates"] = new AOIMetrics();
            _metrics["ItemUpdates"] = new AOIMetrics();
            _metrics["BatchUpdates"] = new AOIMetrics();
            _metrics["GridOperations"] = new AOIMetrics();
            
            // Start performance reporting timer (every 30 seconds)
            _reportTimer = new Timer(GeneratePerformanceReport, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            
            LogHelper.WriteLine(LogLevel.Info, "AOI Performance Monitor initialized");
        }
        
        #endregion
        
        #region Performance Tracking
        
        /// <summary>
        /// Start tracking an operation
        /// </summary>
        /// <param name="operationType">Type of operation being tracked</param>
        /// <returns>Stopwatch for timing the operation</returns>
        public Stopwatch StartOperation(string operationType)
        {
            if (!AOIConfiguration.Instance.EnablePerformanceMonitoring)
            {
                return null;
            }
            
            return Stopwatch.StartNew();
        }
        
        /// <summary>
        /// End tracking an operation and record metrics
        /// </summary>
        /// <param name="operationType">Type of operation</param>
        /// <param name="stopwatch">Stopwatch that was timing the operation</param>
        public void EndOperation(string operationType, Stopwatch stopwatch)
        {
            if (!AOIConfiguration.Instance.EnablePerformanceMonitoring || stopwatch == null)
            {
                return;
            }
            
            stopwatch.Stop();
            var elapsedMs = stopwatch.ElapsedMilliseconds;
            
            var metrics = _metrics.GetOrAdd(operationType, _ => new AOIMetrics());
            
            lock (metrics)
            {
                metrics.TotalUpdates++;
                metrics.TotalUpdateTime += elapsedMs;
                metrics.LastUpdate = DateTime.Now;
                
                if (elapsedMs > metrics.MaxUpdateTime)
                {
                    metrics.MaxUpdateTime = elapsedMs;
                }
                
                if (elapsedMs < metrics.MinUpdateTime)
                {
                    metrics.MinUpdateTime = elapsedMs;
                }
            }
        }
        
        /// <summary>
        /// Record a simple metric without timing
        /// </summary>
        /// <param name="operationType">Type of operation</param>
        /// <param name="count">Number of operations</param>
        public void RecordMetric(string operationType, long count = 1)
        {
            if (!AOIConfiguration.Instance.EnablePerformanceMonitoring)
            {
                return;
            }

            var metrics = _metrics.GetOrAdd(operationType, _ => new AOIMetrics());

            lock (metrics)
            {
                metrics.TotalUpdates += count;
                metrics.LastUpdate = DateTime.Now;
            }
        }

        /// <summary>
        /// Record an operation with its duration
        /// </summary>
        /// <param name="operationType">Type of operation</param>
        /// <param name="duration">Duration of the operation</param>
        public void RecordOperation(string operationType, TimeSpan duration)
        {
            if (!AOIConfiguration.Instance.EnablePerformanceMonitoring)
            {
                return;
            }

            var elapsedMs = (long)duration.TotalMilliseconds;
            var metrics = _metrics.GetOrAdd(operationType, _ => new AOIMetrics());

            lock (metrics)
            {
                metrics.TotalUpdates++;
                metrics.TotalUpdateTime += elapsedMs;
                metrics.LastUpdate = DateTime.Now;

                if (elapsedMs > metrics.MaxUpdateTime)
                {
                    metrics.MaxUpdateTime = elapsedMs;
                }

                if (elapsedMs < metrics.MinUpdateTime)
                {
                    metrics.MinUpdateTime = elapsedMs;
                }
            }
        }
        
        #endregion
        
        #region Memory Monitoring
        
        /// <summary>
        /// Update memory usage metrics
        /// </summary>
        public void UpdateMemoryUsage()
        {
            try
            {
                if (!AOIConfiguration.Instance.EnablePerformanceMonitoring)
                {
                    return;
                }
                
                var process = Process.GetCurrentProcess();
                var memoryUsage = process.WorkingSet64;
                
                foreach (var metric in _metrics.Values)
                {
                    metric.MemoryUsage = memoryUsage;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating memory usage: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Reporting
        
        /// <summary>
        /// Generate performance report
        /// </summary>
        /// <param name="state">Timer state (unused)</param>
        private void GeneratePerformanceReport(object state)
        {
            try
            {
                if (!AOIConfiguration.Instance.EnablePerformanceMonitoring)
                {
                    return;
                }
                
                UpdateMemoryUsage();
                CalculateUpdatesPerSecond();
                
                var report = GetPerformanceReport();
                LogHelper.WriteLine(LogLevel.Info, $"AOI Performance Report:\n{report}");
                
                // Check for performance issues
                CheckPerformanceIssues();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error generating performance report: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Calculate updates per second for all metrics
        /// </summary>
        private void CalculateUpdatesPerSecond()
        {
            var currentTime = DateTime.Now;
            var timeDiff = (currentTime - _lastReportTime).TotalSeconds;
            
            if (timeDiff > 0)
            {
                var currentTotalUpdates = _metrics.Values.Sum(m => m.TotalUpdates);
                var updatesDiff = currentTotalUpdates - _lastTotalUpdates;
                
                foreach (var metric in _metrics.Values)
                {
                    metric.UpdatesPerSecond = (long)(updatesDiff / timeDiff);
                }
                
                _lastTotalUpdates = currentTotalUpdates;
                _lastReportTime = currentTime;
            }
        }
        
        /// <summary>
        /// Get detailed performance report
        /// </summary>
        /// <returns>Performance report string</returns>
        public string GetPerformanceReport()
        {
            try
            {
                var report = "=== AOI Performance Report ===\n";
                report += $"Uptime: {_globalStopwatch.Elapsed:hh\\:mm\\:ss}\n";
                report += $"Memory Usage: {GetMemoryUsageString()}\n\n";
                
                foreach (var kvp in _metrics.OrderBy(m => m.Key))
                {
                    var operationType = kvp.Key;
                    var metrics = kvp.Value;
                    
                    report += $"{operationType}:\n";
                    report += $"  Total Operations: {metrics.TotalUpdates:N0}\n";
                    report += $"  Average Time: {metrics.AverageUpdateTime:F2}ms\n";
                    report += $"  Min/Max Time: {metrics.MinUpdateTime}ms / {metrics.MaxUpdateTime}ms\n";
                    report += $"  Operations/sec: {metrics.UpdatesPerSecond:N0}\n";
                    report += $"  Last Update: {metrics.LastUpdate:HH:mm:ss}\n\n";
                }
                
                return report;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error generating performance report: {ex.Message}");
                return "Error generating performance report";
            }
        }
        
        /// <summary>
        /// Get memory usage as formatted string
        /// </summary>
        /// <returns>Memory usage string</returns>
        private string GetMemoryUsageString()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var workingSet = process.WorkingSet64;
                var privateMemory = process.PrivateMemorySize64;
                
                return $"Working Set: {FormatBytes(workingSet)}, Private: {FormatBytes(privateMemory)}";
            }
            catch
            {
                return "Unknown";
            }
        }
        
        /// <summary>
        /// Format bytes to human readable string
        /// </summary>
        /// <param name="bytes">Number of bytes</param>
        /// <returns>Formatted string</returns>
        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:N1} {suffixes[counter]}";
        }
        
        #endregion
        
        #region Performance Issue Detection
        
        /// <summary>
        /// Check for performance issues and log warnings
        /// </summary>
        private void CheckPerformanceIssues()
        {
            try
            {
                foreach (var kvp in _metrics)
                {
                    var operationType = kvp.Key;
                    var metrics = kvp.Value;
                    
                    // Check for slow operations
                    if (metrics.AverageUpdateTime > 50) // More than 50ms average
                    {
                        LogHelper.WriteLine(LogLevel.Warning, 
                            $"AOI Performance Issue: {operationType} average time is {metrics.AverageUpdateTime:F2}ms");
                    }
                    
                    // Check for very slow individual operations
                    if (metrics.MaxUpdateTime > 200) // More than 200ms for single operation
                    {
                        LogHelper.WriteLine(LogLevel.Warning, 
                            $"AOI Performance Issue: {operationType} max time is {metrics.MaxUpdateTime}ms");
                    }
                    
                    // Check for low update rates (if expected to be higher)
                    if (operationType == "PlayerUpdates" && metrics.UpdatesPerSecond < 10 && World.allConnectedChars.Count > 50)
                    {
                        LogHelper.WriteLine(LogLevel.Warning, 
                            $"AOI Performance Issue: Low player update rate ({metrics.UpdatesPerSecond}/sec) with {World.allConnectedChars.Count} players online");
                    }
                }
                
                // Check memory usage
                var process = Process.GetCurrentProcess();
                var memoryMB = process.WorkingSet64 / (1024 * 1024);
                
                if (memoryMB > 2048) // More than 2GB
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"AOI Performance Issue: High memory usage ({memoryMB}MB)");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error checking performance issues: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Public API
        
        /// <summary>
        /// Get metrics for a specific operation type
        /// </summary>
        /// <param name="operationType">Operation type</param>
        /// <returns>Metrics for the operation</returns>
        public AOIMetrics GetMetrics(string operationType)
        {
            return _metrics.GetValueOrDefault(operationType, new AOIMetrics());
        }
        
        /// <summary>
        /// Reset all metrics
        /// </summary>
        public void ResetMetrics()
        {
            foreach (var metric in _metrics.Values)
            {
                lock (metric)
                {
                    metric.TotalUpdates = 0;
                    metric.TotalUpdateTime = 0;
                    metric.MaxUpdateTime = 0;
                    metric.MinUpdateTime = long.MaxValue;
                    metric.UpdatesPerSecond = 0;
                }
            }
            
            _lastTotalUpdates = 0;
            _lastReportTime = DateTime.Now;
            
            LogHelper.WriteLine(LogLevel.Info, "AOI performance metrics reset");
        }
        
        /// <summary>
        /// Get summary statistics
        /// </summary>
        /// <returns>Summary statistics</returns>
        public string GetSummaryStatistics()
        {
            var totalOperations = _metrics.Values.Sum(m => m.TotalUpdates);
            var totalTime = _metrics.Values.Sum(m => m.TotalUpdateTime);
            var averageTime = totalOperations > 0 ? (double)totalTime / totalOperations : 0;
            
            return $"Total Operations: {totalOperations:N0}, Average Time: {averageTime:F2}ms, " +
                   $"Uptime: {_globalStopwatch.Elapsed:hh\\:mm\\:ss}";
        }
        
        #endregion
        
        #region Cleanup
        
        /// <summary>
        /// Dispose of resources
        /// </summary>
        public void Dispose()
        {
            _reportTimer?.Dispose();
            _globalStopwatch?.Stop();
            LogHelper.WriteLine(LogLevel.Info, "AOI Performance Monitor disposed");
        }
        
        #endregion
    }
}
