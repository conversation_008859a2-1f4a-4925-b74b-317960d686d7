using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;

namespace HeroYulgang.Core.Network
{
    /// <summary>
    /// Configuration manager for packet batching system
    /// </summary>
    public static class PacketBatchConfig
    {
        private static readonly string ConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "PacketBatch.json");
        private static BatchSettings _settings;
        private static readonly object _lock = new object();

        /// <summary>
        /// Batch configuration settings
        /// </summary>
        public class BatchSettings
        {
            public bool EnableBatching { get; set; } = true;
            public int BatchWindowMs { get; set; } = 50;
            public int MaxBatchSize { get; set; } = 10;
            public int CompressionOpcode { get; set; } = 587;
            public Dictionary<int, bool> OpcodeSettings { get; set; } = new()
            {
                { 29696, true }, // Movement packets - enabled by default
                { 26368, false }, // NPC info packets - disabled by default
                { 3072, false }   // Attack packets - disabled by default
            };
            public Dictionary<int, BatchMapSettings> MapSettings { get; set; } = new();
        }

        /// <summary>
        /// Per-map batch settings
        /// </summary>
        public class BatchMapSettings
        {
            public bool EnableBatching { get; set; } = true;
            public int BatchWindowMs { get; set; } = 50;
            public int MaxBatchSize { get; set; } = 10;
        }

        /// <summary>
        /// Get current batch settings
        /// </summary>
        public static BatchSettings Settings
        {
            get
            {
                if (_settings == null)
                {
                    LoadSettings();
                }
                return _settings;
            }
        }

        /// <summary>
        /// Load settings from file or create default
        /// </summary>
        private static void LoadSettings()
        {
            lock (_lock)
            {
                try
                {
                    if (File.Exists(ConfigPath))
                    {
                        var json = File.ReadAllText(ConfigPath);
                        _settings = JsonSerializer.Deserialize<BatchSettings>(json) ?? new BatchSettings();
                        LogHelper.WriteLine(LogLevel.Info, "Loaded packet batch configuration from file");
                    }
                    else
                    {
                        _settings = new BatchSettings();
                        SaveSettings();
                        LogHelper.WriteLine(LogLevel.Info, "Created default packet batch configuration");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error loading packet batch config: {ex.Message}");
                    _settings = new BatchSettings();
                }
            }
        }

        /// <summary>
        /// Save current settings to file
        /// </summary>
        public static void SaveSettings()
        {
            lock (_lock)
            {
                try
                {
                    var configDir = Path.GetDirectoryName(ConfigPath);
                    if (!Directory.Exists(configDir))
                    {
                        Directory.CreateDirectory(configDir);
                    }

                    var options = new JsonSerializerOptions
                    {
                        WriteIndented = true
                    };

                    var json = JsonSerializer.Serialize(_settings, options);
                    File.WriteAllText(ConfigPath, json);
                    LogHelper.WriteLine(LogLevel.Info, "Saved packet batch configuration");
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error saving packet batch config: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Check if batching is enabled for specific opcode
        /// </summary>
        public static bool IsBatchingEnabled(int opcode)
        {
            return Settings.EnableBatching && 
                   Settings.OpcodeSettings.TryGetValue(opcode, out var enabled) && 
                   enabled;
        }

        /// <summary>
        /// Check if batching is enabled for specific map
        /// </summary>
        public static bool IsBatchingEnabledForMap(int mapId)
        {
            if (!Settings.EnableBatching) return false;

            if (Settings.MapSettings.TryGetValue(mapId, out var mapSettings))
            {
                return mapSettings.EnableBatching;
            }

            return true; // Default to enabled if no specific map setting
        }

        /// <summary>
        /// Get batch window for specific map
        /// </summary>
        public static int GetBatchWindowForMap(int mapId)
        {
            if (Settings.MapSettings.TryGetValue(mapId, out var mapSettings))
            {
                return mapSettings.BatchWindowMs;
            }

            return Settings.BatchWindowMs;
        }

        /// <summary>
        /// Get max batch size for specific map
        /// </summary>
        public static int GetMaxBatchSizeForMap(int mapId)
        {
            if (Settings.MapSettings.TryGetValue(mapId, out var mapSettings))
            {
                return mapSettings.MaxBatchSize;
            }

            return Settings.MaxBatchSize;
        }

        /// <summary>
        /// Enable/disable batching for specific opcode
        /// </summary>
        public static void SetOpcodeEnabled(int opcode, bool enabled)
        {
            Settings.OpcodeSettings[opcode] = enabled;
            SaveSettings();
        }

        /// <summary>
        /// Set map-specific batch settings
        /// </summary>
        public static void SetMapSettings(int mapId, bool enabled, int windowMs = -1, int maxSize = -1)
        {
            if (!Settings.MapSettings.ContainsKey(mapId))
            {
                Settings.MapSettings[mapId] = new BatchMapSettings();
            }

            var mapSettings = Settings.MapSettings[mapId];
            mapSettings.EnableBatching = enabled;
            
            if (windowMs > 0)
                mapSettings.BatchWindowMs = windowMs;
            
            if (maxSize > 0)
                mapSettings.MaxBatchSize = maxSize;

            SaveSettings();
        }

        /// <summary>
        /// Get performance-optimized settings for high-traffic scenarios
        /// </summary>
        public static BatchSettings GetHighPerformanceSettings()
        {
            return new BatchSettings
            {
                EnableBatching = true,
                BatchWindowMs = 30, // Shorter window for lower latency
                MaxBatchSize = 15,  // Larger batches for better compression
                CompressionOpcode = 587,
                OpcodeSettings = new Dictionary<int, bool>
                {
                    { 29696, true }, // Movement packets
                    { 26368, true }, // NPC info packets
                    { 3072, false }  // Keep attack packets unbatched for responsiveness
                }
            };
        }

        /// <summary>
        /// Get bandwidth-optimized settings for low-bandwidth scenarios
        /// </summary>
        public static BatchSettings GetBandwidthOptimizedSettings()
        {
            return new BatchSettings
            {
                EnableBatching = true,
                BatchWindowMs = 100, // Longer window for better batching
                MaxBatchSize = 20,   // Larger batches for maximum compression
                CompressionOpcode = 587,
                OpcodeSettings = new Dictionary<int, bool>
                {
                    { 29696, true }, // Movement packets
                    { 26368, true }, // NPC info packets
                    { 3072, true }   // Batch everything for maximum bandwidth savings
                }
            };
        }

        /// <summary>
        /// Apply preset configuration
        /// </summary>
        public static void ApplyPreset(string preset)
        {
            switch (preset.ToLower())
            {
                case "performance":
                    _settings = GetHighPerformanceSettings();
                    break;
                case "bandwidth":
                    _settings = GetBandwidthOptimizedSettings();
                    break;
                default:
                    _settings = new BatchSettings();
                    break;
            }
            SaveSettings();
            LogHelper.WriteLine(LogLevel.Info, $"Applied packet batch preset: {preset}");
        }
    }
}
