using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer.AOI
{
    /// <summary>
    /// Adaptive performance optimizer for AOI system
    /// Automatically adjusts parameters based on real-time performance metrics
    /// </summary>
    public class AOIAdaptiveOptimizer
    {
        #region Singleton Pattern
        
        private static readonly Lazy<AOIAdaptiveOptimizer> _instance = new Lazy<AOIAdaptiveOptimizer>(() => new AOIAdaptiveOptimizer());
        public static AOIAdaptiveOptimizer Instance => _instance.Value;
        
        #endregion
        
        #region Configuration
        
        /// <summary>
        /// Optimization check interval in seconds
        /// </summary>
        private const int OPTIMIZATION_INTERVAL = 30;
        
        /// <summary>
        /// Performance history window size
        /// </summary>
        private const int HISTORY_WINDOW_SIZE = 10;
        
        /// <summary>
        /// Minimum player count to trigger optimizations
        /// </summary>
        private const int MIN_PLAYERS_FOR_OPTIMIZATION = 50;
        
        #endregion
        
        #region Performance Metrics
        
        /// <summary>
        /// Performance snapshot for analysis
        /// </summary>
        public class PerformanceSnapshot
        {
            public DateTime Timestamp { get; set; }
            public int PlayerCount { get; set; }
            public int NPCCount { get; set; }
            public double AverageUpdateTime { get; set; }
            public double MaxUpdateTime { get; set; }
            public int UpdatesPerSecond { get; set; }
            public long MemoryUsageMB { get; set; }
            public int QueueSize { get; set; }
            public int DroppedTasks { get; set; }
            public double CPUUsage { get; set; }
        }
        
        /// <summary>
        /// Performance history for trend analysis
        /// </summary>
        private readonly Queue<PerformanceSnapshot> _performanceHistory;
        
        /// <summary>
        /// Current optimization level
        /// </summary>
        public enum OptimizationLevel
        {
            Conservative,   // Default settings
            Balanced,       // Moderate optimizations
            Aggressive,     // Maximum performance
            Emergency       // Survival mode
        }
        
        private OptimizationLevel _currentLevel;
        
        #endregion
        
        #region Optimization Timer
        
        private readonly Timer _optimizationTimer;
        private readonly object _optimizationLock = new object();
        
        #endregion
        
        #region Constructor
        
        private AOIAdaptiveOptimizer()
        {
            _performanceHistory = new Queue<PerformanceSnapshot>();
            _currentLevel = OptimizationLevel.Conservative;
            
            // Start optimization timer
            _optimizationTimer = new Timer(OptimizationCallback, null, 
                TimeSpan.FromSeconds(OPTIMIZATION_INTERVAL), 
                TimeSpan.FromSeconds(OPTIMIZATION_INTERVAL));
                
            LogHelper.WriteLine(LogLevel.Info, "AOI Adaptive Optimizer initialized");
        }
        
        #endregion
        
        #region Performance Analysis
        
        /// <summary>
        /// Optimization timer callback
        /// </summary>
        private void OptimizationCallback(object state)
        {
            try
            {
                lock (_optimizationLock)
                {
                    AnalyzeAndOptimize();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in AOI optimization: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Analyze current performance and apply optimizations
        /// </summary>
        private void AnalyzeAndOptimize()
        {
            // Collect current performance snapshot
            var snapshot = CollectPerformanceSnapshot();
            
            // Add to history
            _performanceHistory.Enqueue(snapshot);
            if (_performanceHistory.Count > HISTORY_WINDOW_SIZE)
            {
                _performanceHistory.Dequeue();
            }
            
            // Skip optimization if not enough players
            if (snapshot.PlayerCount < MIN_PLAYERS_FOR_OPTIMIZATION)
            {
                return;
            }
            
            // Analyze trends and determine optimization level
            var newLevel = DetermineOptimizationLevel(snapshot);
            
            if (newLevel != _currentLevel)
            {
                LogHelper.WriteLine(LogLevel.Info, 
                    $"AOI Optimization level changed from {_currentLevel} to {newLevel}");
                    
                ApplyOptimizationLevel(newLevel);
                _currentLevel = newLevel;
            }
            
            // Apply specific optimizations based on metrics
            ApplySpecificOptimizations(snapshot);
        }
        
        /// <summary>
        /// Collect current performance snapshot
        /// </summary>
        private PerformanceSnapshot CollectPerformanceSnapshot()
        {
            var monitor = AOIPerformanceMonitor.Instance;
            var threadPool = AOIThreadPool.Instance;
            
            // Get player and NPC counts
            var playerCount = World.allConnectedChars.Count;
            var npcCount = 0; // TODO: Get actual NPC count from AOI system
            
            // Get performance metrics
            var playerMetrics = monitor.GetMetrics("PlayerUpdates");
            
            return new PerformanceSnapshot
            {
                Timestamp = DateTime.Now,
                PlayerCount = playerCount,
                NPCCount = npcCount,
                AverageUpdateTime = playerMetrics?.AverageUpdateTime ?? 0,
                MaxUpdateTime = playerMetrics?.MaxUpdateTime ?? 0,
                UpdatesPerSecond = (int)(playerMetrics?.UpdatesPerSecond ?? 0),
                MemoryUsageMB = GC.GetTotalMemory(false) / (1024 * 1024),
                QueueSize = threadPool.GetQueueSize(),
                DroppedTasks = 0, // TODO: Get from thread pool stats
                CPUUsage = GetCPUUsage()
            };
        }
        
        /// <summary>
        /// Determine appropriate optimization level based on performance
        /// </summary>
        private OptimizationLevel DetermineOptimizationLevel(PerformanceSnapshot snapshot)
        {
            var score = CalculatePerformanceScore(snapshot);
            
            if (score < 30) // Critical performance issues
            {
                return OptimizationLevel.Emergency;
            }
            else if (score < 50) // Poor performance
            {
                return OptimizationLevel.Aggressive;
            }
            else if (score < 70) // Moderate performance
            {
                return OptimizationLevel.Balanced;
            }
            else // Good performance
            {
                return OptimizationLevel.Conservative;
            }
        }
        
        /// <summary>
        /// Calculate performance score (0-100, higher is better)
        /// </summary>
        private double CalculatePerformanceScore(PerformanceSnapshot snapshot)
        {
            double score = 100;
            
            // Penalize high update times
            if (snapshot.AverageUpdateTime > 20)
                score -= Math.Min(30, (snapshot.AverageUpdateTime - 20) * 2);
                
            if (snapshot.MaxUpdateTime > 100)
                score -= Math.Min(20, (snapshot.MaxUpdateTime - 100) / 5);
            
            // Penalize low update rates (if we have many players)
            if (snapshot.PlayerCount > 100 && snapshot.UpdatesPerSecond < 20)
                score -= 15;
            
            // Penalize high memory usage
            if (snapshot.MemoryUsageMB > 1024)
                score -= Math.Min(15, (snapshot.MemoryUsageMB - 1024) / 100);
            
            // Penalize large queue sizes
            if (snapshot.QueueSize > 1000)
                score -= Math.Min(10, snapshot.QueueSize / 1000);
            
            // Penalize high CPU usage
            if (snapshot.CPUUsage > 70)
                score -= Math.Min(10, (snapshot.CPUUsage - 70) / 3);
            
            return Math.Max(0, score);
        }
        
        #endregion
        
        #region Optimization Application
        
        /// <summary>
        /// Apply optimization level settings
        /// </summary>
        private void ApplyOptimizationLevel(OptimizationLevel level)
        {
            var config = AOIConfiguration.Instance;
            
            switch (level)
            {
                case OptimizationLevel.Conservative:
                    config.MinUpdateInterval = 100;
                    config.MaxUpdateInterval = 5;
                    config.MaxBatchSize = 100;
                    config.EnableDebugLogging = false;
                    break;
                    
                case OptimizationLevel.Balanced:
                    config.MinUpdateInterval = 150;
                    config.MaxUpdateInterval = 7;
                    config.MaxBatchSize = 75;
                    config.EnableDebugLogging = false;
                    break;
                    
                case OptimizationLevel.Aggressive:
                    config.MinUpdateInterval = 200;
                    config.MaxUpdateInterval = 10;
                    config.MaxBatchSize = 50;
                    config.EnableDebugLogging = false;
                    break;
                    
                case OptimizationLevel.Emergency:
                    config.MinUpdateInterval = 300;
                    config.MaxUpdateInterval = 15;
                    config.MaxBatchSize = 25;
                    config.EnableDebugLogging = false;
                    // Consider disabling AOI for some maps
                    break;
            }
        }
        
        /// <summary>
        /// Apply specific optimizations based on current metrics
        /// </summary>
        private void ApplySpecificOptimizations(PerformanceSnapshot snapshot)
        {
            // Cache optimization
            if (snapshot.MemoryUsageMB > 1024)
            {
                // Trigger cache cleanup
                AOIThreadPool.Instance.QueueCacheCleanup(() => AOICache.Instance.ClearAllCaches());
            }
            
            // Thread pool optimization
            if (snapshot.QueueSize > 5000)
            {
                // Reduce batch sizes temporarily
                AOIConfiguration.Instance.MaxBatchSize = Math.Max(10, AOIConfiguration.Instance.MaxBatchSize / 2);
            }
            
            // Update frequency optimization
            if (snapshot.AverageUpdateTime > 50)
            {
                // Increase update intervals
                AOIConfiguration.Instance.MinUpdateInterval = Math.Min(500, AOIConfiguration.Instance.MinUpdateInterval * 2);
            }
            
            // Emergency measures
            if (snapshot.MaxUpdateTime > 500)
            {
                LogHelper.WriteLine(LogLevel.Warning, "AOI Emergency: Extremely slow updates detected, applying emergency measures");
                
                // Disable AOI for high-load maps temporarily
                var highLoadMaps = GetHighLoadMaps();
                foreach (var mapID in highLoadMaps)
                {
                    AOIConfiguration.Instance.DisableMapAOI(mapID);
                    LogHelper.WriteLine(LogLevel.Warning, $"AOI temporarily disabled for map {mapID}");
                }
            }
        }
        
        /// <summary>
        /// Get maps with high player load
        /// </summary>
        private List<int> GetHighLoadMaps()
        {
            var mapPlayerCounts = new Dictionary<int, int>();
            
            foreach (var player in World.allConnectedChars.Values)
            {
                if (mapPlayerCounts.ContainsKey(player.MapID))
                    mapPlayerCounts[player.MapID]++;
                else
                    mapPlayerCounts[player.MapID] = 1;
            }
            
            // Return maps with more than 200 players
            return mapPlayerCounts.Where(kvp => kvp.Value > 200).Select(kvp => kvp.Key).ToList();
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Get current CPU usage (simplified)
        /// </summary>
        private double GetCPUUsage()
        {
            try
            {
                // This is a simplified CPU usage calculation
                // In a real implementation, you might want to use PerformanceCounter
                return 0; // Placeholder
            }
            catch
            {
                return 0;
            }
        }
        
        /// <summary>
        /// Get optimization statistics
        /// </summary>
        public string GetOptimizationStatistics()
        {
            var recentSnapshots = _performanceHistory.TakeLast(3).ToList();
            if (!recentSnapshots.Any())
                return "No performance data available";
            
            var latest = recentSnapshots.Last();
            var avgUpdateTime = recentSnapshots.Average(s => s.AverageUpdateTime);
            var avgMemory = recentSnapshots.Average(s => s.MemoryUsageMB);
            
            return $"AOI Optimizer - Level: {_currentLevel}, " +
                   $"Players: {latest.PlayerCount}, " +
                   $"Avg Update: {avgUpdateTime:F2}ms, " +
                   $"Memory: {avgMemory:F0}MB, " +
                   $"Queue: {latest.QueueSize}";
        }
        
        /// <summary>
        /// Force optimization analysis
        /// </summary>
        public void ForceOptimization()
        {
            Task.Run(() =>
            {
                lock (_optimizationLock)
                {
                    AnalyzeAndOptimize();
                }
            });
        }
        
        /// <summary>
        /// Reset to conservative settings
        /// </summary>
        public void ResetToConservative()
        {
            ApplyOptimizationLevel(OptimizationLevel.Conservative);
            _currentLevel = OptimizationLevel.Conservative;
            LogHelper.WriteLine(LogLevel.Info, "AOI Optimizer reset to conservative settings");
        }
        
        #endregion
    }
}
