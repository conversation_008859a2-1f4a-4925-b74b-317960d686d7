using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Messaging
{
    /// <summary>
    /// Request-Response service implementation - thay thế Akka Ask pattern
    /// </summary>
    public class RequestResponseService : IRequestResponseService, IDisposable
    {
        private readonly ConcurrentDictionary<string, PendingRequest> _pendingRequests;
        private readonly Timer _timeoutTimer;
        private volatile bool _isDisposed;

        public RequestResponseService()
        {
            _pendingRequests = new ConcurrentDictionary<string, PendingRequest>();
            
            // Timer để cleanup timeout requests
            _timeoutTimer = new Timer(CleanupTimeoutRequests, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        /// <summary>
        /// Gửi request và chờ response (thay thế actor.Ask)
        /// </summary>
        public async Task<TResponse> SendRequestAsync<TRequest, TResponse>(
            IMessageTarget target, 
            TRequest request, 
            TimeSpan? timeout = null,
            CancellationToken cancellationToken = default)
            where TRequest : class
            where TResponse : class
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(RequestResponseService));

            var requestId = Guid.NewGuid().ToString();
            var actualTimeout = timeout ?? TimeSpan.FromSeconds(30);
            var timeoutCts = new CancellationTokenSource(actualTimeout);
            var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var tcs = new TaskCompletionSource<TResponse>();
            var pendingRequest = new PendingRequest(tcs, typeof(TResponse), DateTime.UtcNow.Add(actualTimeout));

            _pendingRequests[requestId] = pendingRequest;

            try
            {
                // Tạo request wrapper với ID
                var requestWrapper = new RequestMessage<TRequest>(request, requestId);
                
                // Gửi request
                await target.SendAsync(requestWrapper, combinedCts.Token);

                // Chờ response hoặc timeout
                var response = await tcs.Task;
                return response;
            }
            catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
            {
                throw new TimeoutException($"Request timeout after {actualTimeout.TotalSeconds} seconds");
            }
            finally
            {
                _pendingRequests.TryRemove(requestId, out _);
                timeoutCts.Dispose();
                combinedCts.Dispose();
            }
        }

        /// <summary>
        /// Xử lý response message
        /// </summary>
        public void HandleResponse<T>(ResponseMessage<T> response) where T : class
        {
            if (_pendingRequests.TryRemove(response.RequestId, out var pendingRequest))
            {
                if (pendingRequest.ResponseType == typeof(T))
                {
                    var tcs = (TaskCompletionSource<T>)pendingRequest.TaskCompletionSource;
                    tcs.SetResult(response.Data);
                }
                else
                {
                    pendingRequest.TaskCompletionSource.SetException(
                        new InvalidOperationException($"Response type mismatch. Expected {pendingRequest.ResponseType}, got {typeof(T)}"));
                }
            }
        }

        /// <summary>
        /// Xử lý error response
        /// </summary>
        public void HandleError(ErrorResponseMessage errorResponse)
        {
            if (_pendingRequests.TryRemove(errorResponse.RequestId, out var pendingRequest))
            {
                pendingRequest.TaskCompletionSource.SetException(errorResponse.Exception);
            }
        }

        /// <summary>
        /// Cleanup timeout requests
        /// </summary>
        private void CleanupTimeoutRequests(object? state)
        {
            var now = DateTime.UtcNow;
            var timeoutRequests = new List<string>();

            foreach (var kvp in _pendingRequests)
            {
                if (kvp.Value.TimeoutAt <= now)
                {
                    timeoutRequests.Add(kvp.Key);
                }
            }

            foreach (var requestId in timeoutRequests)
            {
                if (_pendingRequests.TryRemove(requestId, out var pendingRequest))
                {
                    pendingRequest.TaskCompletionSource.SetException(
                        new TimeoutException($"Request {requestId} timed out"));
                }
            }

            if (timeoutRequests.Count > 0)
            {
                Logger.Instance.Debug($"Cleaned up {timeoutRequests.Count} timeout requests");
            }
        }

        public void Dispose()
        {
            if (_isDisposed)
                return;

            _isDisposed = true;

            try
            {
                _timeoutTimer?.Dispose();

                // Cancel tất cả pending requests
                foreach (var kvp in _pendingRequests)
                {
                    kvp.Value.TaskCompletionSource.SetCanceled();
                }

                _pendingRequests.Clear();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing RequestResponseService: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Pending request tracking
    /// </summary>
    internal class PendingRequest
    {
        public object TaskCompletionSource { get; }
        public Type ResponseType { get; }
        public DateTime TimeoutAt { get; }

        public PendingRequest(object taskCompletionSource, Type responseType, DateTime timeoutAt)
        {
            TaskCompletionSource = taskCompletionSource;
            ResponseType = responseType;
            TimeoutAt = timeoutAt;
        }
    }

    /// <summary>
    /// Request message wrapper
    /// </summary>
    public class RequestMessage<T> : MessageBase where T : class
    {
        public T Data { get; }
        public string RequestId { get; }

        public RequestMessage(T data, string requestId)
        {
            Data = data;
            RequestId = requestId;
        }
    }

    /// <summary>
    /// Response message wrapper
    /// </summary>
    public class ResponseMessage<T> : MessageBase where T : class
    {
        public T Data { get; }
        public string RequestId { get; }

        public ResponseMessage(T data, string requestId)
        {
            Data = data;
            RequestId = requestId;
        }
    }

    /// <summary>
    /// Error response message
    /// </summary>
    public class ErrorResponseMessage : MessageBase
    {
        public Exception Exception { get; }
        public string RequestId { get; }

        public ErrorResponseMessage(Exception exception, string requestId)
        {
            Exception = exception;
            RequestId = requestId;
        }
    }

    /// <summary>
    /// Helper để tạo response từ request
    /// </summary>
    public static class ResponseHelper
    {
        /// <summary>
        /// Tạo response từ request
        /// </summary>
        public static ResponseMessage<TResponse> CreateResponse<TRequest, TResponse>(
            RequestMessage<TRequest> request, 
            TResponse responseData)
            where TRequest : class
            where TResponse : class
        {
            return new ResponseMessage<TResponse>(responseData, request.RequestId);
        }

        /// <summary>
        /// Tạo error response từ request
        /// </summary>
        public static ErrorResponseMessage CreateErrorResponse<TRequest>(
            RequestMessage<TRequest> request, 
            Exception exception)
            where TRequest : class
        {
            return new ErrorResponseMessage(exception, request.RequestId);
        }

        /// <summary>
        /// Reply với response (helper cho message handlers)
        /// </summary>
        public static async Task ReplyAsync<TRequest, TResponse>(
            IMessageContext context,
            RequestMessage<TRequest> request,
            TResponse responseData)
            where TRequest : class
            where TResponse : class
        {
            var response = CreateResponse(request, responseData);
            await context.ReplyAsync(response);
        }

        /// <summary>
        /// Reply với error (helper cho message handlers)
        /// </summary>
        public static async Task ReplyErrorAsync<TRequest>(
            IMessageContext context,
            RequestMessage<TRequest> request,
            Exception exception)
            where TRequest : class
        {
            var errorResponse = CreateErrorResponse(request, exception);
            await context.ReplyAsync(errorResponse);
        }
    }
}
