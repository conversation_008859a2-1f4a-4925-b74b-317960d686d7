using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using HeroYulgang.Core.Native.Packets;

namespace HeroYulgang.Tests.Core.Native.Packets
{
    /// <summary>
    /// Tests for PacketProcessor implementation
    /// </summary>
    [TestFixture]
    public class PacketProcessorTests : TestBase
    {
        private IPacketProcessor _packetProcessor = null!;
        private TestPacketHandler _testHandler = null!;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            _packetProcessor = ServiceProvider.GetRequiredService<IPacketProcessor>();
            _testHandler = new TestPacketHandler();
        }

        [Test]
        public async Task ProcessAsync_Should_Handle_Packet_Successfully()
        {
            // Arrange
            _packetProcessor.RegisterHandler<Packet>(_testHandler.HandleAsync);
            
            var packet = CreateTestPacket(PacketType.Login, 1);
            var session = await CreateTestSessionAsync(1);
            var context = session.PacketContext;

            // Act
            await _packetProcessor.ProcessAsync(packet, context);

            // Assert
            await AssertTaskCompletesAsync(_testHandler.HandlerCalledTcs.Task, TimeSpan.FromSeconds(1));
            Assert.AreEqual(1, _testHandler.HandledPackets.Count);
            Assert.AreEqual(PacketType.Login, _testHandler.HandledPackets[0].Packet.Type);
            Assert.AreEqual(1, _testHandler.HandledPackets[0].Packet.SessionId);
        }

        [Test]
        public async Task ProcessAsync_With_Priority_Should_Process_In_Order()
        {
            // Arrange
            var processedPackets = new List<(PacketType Type, PacketPriority Priority)>();
            var allProcessed = new TaskCompletionSource<bool>();
            var processedCount = 0;

            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                var priority = packet.Type switch
                {
                    PacketType.Login => PacketPriority.Critical,
                    PacketType.Chat => PacketPriority.Normal,
                    PacketType.Movement => PacketPriority.Low,
                    _ => PacketPriority.Normal
                };

                processedPackets.Add((packet.Type, priority));
                
                Interlocked.Increment(ref processedCount);
                if (processedCount >= 3)
                {
                    allProcessed.TrySetResult(true);
                }
            });

            var session = await CreateTestSessionAsync(1);
            var context = session.PacketContext;

            // Act - Submit packets in reverse priority order
            await _packetProcessor.ProcessAsync(CreateTestPacket(PacketType.Movement, 1), context, PacketPriority.Low);
            await _packetProcessor.ProcessAsync(CreateTestPacket(PacketType.Chat, 1), context, PacketPriority.Normal);
            await _packetProcessor.ProcessAsync(CreateTestPacket(PacketType.Login, 1), context, PacketPriority.Critical);

            // Assert
            await AssertTaskCompletesAsync(allProcessed.Task, TimeSpan.FromSeconds(2));
            Assert.AreEqual(3, processedPackets.Count);
            
            // Critical should be processed first
            Assert.AreEqual(PacketType.Login, processedPackets[0].Type);
            Assert.AreEqual(PacketPriority.Critical, processedPackets[0].Priority);
        }

        [Test]
        public async Task RegisterHandler_Should_Allow_Multiple_Handlers()
        {
            // Arrange
            var handler1Called = new TaskCompletionSource<bool>();
            var handler2Called = new TaskCompletionSource<bool>();

            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                if (packet.Type == PacketType.Login)
                {
                    handler1Called.TrySetResult(true);
                }
            });

            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                if (packet.Type == PacketType.Login)
                {
                    handler2Called.TrySetResult(true);
                }
            });

            var packet = CreateTestPacket(PacketType.Login, 1);
            var session = await CreateTestSessionAsync(1);

            // Act
            await _packetProcessor.ProcessAsync(packet, session.PacketContext);

            // Assert
            await AssertTaskCompletesAsync(handler1Called.Task, TimeSpan.FromSeconds(1));
            await AssertTaskCompletesAsync(handler2Called.Task, TimeSpan.FromSeconds(1));
        }

        [Test]
        public async Task ProcessAsync_Should_Handle_Handler_Exception_Gracefully()
        {
            // Arrange
            var exceptionThrown = false;
            var goodHandlerCalled = new TaskCompletionSource<bool>();

            // Handler that throws exception
            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                exceptionThrown = true;
                throw new InvalidOperationException("Test exception");
            });

            // Handler that should still work
            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                goodHandlerCalled.TrySetResult(true);
            });

            var packet = CreateTestPacket(PacketType.Login, 1);
            var session = await CreateTestSessionAsync(1);

            // Act
            await _packetProcessor.ProcessAsync(packet, session.PacketContext);

            // Assert
            await AssertTaskCompletesAsync(goodHandlerCalled.Task, TimeSpan.FromSeconds(1));
            Assert.IsTrue(exceptionThrown);
        }

        [Test]
        public async Task High_Volume_Processing_Should_Maintain_Performance()
        {
            // Arrange
            const int packetCount = 1000;
            var processedCount = 0;
            var allProcessed = new TaskCompletionSource<bool>();

            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                var count = Interlocked.Increment(ref processedCount);
                if (count >= packetCount)
                {
                    allProcessed.TrySetResult(true);
                }
            });

            var session = await CreateTestSessionAsync(1);
            var startTime = DateTime.UtcNow;

            // Act
            var processingTasks = new List<Task>();
            for (int i = 0; i < packetCount; i++)
            {
                var packet = CreateTestPacket(PacketType.Chat, 1);
                processingTasks.Add(_packetProcessor.ProcessAsync(packet, session.PacketContext));
            }

            await Task.WhenAll(processingTasks);

            // Assert
            await AssertTaskCompletesAsync(allProcessed.Task, TimeSpan.FromSeconds(10));
            
            var duration = DateTime.UtcNow - startTime;
            var packetsPerSecond = packetCount / duration.TotalSeconds;
            
            Assert.AreEqual(packetCount, processedCount);
            Assert.Greater(packetsPerSecond, 500, "Should process at least 500 packets per second");
            
            Console.WriteLine($"Processed {packetCount} packets in {duration.TotalMilliseconds:F2}ms ({packetsPerSecond:F0} packets/sec)");
        }

        [Test]
        public async Task GetStatistics_Should_Return_Accurate_Metrics()
        {
            // Arrange
            var processedCount = 0;
            var allProcessed = new TaskCompletionSource<bool>();

            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                await Task.Delay(10); // Simulate processing time
                var count = Interlocked.Increment(ref processedCount);
                if (count >= 5)
                {
                    allProcessed.TrySetResult(true);
                }
            });

            var session = await CreateTestSessionAsync(1);

            // Act
            for (int i = 0; i < 5; i++)
            {
                var packet = CreateTestPacket(PacketType.Chat, 1);
                await _packetProcessor.ProcessAsync(packet, session.PacketContext);
            }

            await AssertTaskCompletesAsync(allProcessed.Task, TimeSpan.FromSeconds(2));

            // Assert
            var stats = _packetProcessor.GetStatistics();
            Assert.GreaterOrEqual(stats.TotalPacketsProcessed, 5);
            Assert.GreaterOrEqual(stats.PacketsPerSecond, 0);
            Assert.GreaterOrEqual(stats.AverageProcessingTime.TotalMilliseconds, 0);
        }

        [Test]
        public async Task Concurrent_Processing_Should_Be_Thread_Safe()
        {
            // Arrange
            const int concurrentSessions = 10;
            const int packetsPerSession = 50;
            var totalExpected = concurrentSessions * packetsPerSession;
            
            var processedCount = 0;
            var allProcessed = new TaskCompletionSource<bool>();
            var processedPackets = new List<(int SessionId, PacketType Type)>();
            var lockObject = new object();

            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                lock (lockObject)
                {
                    processedPackets.Add((packet.SessionId, packet.Type));
                    var count = Interlocked.Increment(ref processedCount);
                    if (count >= totalExpected)
                    {
                        allProcessed.TrySetResult(true);
                    }
                }
            });

            // Act - Process packets from multiple sessions concurrently
            var processingTasks = new List<Task>();
            
            for (int sessionId = 1; sessionId <= concurrentSessions; sessionId++)
            {
                var currentSessionId = sessionId;
                processingTasks.Add(Task.Run(async () =>
                {
                    var session = await CreateTestSessionAsync(currentSessionId);
                    
                    for (int i = 0; i < packetsPerSession; i++)
                    {
                        var packet = CreateTestPacket(PacketType.Chat, currentSessionId);
                        await _packetProcessor.ProcessAsync(packet, session.PacketContext);
                    }
                }));
            }

            await Task.WhenAll(processingTasks);

            // Assert
            await AssertTaskCompletesAsync(allProcessed.Task, TimeSpan.FromSeconds(10));
            Assert.AreEqual(totalExpected, processedCount);
            
            // Verify all sessions were processed
            var sessionIds = processedPackets.Select(p => p.SessionId).Distinct().OrderBy(id => id).ToArray();
            var expectedSessionIds = Enumerable.Range(1, concurrentSessions).ToArray();
            CollectionAssert.AreEqual(expectedSessionIds, sessionIds);
            
            // Verify packet count per session
            foreach (var sessionId in sessionIds)
            {
                var sessionPacketCount = processedPackets.Count(p => p.SessionId == sessionId);
                Assert.AreEqual(packetsPerSession, sessionPacketCount, $"Session {sessionId} should have {packetsPerSession} packets");
            }
        }

        [Test]
        public async Task Stop_Should_Complete_Processing_And_Reject_New_Packets()
        {
            // Arrange
            var processingStarted = new TaskCompletionSource<bool>();
            var canComplete = new TaskCompletionSource<bool>();

            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                processingStarted.TrySetResult(true);
                await canComplete.Task; // Wait for signal to complete
            });

            var session = await CreateTestSessionAsync(1);
            var packet = CreateTestPacket(PacketType.Login, 1);

            // Start processing a packet
            var processingTask = _packetProcessor.ProcessAsync(packet, session.PacketContext);
            await AssertTaskCompletesAsync(processingStarted.Task, TimeSpan.FromSeconds(1));

            // Act - Stop the processor
            var stopTask = _packetProcessor.StopAsync(CancellationToken.None);

            // Allow the processing to complete
            canComplete.SetResult(true);

            // Assert
            await AssertTaskCompletesAsync(processingTask, TimeSpan.FromSeconds(1));
            await AssertTaskCompletesAsync(stopTask, TimeSpan.FromSeconds(2));

            // New packets should be rejected or handled gracefully
            var newPacket = CreateTestPacket(PacketType.Chat, 1);
            
            // This should either throw or complete quickly without processing
            try
            {
                await _packetProcessor.ProcessAsync(newPacket, session.PacketContext);
            }
            catch (InvalidOperationException)
            {
                // Expected - processor is stopped
            }
        }
    }
}
