using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using HeroYulgang.Core.Native.Services;
using HeroYulgang.Core.Native.Messaging;
using HeroYulgang.Core.Native.Network;
using HeroYulgang.Core.Native.Packets;
using HeroYulgang.Core.Native.Sessions;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Integration
{
    /// <summary>
    /// Native system bootstrap - thay thế Akka.NET ActorSystem
    /// </summary>
    public class NativeSystemBootstrap : IHostedService, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IServiceManager _serviceManager;
        private readonly INetworkManager _networkManager;
        private readonly IPacketProcessor _packetProcessor;
        private readonly ISessionManager _sessionManager;
        private readonly IAuthenticationService _authenticationService;
        private readonly IEventBus _eventBus;
        private volatile bool _isStarted;

        public NativeSystemBootstrap(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            
            // Get core services
            _serviceManager = serviceProvider.GetRequiredService<IServiceManager>();
            _networkManager = serviceProvider.GetRequiredService<INetworkManager>();
            _packetProcessor = serviceProvider.GetRequiredService<IPacketProcessor>();
            _sessionManager = serviceProvider.GetRequiredService<ISessionManager>();
            _authenticationService = serviceProvider.GetRequiredService<IAuthenticationService>();
            _eventBus = serviceProvider.GetRequiredService<IEventBus>();
            
            Logger.Instance.Info("NativeSystemBootstrap created");
        }

        /// <summary>
        /// Start native system
        /// </summary>
        public async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                Logger.Instance.Info("Starting Native System...");

                // 1. Start core messaging system
                await _serviceManager.StartAsync(cancellationToken);
                Logger.Instance.Info("✓ Service Manager started");

                // 2. Start authentication service
                await _authenticationService.StartAsync(cancellationToken);
                Logger.Instance.Info("✓ Authentication Service started");

                // 3. Start session manager
                await _sessionManager.StartAsync(cancellationToken);
                Logger.Instance.Info("✓ Session Manager started");

                // 4. Start packet processor
                await _packetProcessor.StartAsync(cancellationToken);
                Logger.Instance.Info("✓ Packet Processor started");

                // 5. Setup network event handlers
                SetupNetworkEventHandlers();
                Logger.Instance.Info("✓ Network event handlers configured");

                // 6. Start network manager (this should be last)
                await _networkManager.StartAsync(cancellationToken);
                Logger.Instance.Info("✓ Network Manager started");

                // 7. Register default packet handlers
                RegisterDefaultPacketHandlers();
                Logger.Instance.Info("✓ Default packet handlers registered");

                _isStarted = true;
                Logger.Instance.Info("🚀 Native System started successfully!");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Failed to start Native System: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Stop native system
        /// </summary>
        public async Task StopAsync(CancellationToken cancellationToken)
        {
            try
            {
                Logger.Instance.Info("Stopping Native System...");

                if (!_isStarted)
                {
                    Logger.Instance.Info("Native System was not started");
                    return;
                }

                // Stop in reverse order
                await _networkManager.StopAsync(cancellationToken);
                Logger.Instance.Info("✓ Network Manager stopped");

                await _packetProcessor.StopAsync(cancellationToken);
                Logger.Instance.Info("✓ Packet Processor stopped");

                await _sessionManager.StopAsync(cancellationToken);
                Logger.Instance.Info("✓ Session Manager stopped");

                await _authenticationService.StopAsync(cancellationToken);
                Logger.Instance.Info("✓ Authentication Service stopped");

                await _serviceManager.StopAsync(cancellationToken);
                Logger.Instance.Info("✓ Service Manager stopped");

                _isStarted = false;
                Logger.Instance.Info("🛑 Native System stopped successfully!");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error stopping Native System: {ex.Message}");
            }
        }

        /// <summary>
        /// Setup network event handlers
        /// </summary>
        private void SetupNetworkEventHandlers()
        {
            // Handle client connections
            _eventBus.Subscribe<ClientConnectedEvent>(async (eventData) =>
            {
                try
                {
                    Logger.Instance.Info($"Client connected: {eventData.SessionId} from {eventData.RemoteEndPoint}");
                    
                    // Create player session
                    var session = await _sessionManager.CreatePlayerSessionAsync(eventData.Session);
                    
                    Logger.Instance.Debug($"Created player session {session.SessionId}");
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"Error handling client connection: {ex.Message}");
                }
            });

            // Handle client disconnections
            _eventBus.Subscribe<ClientDisconnectedEvent>(async (eventData) =>
            {
                try
                {
                    Logger.Instance.Info($"Client disconnected: {eventData.SessionId}, reason: {eventData.Reason}");
                    
                    // Close player session
                    await _sessionManager.ClosePlayerSessionAsync(eventData.SessionId, eventData.Reason);
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"Error handling client disconnection: {ex.Message}");
                }
            });

            // Handle incoming data
            _eventBus.Subscribe<DataReceivedEvent>(async (eventData) =>
            {
                try
                {
                    // Parse packet
                    var packet = Packet.Parse(eventData.Data, eventData.Length, eventData.SessionId);
                    
                    // Get session
                    var session = await _sessionManager.GetPlayerSessionAsync(eventData.SessionId);
                    if (session == null)
                    {
                        Logger.Instance.Warning($"No session found for packet from {eventData.SessionId}");
                        return;
                    }

                    // Update activity
                    if (session is PlayerSession playerSession)
                    {
                        playerSession.UpdateActivity();
                    }

                    // Process packet
                    await _packetProcessor.ProcessAsync(packet, session.PacketContext);
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"Error handling received data: {ex.Message}");
                }
            });

            // Handle network errors
            _eventBus.Subscribe<NetworkErrorEvent>(async (eventData) =>
            {
                Logger.Instance.Error($"Network error on session {eventData.SessionId}: {eventData.Exception.Message}");
                
                // Close session on network error
                await _sessionManager.ClosePlayerSessionAsync(eventData.SessionId, "Network error");
            });
        }

        /// <summary>
        /// Register default packet handlers
        /// </summary>
        private void RegisterDefaultPacketHandlers()
        {
            // Login packet handler
            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                if (packet.Type == PacketType.Login)
                {
                    await HandleLoginPacket(packet, context);
                }
            });

            // Valid1375 packet handler
            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                if (packet.Type == PacketType.Valid1375)
                {
                    await HandleValid1375Packet(packet, context);
                }
            });

            // Heartbeat packet handler
            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                if (packet.Type == PacketType.Heartbeat)
                {
                    await HandleHeartbeatPacket(packet, context);
                }
            });

            // Disconnect packet handler
            _packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                if (packet.Type == PacketType.Disconnect)
                {
                    await HandleDisconnectPacket(packet, context);
                }
            });
        }

        /// <summary>
        /// Handle login packet
        /// </summary>
        private async Task HandleLoginPacket(IPacket packet, IPacketContext context)
        {
            try
            {
                Logger.Instance.Debug($"Handling login packet from session {packet.SessionId}");

                // TODO: Parse login data from packet
                // For now, use placeholder data
                var username = "testuser";
                var password = "testpass";

                // Authenticate
                var authResult = await _authenticationService.AuthenticateAsync(username, password, packet.SessionId);
                
                if (authResult.Success)
                {
                    // Authenticate session
                    var success = await _sessionManager.AuthenticateSessionAsync(
                        packet.SessionId, 
                        authResult.AccountId, 
                        authResult.CharacterName ?? "Unknown");

                    if (success && authResult.PlayerData != null)
                    {
                        await _sessionManager.SetPlayerAsync(packet.SessionId, authResult.PlayerData);
                    }

                    // Send success response
                    var response = CreateLoginResponse(true, authResult.SessionToken);
                    await context.SendAsync(response);
                    
                    Logger.Instance.Info($"Login successful for session {packet.SessionId}");
                }
                else
                {
                    // Send failure response
                    var response = CreateLoginResponse(false, null, authResult.ErrorMessage);
                    await context.SendAsync(response);
                    
                    Logger.Instance.Warning($"Login failed for session {packet.SessionId}: {authResult.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error handling login packet: {ex.Message}");
                
                var response = CreateLoginResponse(false, null, "Server error");
                await context.SendAsync(response);
            }
        }

        /// <summary>
        /// Handle Valid1375 packet
        /// </summary>
        private async Task HandleValid1375Packet(IPacket packet, IPacketContext context)
        {
            try
            {
                Logger.Instance.Debug($"Handling Valid1375 packet from session {packet.SessionId}");
                
                // Send acknowledgment
                var response = new byte[] { 0x57, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00 };
                await context.SendAsync(response);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error handling Valid1375 packet: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle heartbeat packet
        /// </summary>
        private async Task HandleHeartbeatPacket(IPacket packet, IPacketContext context)
        {
            try
            {
                // Simply echo back the heartbeat
                await context.SendAsync(packet.Data);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error handling heartbeat packet: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle disconnect packet
        /// </summary>
        private async Task HandleDisconnectPacket(IPacket packet, IPacketContext context)
        {
            try
            {
                Logger.Instance.Info($"Client requested disconnect for session {packet.SessionId}");
                await _sessionManager.ClosePlayerSessionAsync(packet.SessionId, "Client requested disconnect");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error handling disconnect packet: {ex.Message}");
            }
        }

        /// <summary>
        /// Create login response packet
        /// </summary>
        private byte[] CreateLoginResponse(bool success, string? token = null, string? errorMessage = null)
        {
            // TODO: Implement actual packet structure
            // This is a placeholder implementation
            if (success)
            {
                return new byte[] { 0x01, 0x00, 0x00, 0x00, 0x01 }; // Success
            }
            else
            {
                return new byte[] { 0x01, 0x00, 0x00, 0x00, 0x00 }; // Failure
            }
        }

        public void Dispose()
        {
            try
            {
                if (_isStarted)
                {
                    // Don't await here to avoid blocking
                    _ = Task.Run(async () => await StopAsync(CancellationToken.None));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing NativeSystemBootstrap: {ex.Message}");
            }
        }
    }
}
