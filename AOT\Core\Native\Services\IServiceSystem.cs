using System;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Messaging;

namespace HeroYulgang.Core.Native.Services
{
    /// <summary>
    /// Service states - thay thế Actor lifecycle
    /// </summary>
    public enum ServiceState
    {
        NotStarted,
        Starting,
        Running,
        Stopping,
        Stopped,
        Failed,
        Restarting
    }

    /// <summary>
    /// Base service interface - thay thế Actor
    /// </summary>
    public interface IService
    {
        /// <summary>
        /// Service unique identifier
        /// </summary>
        string ServiceId { get; }
        
        /// <summary>
        /// Current service state
        /// </summary>
        ServiceState State { get; }
        
        /// <summary>
        /// Start service
        /// </summary>
        Task StartAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Stop service gracefully
        /// </summary>
        Task StopAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Health check
        /// </summary>
        Task<bool> IsHealthyAsync();
        
        /// <summary>
        /// Service configuration
        /// </summary>
        object? Configuration { get; set; }
    }

    /// <summary>
    /// Message-enabled service - có thể nhận và xử lý messages
    /// </summary>
    public interface IMessageService : IService
    {
        /// <summary>
        /// Message target cho service này
        /// </summary>
        IMessageTarget MessageTarget { get; }
    }

    /// <summary>
    /// Service manager interface - thay thế ActorSystem
    /// </summary>
    public interface IServiceManager
    {
        /// <summary>
        /// Get service instance
        /// </summary>
        Task<T> GetServiceAsync<T>() where T : class, IService;
        
        /// <summary>
        /// Start service
        /// </summary>
        Task StartServiceAsync<T>() where T : class, IService;
        
        /// <summary>
        /// Stop service
        /// </summary>
        Task StopServiceAsync<T>() where T : class, IService;
        
        /// <summary>
        /// Restart service
        /// </summary>
        Task RestartServiceAsync<T>() where T : class, IService;
        
        /// <summary>
        /// Register service type
        /// </summary>
        void RegisterService<T>(Func<IServiceProvider, T> factory) where T : class, IService;
        
        /// <summary>
        /// Register service instance
        /// </summary>
        void RegisterService<T>(T instance) where T : class, IService;
        
        /// <summary>
        /// Get all services of type
        /// </summary>
        Task<T[]> GetServicesAsync<T>() where T : class, IService;
        
        /// <summary>
        /// Check if service is registered
        /// </summary>
        bool IsServiceRegistered<T>() where T : class, IService;
        
        /// <summary>
        /// Get service state
        /// </summary>
        ServiceState GetServiceState<T>() where T : class, IService;
    }

    /// <summary>
    /// Service supervisor interface - thay thế Actor supervision
    /// </summary>
    public interface IServiceSupervisor
    {
        /// <summary>
        /// Monitor service health
        /// </summary>
        Task MonitorServiceAsync<T>(T service) where T : IService;
        
        /// <summary>
        /// Set restart policy for service type
        /// </summary>
        void SetRestartPolicy<T>(RestartPolicy policy) where T : IService;
        
        /// <summary>
        /// Handle service failure
        /// </summary>
        Task HandleServiceFailureAsync<T>(T service, Exception exception) where T : IService;
        
        /// <summary>
        /// Get service health status
        /// </summary>
        Task<ServiceHealthStatus> GetServiceHealthAsync<T>() where T : IService;
    }

    /// <summary>
    /// Restart policies - thay thế Actor supervision strategies
    /// </summary>
    public enum RestartPolicy
    {
        /// <summary>
        /// Never restart on failure
        /// </summary>
        Never,
        
        /// <summary>
        /// Restart only on failure
        /// </summary>
        OnFailure,
        
        /// <summary>
        /// Always restart
        /// </summary>
        Always,
        
        /// <summary>
        /// Exponential backoff restart
        /// </summary>
        Exponential,
        
        /// <summary>
        /// Custom restart logic
        /// </summary>
        Custom
    }

    /// <summary>
    /// Service health status
    /// </summary>
    public class ServiceHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime LastCheckTime { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public int FailureCount { get; set; }
        public DateTime? LastFailureTime { get; set; }
    }

    /// <summary>
    /// Service configuration interface
    /// </summary>
    public interface IServiceConfiguration
    {
        /// <summary>
        /// Service name
        /// </summary>
        string ServiceName { get; set; }
        
        /// <summary>
        /// Restart policy
        /// </summary>
        RestartPolicy RestartPolicy { get; set; }
        
        /// <summary>
        /// Health check interval
        /// </summary>
        TimeSpan HealthCheckInterval { get; set; }
        
        /// <summary>
        /// Startup timeout
        /// </summary>
        TimeSpan StartupTimeout { get; set; }
        
        /// <summary>
        /// Shutdown timeout
        /// </summary>
        TimeSpan ShutdownTimeout { get; set; }
        
        /// <summary>
        /// Max restart attempts
        /// </summary>
        int MaxRestartAttempts { get; set; }
        
        /// <summary>
        /// Restart delay
        /// </summary>
        TimeSpan RestartDelay { get; set; }
    }

    /// <summary>
    /// Default service configuration
    /// </summary>
    public class ServiceConfiguration : IServiceConfiguration
    {
        public string ServiceName { get; set; } = string.Empty;
        public RestartPolicy RestartPolicy { get; set; } = RestartPolicy.OnFailure;
        public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(1);
        public TimeSpan StartupTimeout { get; set; } = TimeSpan.FromSeconds(30);
        public TimeSpan ShutdownTimeout { get; set; } = TimeSpan.FromSeconds(30);
        public int MaxRestartAttempts { get; set; } = 3;
        public TimeSpan RestartDelay { get; set; } = TimeSpan.FromSeconds(5);
    }

    /// <summary>
    /// Service events
    /// </summary>
    public abstract class ServiceEvent : MessageBase
    {
        public string ServiceId { get; }
        public ServiceState State { get; }
        
        protected ServiceEvent(string serviceId, ServiceState state)
        {
            ServiceId = serviceId;
            State = state;
        }
    }

    public class ServiceStartedEvent : ServiceEvent
    {
        public ServiceStartedEvent(string serviceId) : base(serviceId, ServiceState.Running) { }
    }

    public class ServiceStoppedEvent : ServiceEvent
    {
        public ServiceStoppedEvent(string serviceId) : base(serviceId, ServiceState.Stopped) { }
    }

    public class ServiceFailedEvent : ServiceEvent
    {
        public Exception Exception { get; }
        
        public ServiceFailedEvent(string serviceId, Exception exception) : base(serviceId, ServiceState.Failed)
        {
            Exception = exception;
        }
    }

    public class ServiceRestartingEvent : ServiceEvent
    {
        public int AttemptNumber { get; }
        
        public ServiceRestartingEvent(string serviceId, int attemptNumber) : base(serviceId, ServiceState.Restarting)
        {
            AttemptNumber = attemptNumber;
        }
    }

    /// <summary>
    /// Service dependency interface
    /// </summary>
    public interface IServiceDependency
    {
        /// <summary>
        /// Services that this service depends on
        /// </summary>
        Type[] Dependencies { get; }
        
        /// <summary>
        /// Check if dependencies are satisfied
        /// </summary>
        Task<bool> AreDependenciesSatisfiedAsync(IServiceManager serviceManager);
    }

    /// <summary>
    /// Service factory interface
    /// </summary>
    public interface IServiceFactory<T> where T : class, IService
    {
        /// <summary>
        /// Create service instance
        /// </summary>
        T CreateService(IServiceProvider serviceProvider);
        
        /// <summary>
        /// Service configuration
        /// </summary>
        IServiceConfiguration Configuration { get; }
    }

    /// <summary>
    /// Service registry interface
    /// </summary>
    public interface IServiceRegistry
    {
        /// <summary>
        /// Register service factory
        /// </summary>
        void Register<T>(IServiceFactory<T> factory) where T : class, IService;
        
        /// <summary>
        /// Register service instance
        /// </summary>
        void Register<T>(T instance) where T : class, IService;
        
        /// <summary>
        /// Get service factory
        /// </summary>
        IServiceFactory<T>? GetFactory<T>() where T : class, IService;
        
        /// <summary>
        /// Check if service is registered
        /// </summary>
        bool IsRegistered<T>() where T : class, IService;
        
        /// <summary>
        /// Get all registered service types
        /// </summary>
        Type[] GetRegisteredServiceTypes();
    }
}
