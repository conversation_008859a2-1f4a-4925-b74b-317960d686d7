namespace RxjhServer;

public class X_Mon_Phai_Xep_Hang
{
	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	private int int_4;

	private int int_5;

	private string string_0;

	private string string_1;

	private string string_2;

	public string BangPhaiBangPhaiTen
	{
		get
		{
			return string_2;
		}
		set
		{
			string_2 = value;
		}
	}

	public int BangPhaiDangCap
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public string BangPhaiPhanKhuID
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	public int BangPhaiNhanVat_DangCap
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public string BangPhaiTenNhanVat
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public int BangPhaiVinhDu_DiemSo
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int BangPhaiChinhTa
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int BangPhaiNgheNghiep
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int BangPhaiChuyenChuc
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}
}
