#define TRACE
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;

namespace RxjhServer;

public class WUserManager<TUser> where TUser : IWUser<string>, new()
{
	private static volatile bool _initialized;

	private static WUserManager<TUser> _userManager;

	private Dictionary<int, TUser> _users;

	private int _maxUsers;

	public static void Init(int maxUsers)
	{
		if (_initialized)
		{
			throw new ApplicationException("alread inited");
		}
		_userManager = new();
		_userManager._maxUsers = maxUsers;
		_userManager._users = new(maxUsers);
		for (var i = 0; i < maxUsers; i++)
		{
			var users = _userManager._users;
			var key = i;
			users[key] = new()
			{
				Index = i
			};
		}
		Thread thread = new(threadProce);
		thread.Name = "user manager clearner";
		thread.IsBackground = true;
		thread.Priority = ThreadPriority.Highest;
		thread.Start();
		_initialized = true;
	}

	private static void threadProce(object State)
	{
		try
		{
			while (true)
			{
				foreach (var value in _userManager._users.Values)
				{
					var val = value;
					using (val.ReadLock)
					{
						if (val.Invalid)
						{
							continue;
						}
						using (val.UpdateLock)
						{
							if (DateTime.Now - val.Timestamp > TimeSpan.FromMinutes(3.0))
							{
								using (val.WriteLock)
								{
									val.Invalid = true;
								}
							}
						}
					}
				}
				Thread.Sleep(TimeSpan.FromMinutes(1.0));
			}
		}
		catch (Exception arg)
		{
			Trace.TraceError(string.Format("user manager clear error:", arg));
		}
	}

	public static WUserManager<TUser> GetInstance()
	{
		if (!_initialized)
		{
			throw new ApplicationException("no init");
		}
		return _userManager;
	}

	public TUser GetUser(int index, string credentials)
	{
		TUser val;
		if (index >= 0 && index < _maxUsers)
		{
			if (string.IsNullOrEmpty(credentials))
			{
				val = default(TUser);
				return val;
			}
			var result = _users[index];
			using (result.UpdateLock)
			{
				if (!result.Authentication(credentials))
				{
					throw new ApplicationException("Authentication failed");
				}
				if (!result.Invalid)
				{
					using (result.WriteLock)
					{
						ref var reference = ref result;
						val = default(TUser);
						if (val == null)
						{
							val = reference;
							reference = ref val;
						}
						reference.Timestamp = DateTime.Now;
						return result;
					}
				}
			}
			val = default(TUser);
			return val;
		}
		val = default(TUser);
		return val;
	}

	public TUser AddUser()
	{
		TUser val;
		for (var i = 0; i < _maxUsers; i++)
		{
			var result = _users[i];
			using (result.UpdateLock)
			{
				if (!result.Invalid)
				{
					continue;
				}
				using (result.WriteLock)
				{
					result.Reset();
					result.Invalid = false;
					ref var reference = ref result;
					val = default(TUser);
					if (val == null)
					{
						val = reference;
						reference = ref val;
					}
					reference.Timestamp = DateTime.Now;
					return result;
				}
			}
		}
		val = default(TUser);
		return val;
	}

	public bool RemoveUser(int index, string credentials)
	{
		if (index >= 0 && index < _maxUsers && !string.IsNullOrEmpty(credentials))
		{
			var val = _users[index];
			if (val == null)
			{
				return false;
			}
			using (val.WriteLock)
			{
				val.Invalid = true;
			}
			return true;
		}
		return false;
	}
}
