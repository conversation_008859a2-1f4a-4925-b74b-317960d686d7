using Microsoft.Extensions.Configuration;
using System;
using System.IO;

namespace RxjhServer.Database.Configuration
{
    public class ConfigurationManager
    {
        private static IConfiguration _configuration;

        static ConfigurationManager()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            _configuration = builder.Build();
        }

        public static string GetConnectionString(string name)
        {
            return _configuration.GetConnectionString(name);
        }

        public static string GetSetting(string key)
        {
            return _configuration[key];
        }

        public static string GetSetting(string section, string key)
        {
            return _configuration.GetSection(section)[key];
        }

        public static int GetSettingInt(string section, string key, int defaultValue = 0)
        {
            var value = _configuration.GetSection(section)[key];
            if (int.TryParse(value, out int result))
            {
                return result;
            }
            return defaultValue;
        }

        public static bool GetSettingBool(string section, string key, bool defaultValue = false)
        {
            var value = _configuration.GetSection(section)[key];
            if (bool.TryParse(value, out bool result))
            {
                return result;
            }
            return defaultValue;
        }
    }
}
