﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class vinhdubangphaixephang {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_zx { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_bp { get; set; }

		[JsonProperty]
		public int? fld_job { get; set; }

		[JsonProperty]
		public int? fld_job_level { get; set; }

		[JsonProperty]
		public int? fld_ry { get; set; }

		[JsonProperty]
		public DateTime? thoigian { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_fq { get; set; }

	}

}
