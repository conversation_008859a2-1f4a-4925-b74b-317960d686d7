using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer
{
    public partial class Players
    {

        /// <summary>
        /// Kiểm tra xem một người chơi khác có ở trong phạm vi và có thể nhìn thấy theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <param name="player">Người chơi cần kiểm tra</param>
        /// <returns>true nếu người chơi trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public bool FindPlayers(int far, Players player)
        {
            try
            {
                // Kiểm tra cơ bản về bản đồ và khoảng cách
                if (!FindPlayerByRange(far, player))
                {
                    return false;
                }
                return true;
                //// Nếu một trong hai người chơi không có zone, mặc định cho phép nhìn thấy
                //if (this.CurrentZone == null || player.CurrentZone == null)
                //{
                    
                //    return true;
                //}
                //// Kiểm tra theo quy tắc zone
                //return this.CurrentZone.CanSeeZone(player.CurrentZone);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"FindPlayersZone error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Kiểm tra xem một NPC có ở trong phạm vi và có thể nhìn thấy theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <param name="npc">NPC cần kiểm tra</param>
        /// <returns>true nếu NPC trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public bool FindNpcZone(int far, NpcClass npc)
        {
            try
            {
                // Kiểm tra cơ bản về bản đồ
                if (npc.Rxjh_Map != this.MapID)
                {
                    return false;
                }
                
                // Kiểm tra khoảng cách
                var num = npc.Rxjh_X - this.PosX;
                var num2 = npc.Rxjh_Y - this.PosY;
                bool isInRange = (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far;
                
                if (!isInRange)
                {
                    return false;
                }
                return true;
                
                // Nếu người chơi không có zone, mặc định cho phép nhìn thấy
                //if (this.CurrentZone == null)
                //{
                //    return true;
                //}
                
                //// Tìm zone chứa NPC
                //Zone npcZone = null;
                //var allZones = ZoneManager.Instance.GetAllZones();
                //foreach (var zone in allZones)
                //{
                //    if (zone.NPCs.Contains(npc))
                //    {
                //        npcZone = zone;
                //        break;
                //    }
                //}
                
                //// Nếu NPC không thuộc zone nào, mặc định cho phép nhìn thấy
                //if (npcZone == null)
                //{
                //    return true;
                //}
                
                //// Kiểm tra theo quy tắc zone
                //return this.CurrentZone.CanSeeZone(npcZone);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"FindNpcZone error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Kiểm tra xem một vật phẩm có ở trong phạm vi và có thể nhìn thấy theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <param name="item">Vật phẩm cần kiểm tra</param>
        /// <returns>true nếu vật phẩm trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public bool FindItemZone(int far, GroundItem item)
        {
            try
            {
                // Kiểm tra cơ bản về bản đồ
                if (item.MapID != this.MapID)
                {
                    return false;
                }
                
                // Kiểm tra khoảng cách
                var num = item.PosX - this.PosX;
                var num2 = item.PosY - this.PosY;
                bool isInRange = (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= far;
                
                if (!isInRange)
                {
                    return false;
                }
                
                //// Nếu người chơi không có zone, mặc định cho phép nhìn thấy
                //if (this.CurrentZone == null)
                //{
                //    return true;
                //}
                
                //// Kiểm tra xem người chơi có thể nhìn thấy vật phẩm theo quy tắc zone không
                return item.CanPlayerSeeItem(this);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"FindItemZone error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Lấy danh sách người chơi xung quanh theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <returns>Danh sách người chơi trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public List<Players> GetPlayersInRangeZone(int far)
        {
            List<Players> result = new List<Players>();
            try
            {
                // Use AOI system if enabled for this map
                if (AOI.AOIConfiguration.Instance.ShouldUseAOI(MapID))
                {
                    var aoiGrids = AOI.AOIManager.Instance.GetAOIGrids(PosX, PosY, MapID);
                    foreach (var grid in aoiGrids)
                    {
                        foreach (var player in grid.Players)
                        {
                            if (player != this && FindPlayers(far, player))
                            {
                                result.Add(player);
                            }
                        }
                    }
                }
                else
                {
                    // Fallback to old system
                    foreach (var player in World.allConnectedChars.Values)
                    {
                        if (player != this && FindPlayers(far, player))
                        {
                            result.Add(player);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"GetPlayersInRangeZone error: {ex.Message}");
            }
            return result;
        }
        
        /// <summary>
        /// Lấy danh sách NPC xung quanh theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <returns>Danh sách NPC trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public List<NpcClass> GetNpcsInRangeZone(int far)
        {
            List<NpcClass> result = new List<NpcClass>();
            try
            {
                // Use AOI system if enabled for this map
                if (AOI.AOIConfiguration.Instance.ShouldUseAOI(MapID))
                {
                    var aoiGrids = AOI.AOIManager.Instance.GetAOIGrids(PosX, PosY, MapID);
                    foreach (var grid in aoiGrids)
                    {
                        foreach (var npc in grid.NPCs)
                        {
                            if (FindNpcZone(far, npc))
                            {
                                result.Add(npc);
                            }
                        }
                    }
                }
                else
                {
                    // Fallback to old system - Lấy tất cả NPC trên bản đồ hiện tại bằng MapClass
                    var npcsInMap = MapClass.GetnpcTemplate(this.MapID);

                    if (npcsInMap != null)
                    {
                        foreach (var npc in npcsInMap.Values)
                        {
                            if (FindNpcZone(far, npc))
                            {
                                result.Add(npc);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"GetNpcsInRangeZone error: {ex.Message}");
            }
            return result;
        }
        
    }
} 