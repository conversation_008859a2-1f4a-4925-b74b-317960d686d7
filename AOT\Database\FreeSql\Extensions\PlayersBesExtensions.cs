using HeroYulgang.Database.FreeSql.Models;
using RxjhServer;

namespace HeroYulgang.Database.FreeSql.Extensions
{
    /// <summary>
    /// Extension methods for PlayersBes class to convert to database models
    /// Ph<PERSON>ơng thức mở rộng cho lớp PlayersBes để chuyển đổi sang model database
    /// </summary>
    public static class PlayersBesExtensions
    {
        /// <summary>
        /// Convert PlayersBes object to CharacterDataModel for database operations
        /// Chuyển đổi đối tượng PlayersBes sang CharacterDataModel cho thao tác database
        /// </summary>
        /// <param name="player">PlayersBes instance</param>
        /// <returns>CharacterDataModel with all character data</returns>
        public static CharacterDataModel ToCharacterDataModel(this PlayersBes player)
        {
            if (player == null)
                return null;

            return new CharacterDataModel
            {
                // Basic character information - Thông tin cơ bản nhân vật
                AccountID = player.AccountID,
                CharacterName = player.CharacterName,
                Player_Level = player.Player_Level,
                NewCharacterTemplate_CharacterTemplate_byte = player.NewCharacterTemplate.CharacterTemplate_byte,
                Player_Job = player.Player_Job,
                CharacterExperience = player.CharacterExperience.ToString(),
                Player_Zx = player.Player_Zx,
                Player_Job_level = player.Player_Job_level,

                // Position information - Thông tin vị trí
                PosX = player.PosX,
                PosY = player.PosY,
                PosZ = player.PosZ,
                MapID = player.MapID,

                // Character stats - Chỉ số nhân vật
                Player_Money = player.Player_Money.ToString(),
                NhanVat_HP = player.NhanVat_HP,
                NhanVat_MP = player.NhanVat_MP,
                NhanVat_SP = player.NhanVat_SP,
                Player_WuXun = player.Player_WuXun,
                NhanVatThienVaAc = player.NhanVatThienVaAc,
                Player_Qigong_point = player.Player_Qigong_point,

                // Binary data arrays - Dữ liệu nhị phân
                GetWgCodesbyte = player.GetWgCodesbyte(),
                GetWEARITEMCodesbyte = player.GetWEARITEMCodesbyte(),
                GetFLD_ITEMCodesbyte = player.GetFLD_ITEMCodesbyte(),
                GetFLD_FASHION_ITEMCodesbyte = player.GetFLD_FASHION_ITEMCodesbyte(),
                GetQuestITEMCodesbyte = player.GetQuestITEMCodesbyte(),
                GetFLD_KONGFUCodesbyte = player.GetFLD_KONGFUCodesbyte(),
                GetPersonalMedicinebyte = player.GetPersonalMedicinebyte(),
                GetThoLinhPhubyte = player.GetThoLinhPhubyte(),
                GetNhiemVubyte = player.GetNhiemVubyte(),
                GetNhiemVuFinishbyte = player.GetNhiemVuFinishbyte(),

                // Experience and skills - Kinh nghiệm và kỹ năng
                Player_ExpErience = player.Player_ExpErience,
                Character_KhinhCong = player.Character_KhinhCong,
                CharacterNameTemplate = player.CharacterNameTemplate,
                EquipmentDataVersion = player.EquipmentDataVersion,
                FLD_LoaiSanXuat = player.FLD_LoaiSanXuat,
                FLD_TrinhDoSanXuat = player.FLD_TrinhDoSanXuat,
                GetPersonalMedicineNewbyte = player.GetPersonalMedicineNewbyte(),

                // Relationship information - Thông tin quan hệ
                FLD_Couple = player.FLD_Couple,
                FLD_Couple_Love = player.FLD_Couple_Love,
                FLD_CoupleRing = player.FLD_CoupleRing,
                GiaiTruQuanHe_Countdown = player.GiaiTruQuanHe_Countdown,
                WhetherMarried = player.WhetherMarried,
                NhanCuoiKhacChu = player.NhanCuoiKhacChu,

                // Advanced skills - Kỹ năng nâng cao
                GetThangThienKhiCongCodesbyte = player.GetThangThienKhiCongCodesbyte(),
                GetThangThienVoCongCodesbyte = player.GetThangThienVoCongCodesbyte(),
                ThangThienLichLuyen_KinhNghiem = player.ThangThienLichLuyen_KinhNghiem,
                ThangThienVoCong_DiemSo = player.ThangThienVoCong_DiemSo,
                FLD_NUMBER_OPEN = player.FLD_NUMBER_OPEN,
                TitlePoints = player.TitlePoints,
                FLD_NgayKiNiemKetHon = player.FLD_NgayKiNiemKetHon,
                FLD_PVP_Piont = player.FLD_PVP_Piont,

                // Time-based data - Dữ liệu theo thời gian
                RemainingTimeOfTrainingMap = player.RemainingTimeOfTrainingMap,
                ActivityMapRemainingTime = player.ActivityMapRemainingTime,
                MatDi_VoHuan = player.MatDi_VoHuan,
                NhanVoHuan_MoiNgay = player.NhanVoHuan_MoiNgay,
                RoseTitlePoints = player.RoseTitlePoints,

                // Additional stats - Chỉ số bổ sung
                BanThuong_ThemVao_SinhMenh = player.BanThuong_ThemVao_SinhMenh,
                BanThuong_ThemVao_TanCong = player.BanThuong_ThemVao_TanCong.ToString(),
                BanThuong_ThemVao_PhongThu = player.BanThuong_ThemVao_PhongThu.ToString(),
                BanThuong_ThemVao_NeTranh = player.BanThuong_ThemVao_NeTranh,
                BanThuong_ThemVao_NoiCong = player.BanThuong_ThemVao_NoiCong,
                BanThuong_ThemVao_TrungDich = player.BanThuong_ThemVao_TrungDich,
                NumberOfRebirths = player.NumberOfRebirths,
                BanThuong_ThemVao_CLVC = player.BanThuong_ThemVao_CLVC,
                BanThuong_ThemVao_PTVC = player.BanThuong_ThemVao_PTVC,
                BanThuong_ThemVao_KC = player.BanThuong_ThemVao_KC,
                BangPhai_DoCongHien = player.BangPhai_DoCongHien,
                Player_Whtb = player.Player_Whtb,

                // Medicine and time data - Dữ liệu thuốc và thời gian
                GetTitleDrugbyte = player.GetTitleDrugbyte(),
                GetTimeMedicinebyte = player.GetTimeMedicinebyte(),
                ThanNuVoCongDiemSo = player.ThanNuVoCongDiemSo,
                ClientSettings = player.ClientSettings,
                TheLucChien_PhePhai = player.TheLucChien_PhePhai,
                GetFLD_PINKBAG_ITEMCodesbyte = player.GetFLD_PINKBAG_ITEMCodesbyte(),
                GetPhanKhiCongCodesbyte = player.GetPhanKhiCongCodesbyte()
            };
        }
    }
}
