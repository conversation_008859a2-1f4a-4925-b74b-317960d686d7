using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Services;

namespace HeroYulgang.Core.Native.Network
{
    /// <summary>
    /// Client session interface - thay thế ClientActor state
    /// </summary>
    public interface IClientSession
    {
        /// <summary>
        /// Unique session ID
        /// </summary>
        int SessionId { get; }
        
        /// <summary>
        /// Remote endpoint
        /// </summary>
        IPEndPoint RemoteEndPoint { get; }
        
        /// <summary>
        /// Session creation time
        /// </summary>
        DateTime CreatedAt { get; }
        
        /// <summary>
        /// Last activity time
        /// </summary>
        DateTime LastActivity { get; }
        
        /// <summary>
        /// Session state
        /// </summary>
        SessionState State { get; }
        
        /// <summary>
        /// Send data to client
        /// </summary>
        Task SendAsync(byte[] data, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Close session
        /// </summary>
        Task CloseAsync();
        
        /// <summary>
        /// Session properties
        /// </summary>
        IDictionary<string, object> Properties { get; }
        
        /// <summary>
        /// Check if session is active
        /// </summary>
        bool IsActive { get; }
    }

    /// <summary>
    /// Session states
    /// </summary>
    public enum SessionState
    {
        Connecting,
        Connected,
        Authenticated,
        Disconnecting,
        Disconnected
    }

    /// <summary>
    /// Network manager interface - thay thế TcpManagerActor
    /// </summary>
    public interface INetworkManager : IService
    {
        /// <summary>
        /// Start listening on specified port
        /// </summary>
        Task StartListeningAsync(int port, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Stop listening
        /// </summary>
        Task StopListeningAsync();
        
        /// <summary>
        /// Get session by ID
        /// </summary>
        IClientSession? GetSession(int sessionId);
        
        /// <summary>
        /// Get all active sessions
        /// </summary>
        IEnumerable<IClientSession> GetActiveSessions();
        
        /// <summary>
        /// Close session
        /// </summary>
        Task CloseSessionAsync(int sessionId);
        
        /// <summary>
        /// Broadcast data to all sessions
        /// </summary>
        Task BroadcastAsync(byte[] data, Predicate<IClientSession>? filter = null);
        
        /// <summary>
        /// Get network events
        /// </summary>
        IAsyncEnumerable<NetworkEvent> GetEventsAsync();
        
        /// <summary>
        /// Session count
        /// </summary>
        int SessionCount { get; }
        
        /// <summary>
        /// Is listening
        /// </summary>
        bool IsListening { get; }
    }

    /// <summary>
    /// Client session manager interface
    /// </summary>
    public interface IClientSessionManager : IService
    {
        /// <summary>
        /// Create new session
        /// </summary>
        Task<IClientSession> CreateSessionAsync(IPEndPoint remoteEndPoint, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Get session by ID
        /// </summary>
        Task<IClientSession?> GetSessionAsync(int sessionId);
        
        /// <summary>
        /// Close session
        /// </summary>
        Task CloseSessionAsync(int sessionId);
        
        /// <summary>
        /// Get session events
        /// </summary>
        IAsyncEnumerable<SessionEvent> GetSessionEventsAsync();
        
        /// <summary>
        /// Cleanup inactive sessions
        /// </summary>
        Task CleanupInactiveSessionsAsync();
    }

    /// <summary>
    /// Network events
    /// </summary>
    public abstract class NetworkEvent
    {
        public DateTime Timestamp { get; } = DateTime.UtcNow;
        public abstract string EventType { get; }
    }

    public class ClientConnectedEvent : NetworkEvent
    {
        public override string EventType => "ClientConnected";
        public IClientSession Session { get; }
        
        public ClientConnectedEvent(IClientSession session)
        {
            Session = session;
        }
    }

    public class ClientDisconnectedEvent : NetworkEvent
    {
        public override string EventType => "ClientDisconnected";
        public int SessionId { get; }
        public string Reason { get; }
        
        public ClientDisconnectedEvent(int sessionId, string reason)
        {
            SessionId = sessionId;
            Reason = reason;
        }
    }

    public class DataReceivedEvent : NetworkEvent
    {
        public override string EventType => "DataReceived";
        public int SessionId { get; }
        public byte[] Data { get; }
        
        public DataReceivedEvent(int sessionId, byte[] data)
        {
            SessionId = sessionId;
            Data = data;
        }
    }

    public class NetworkErrorEvent : NetworkEvent
    {
        public override string EventType => "NetworkError";
        public int? SessionId { get; }
        public Exception Exception { get; }
        
        public NetworkErrorEvent(Exception exception, int? sessionId = null)
        {
            Exception = exception;
            SessionId = sessionId;
        }
    }

    /// <summary>
    /// Session events
    /// </summary>
    public abstract class SessionEvent
    {
        public DateTime Timestamp { get; } = DateTime.UtcNow;
        public int SessionId { get; }
        public abstract string EventType { get; }
        
        protected SessionEvent(int sessionId)
        {
            SessionId = sessionId;
        }
    }

    public class SessionCreatedEvent : SessionEvent
    {
        public override string EventType => "SessionCreated";
        public IPEndPoint RemoteEndPoint { get; }
        
        public SessionCreatedEvent(int sessionId, IPEndPoint remoteEndPoint) : base(sessionId)
        {
            RemoteEndPoint = remoteEndPoint;
        }
    }

    public class SessionClosedEvent : SessionEvent
    {
        public override string EventType => "SessionClosed";
        public string Reason { get; }
        
        public SessionClosedEvent(int sessionId, string reason) : base(sessionId)
        {
            Reason = reason;
        }
    }

    public class SessionStateChangedEvent : SessionEvent
    {
        public override string EventType => "SessionStateChanged";
        public SessionState OldState { get; }
        public SessionState NewState { get; }
        
        public SessionStateChangedEvent(int sessionId, SessionState oldState, SessionState newState) : base(sessionId)
        {
            OldState = oldState;
            NewState = newState;
        }
    }

    /// <summary>
    /// Network configuration
    /// </summary>
    public class NetworkConfiguration
    {
        /// <summary>
        /// Server port
        /// </summary>
        public int Port { get; set; } = 9999;
        
        /// <summary>
        /// Max concurrent connections
        /// </summary>
        public int MaxConnections { get; set; } = 1000;
        
        /// <summary>
        /// Connection timeout
        /// </summary>
        public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromMinutes(5);
        
        /// <summary>
        /// Buffer size for receiving data
        /// </summary>
        public int ReceiveBufferSize { get; set; } = 8192;
        
        /// <summary>
        /// Buffer size for sending data
        /// </summary>
        public int SendBufferSize { get; set; } = 8192;
        
        /// <summary>
        /// Keep alive interval
        /// </summary>
        public TimeSpan KeepAliveInterval { get; set; } = TimeSpan.FromMinutes(1);
        
        /// <summary>
        /// Inactive session cleanup interval
        /// </summary>
        public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromMinutes(5);
        
        /// <summary>
        /// Enable Nagle algorithm
        /// </summary>
        public bool NoDelay { get; set; } = true;
        
        /// <summary>
        /// Socket linger options
        /// </summary>
        public bool LingerEnabled { get; set; } = false;
        
        /// <summary>
        /// Linger timeout
        /// </summary>
        public int LingerTimeout { get; set; } = 0;
    }

    /// <summary>
    /// Connection statistics
    /// </summary>
    public class ConnectionStatistics
    {
        public int TotalConnections { get; set; }
        public int ActiveConnections { get; set; }
        public long BytesSent { get; set; }
        public long BytesReceived { get; set; }
        public long PacketsSent { get; set; }
        public long PacketsReceived { get; set; }
        public DateTime StartTime { get; set; }
        public TimeSpan Uptime => DateTime.UtcNow - StartTime;
    }

    /// <summary>
    /// Network service factory
    /// </summary>
    public interface INetworkServiceFactory
    {
        /// <summary>
        /// Create network manager
        /// </summary>
        INetworkManager CreateNetworkManager(NetworkConfiguration configuration);
        
        /// <summary>
        /// Create session manager
        /// </summary>
        IClientSessionManager CreateSessionManager();
    }
}
