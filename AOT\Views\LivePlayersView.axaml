<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="700"
             x:Class="HeroYulgang.Views.LivePlayersView"
             x:CompileBindings="False">

    <UserControl.Styles>
        <!-- Adaptive Theme Styles for Light/Dark compatibility -->
        <Style Selector="Border.live-header">
            <Setter Property="Background" Value="#2D3748"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style Selector="Border.live-card">
            <Setter Property="Background" Value="#1F2937"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>

        <Style Selector="Border.live-card:nth-child(1)">
            <Setter Property="Margin" Value="0,0,5,0"/>
        </Style>

        <Style Selector="Border.live-card:nth-last-child(1)">
            <Setter Property="Margin" Value="5,0,0,0"/>
        </Style>

        <Style Selector="TextBlock.live-header-title">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="TextBlock.live-card-title">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style Selector="TextBlock.live-card-value">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,5,0,0"/>
        </Style>

        <Style Selector="TextBlock.live-card-subtitle">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#9CA3AF"/>
        </Style>
    </UserControl.Styles>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header với status và controls -->
        <Border Grid.Row="0" Classes="live-header">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="👥 Live Players Monitor" Classes="live-header-title" VerticalAlignment="Center"/>
                    <Border Name="StatusIndicator" Background="#10B981" CornerRadius="10" Width="20" Height="20" Margin="15,0,0,0">
                        <TextBlock Text="●" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="12"/>
                    </Border>
                    <TextBlock Name="StatusText" Text="Live" Foreground="#10B981" FontWeight="Bold" Margin="10,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,15,0">
                    <TextBlock Text="Auto Update (5s):" Foreground="White" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <CheckBox Name="AutoUpdateCheckBox" IsChecked="True" VerticalAlignment="Center"/>
                </StackPanel>

                <Button Grid.Column="2" Name="RefreshButton" Content="🔄 Refresh" Background="#3B82F6" Foreground="White"
                        Padding="10,5" CornerRadius="5" BorderThickness="0"/>
            </Grid>
        </Border>

        <!-- Statistics Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Players Card -->
            <Border Grid.Column="0" Classes="live-card" Margin="0,0,5,0">
                <StackPanel>
                    <TextBlock Text="👥 Total Players" Classes="live-card-title" Foreground="#60A5FA"/>
                    <TextBlock Name="TotalPlayersText" Text="0" Classes="live-card-value"/>
                    <TextBlock Text="players online" Classes="live-card-subtitle"/>
                </StackPanel>
            </Border>

            <!-- Active Sessions Card -->
            <Border Grid.Column="1" Classes="live-card">
                <StackPanel>
                    <TextBlock Text="🔗 Active Sessions" Classes="live-card-title" Foreground="#34D399"/>
                    <TextBlock Name="ActiveSessionsText" Text="0" Classes="live-card-value"/>
                    <TextBlock Text="connections" Classes="live-card-subtitle"/>
                </StackPanel>
            </Border>

            <!-- Peak Today Card -->
            <Border Grid.Column="2" Classes="live-card">
                <StackPanel>
                    <TextBlock Text="📈 Peak Today" Classes="live-card-title" Foreground="#FBBF24"/>
                    <TextBlock Name="PeakTodayText" Text="0" Classes="live-card-value"/>
                    <TextBlock Name="PeakTimeText" Text="at --:--" Classes="live-card-subtitle"/>
                </StackPanel>
            </Border>

            <!-- Average Level Card -->
            <Border Grid.Column="3" Classes="live-card" Margin="5,0,0,0">
                <StackPanel>
                    <TextBlock Text="⭐ Avg Level" Classes="live-card-title" Foreground="#F87171"/>
                    <TextBlock Name="AverageLevelText" Text="0" Classes="live-card-value"/>
                    <TextBlock Text="level average" Classes="live-card-subtitle"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Players List -->
        <Border Grid.Row="2" Classes="live-card" Margin="0,0,0,15">
            <Grid RowDefinitions="Auto,Auto,*">
                <!-- Search Controls -->
                <Grid Grid.Row="0" ColumnDefinitions="*,Auto,Auto" Margin="0,0,0,15">
                    <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="10">
						<TextBlock Text="Search" FontWeight="Bold" Foreground="White" FontSize="16" VerticalAlignment="Center"/>
                        <TextBox Name="SearchTextBox" Width="300" Watermark="Nhập tên player, ID, SessionID..." Background="#374151" Foreground="White" BorderBrush="#4B5563"/>
                        <Button Name="SearchButton" Content="Tìm kiếm" Background="#3B82F6" Foreground="White" Padding="8,5" CornerRadius="4" BorderThickness="0"/>
                        <Button Name="ClearSearchButton" Content="Xóa" Background="#6B7280" Foreground="White" Padding="8,5" CornerRadius="4" BorderThickness="0"/>
                    </StackPanel>
                </Grid>

                <!-- Table Header -->
                <Grid Grid.Row="1" ColumnDefinitions="80,150,150,80,120,100,120,100,100,*"
                      Background="#2D3748" Height="40" Margin="0,0,0,5">
                    <TextBlock Grid.Column="0" Text="SessionID" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="1" Text="AccountID" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="2" Text="Character Name" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="3" Text="Level" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="4" Text="Job" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="5" Text="Job Level" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="6" Text="Map" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="7" Text="X" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="8" Text="Y" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="9" Text="Online Time" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="8,0"/>
                </Grid>

                <!-- Data -->
                <Border Grid.Row="2" Background="#374151" CornerRadius="4" Margin="0,5,0,0">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto" MaxHeight="300">
                        <ListBox Name="PlayersDataGrid" Background="Transparent" BorderThickness="0" Margin="5">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#4B5563" CornerRadius="4" Margin="0,2" Padding="4">
                                        <Grid ColumnDefinitions="80,150,150,80,120,100,120,100,100,*" MinHeight="36">
                                            <TextBlock Grid.Column="0" Text="{Binding SessionID}" Foreground="#9CA3AF" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="1" Text="{Binding AccountID}" Foreground="White" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="2" Text="{Binding CharacterName}" Foreground="#60A5FA" FontWeight="SemiBold" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="3" Text="{Binding Player_Level}" Foreground="#34D399" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="4" Text="{Binding Player_Job}" Foreground="#FBBF24" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="5" Text="{Binding Player_Job_level}" Foreground="White" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="6" Text="{Binding NhanVatToaDo_BanDo}" Foreground="#F87171" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="7" Text="{Binding NhanVatToaDo_X}" Foreground="White" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="8" Text="{Binding NhanVatToaDo_Y}" Foreground="White" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                            <TextBlock Grid.Column="9" Text="{Binding OnlineTime, StringFormat=\{0:HH:mm:ss\}}" Foreground="#9CA3AF" VerticalAlignment="Center" Margin="8,0" TextWrapping="Wrap"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </ScrollViewer>
                </Border>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Player Actions -->
            <Border Grid.Column="0" Classes="live-card" Margin="0,0,5,0">
                <StackPanel>
                    <TextBlock Text="🎮 Player Actions" FontWeight="Bold" Foreground="White" FontSize="16" Margin="0,0,0,10"/>
                    <StackPanel Orientation="Horizontal" Spacing="10">
                        <Button Name="KickPlayerButton" Content="🚫 Kick Player" Background="#EF4444" Foreground="White"
                                Padding="10,6" CornerRadius="5" BorderThickness="0" IsEnabled="False"/>
                        <Button Name="SendMessageButton" Content="💬 Gửi tin nhắn" Background="#3B82F6" Foreground="White"
                                Padding="10,6" CornerRadius="5" BorderThickness="0" IsEnabled="False"/>
                        <Button Name="TeleportToPlayerButton" Content="🌟 Teleport đến" Background="#8B5CF6" Foreground="White"
                                Padding="10,6" CornerRadius="5" BorderThickness="0" IsEnabled="False"/>
                        <Button Name="ViewPlayerDetailsButton" Content="📋 Xem chi tiết" Background="#059669" Foreground="White"
                                Padding="10,6" CornerRadius="5" BorderThickness="0" IsEnabled="False"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Server Actions -->
            <Border Grid.Column="1" Classes="live-card" Margin="5,0,0,0">
                <StackPanel>
                    <TextBlock Text="🌐 Server Actions" FontWeight="Bold" Foreground="White" FontSize="16" Margin="0,0,0,10"/>
                    <StackPanel Orientation="Horizontal" Spacing="10">
                        <Button Name="ExportPlayersButton" Content="📊 Xuất danh sách" Background="#6B7280" Foreground="White"
                                Padding="10,6" CornerRadius="5" BorderThickness="0"/>
                        <Button Name="BroadcastMessageButton" Content="📢 Thông báo toàn server" Background="#F59E0B" Foreground="White"
                                Padding="10,6" CornerRadius="5" BorderThickness="0"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</UserControl>
