using System;
using System.Linq;

namespace RxjhServer;

public class X_Toa_Do_Class : IDisposable
{
	private float _Rxjh_X;

	private float _Rxjh_Y;

	private float _Rxjh_Z;

	private int _Rxjh_Map;

	private string _Rxjh_name;

	public float Rxjh_X
	{
		get
		{
			return _Rxjh_X;
		}
		set
		{
			_Rxjh_X = value;
		}
	}

	public float Rxjh_Y
	{
		get
		{
			return _Rxjh_Y;
		}
		set
		{
			_Rxjh_Y = value;
		}
	}

	public float Rxjh_Z
	{
		get
		{
			return _Rxjh_Z;
		}
		set
		{
			_Rxjh_Z = value;
		}
	}

	public int Rxjh_Map
	{
		get
		{
			return _Rxjh_Map;
		}
		set
		{
			_Rxjh_Map = value;
		}
	}

	public string Rxjh_name
	{
		get
		{
			return _Rxjh_name;
		}
		set
		{
			_Rxjh_name = value;
		}
	}

	public static string getmapname(int id)
	{
		return World.DiDong.FirstOrDefault(x => x.Rxjh_Map == id)?.Rxjh_name ?? string.Empty;
	}

	public static string GetMapName(int id)
	{
		if (World.Maplist.TryGetValue(id, out var value))
		{
			return value;
		}
		return string.Empty;
	}

	public void Dispose()
	{
	}

	public X_Toa_Do_Class(float Rxjh__X, float Rxjh__Y, float Rxjh__Z, int Rxjh__Map)
	{
		Rxjh_X = Rxjh__X;
		Rxjh_Y = Rxjh__Y;
		Rxjh_Z = Rxjh__Z;
		Rxjh_Map = Rxjh__Map;
	}

	public X_Toa_Do_Class()
	{
	}

	public static string GetName_TiengViet(int Rxjh_MapID)
	{
		return Rxjh_MapID switch
		{
			101 => "Huyền Bột Phái", 
			201 => "Tam Tà Quan", 
			301 => "Liễu Chính Quan", 
			401 => "Vô Thiên Các Tầng 1", 
			402 => "Vô Thiên Các Tầng 2", 
			403 => "Vô Thiên Các Tầng 3", 
			601 => "Uyên Trúc Lâm", 
			501 => "Vạn Thọ Các Tầng 1", 
			502 => "Vạn Thọ Các Tầng 2", 
			503 => "Vạn Thọ Các Tầng 3", 
			701 => "Trúc Hỏa Lâm", 
			801 => "Nghĩa Đấu Quan", 
			901 => "Huân Phủng Bao", 
			1001 => "Thần Võ Môn", 
			1101 => "Liễu Thiện Phủ", 
			1201 => "Chợ", 
			1301 => "Nam Minh Hiệu", 
			1401 => "Huyết Ma Động Tầng 1", 
			1501 => "Huyết Ma Động Tầng 2", 
			1601 => "Huyết Ma Động Tầng 3", 
			1701 => "Địa Linh Động Tầng 1", 
			1801 => "Địa Linh Động Tầng 2", 
			1901 => "Địa Linh Động Tầng 3", 
			2001 => "Nam Minh Động", 
			2101 => "Tụng Nguyệt Quán", 
			2201 => "Bạch Võ Quán", 
			2401 => "Mai Hoa Mê Cung", 
			2501 => "Thất Lạc Chi Địa", 
			2601 => "Thược Thi Phòng", 
			2701 => "Mê Cung Tầng Thứ 1", 
			2711 => "Mê Cung Tầng Thứ 2", 
			2721 => "Mê Cung Tầng Thứ 3", 
			2801 => "Tam Giới Huyền Môn", 
			3201 => "Nữ Vương Cung Điện", 
			2901 => "Khu Luyện Tập", 
			5001 => "Bắc Hải Băng Cung", 
			5101 => "Bắc Hải Thủy Cung", 
			5201 => "Bắc Hải Huyền Băng Cung", 
			5401 => "Huyền Băng Địa Cung", 
			5501 => "Bắc Hải Băng Cung Huyễn Ảnh", 
			5601 => "Plants vs Zombie", 
			6001 => "Nam Lâm", 
			25100 => "Hồ Hạp Cốc", 
			25201 => "Cửu Tử", 
			26000 => "Xích Thiên Giới", 
			26100 => "Thiên Du Sơn", 
			26200 => "Thánh Địa Kiếm Hoàng", 
			40101 => "Đại Chiến Hồn", 
			_ => string.Empty, 
		};
	}
}
