﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class bachbaocacrecord {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string userid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string username { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string vatpham_id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string vatphamten { get; set; }

		[JsonProperty]
		public int? vatphamsoluong { get; set; }

		[JsonProperty]
		public int? nguyenbaosoluong { get; set; }

		[JsonProperty]
		public DateTime? thoigian { get; set; }

	}

}
