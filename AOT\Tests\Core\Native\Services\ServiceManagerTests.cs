using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using HeroYulgang.Core.Native.Services;

namespace HeroYulgang.Tests.Core.Native.Services
{
    /// <summary>
    /// Tests for ServiceManager implementation
    /// </summary>
    [TestFixture]
    public class ServiceManagerTests : TestBase
    {
        private IServiceManager _serviceManager = null!;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            _serviceManager = ServiceProvider.GetRequiredService<IServiceManager>();
        }

        [Test]
        public async Task RegisterService_Should_Add_Service_Successfully()
        {
            // Arrange
            var testService = new TestService();

            // Act
            _serviceManager.RegisterService(testService);

            // Assert
            var registeredServices = _serviceManager.GetRegisteredServices();
            Assert.Contains(testService, registeredServices.ToArray());
        }

        [Test]
        public async Task StartAsync_Should_Start_All_Registered_Services()
        {
            // Arrange
            var service1 = new TestService();
            var service2 = new TestService();
            
            _serviceManager.RegisterService(service1);
            _serviceManager.RegisterService(service2);

            // Act
            await _serviceManager.StartAsync(CancellationToken.None);

            // Assert
            await AssertTaskCompletesAsync(service1.StartedTcs.Task, TimeSpan.FromSeconds(1));
            await AssertTaskCompletesAsync(service2.StartedTcs.Task, TimeSpan.FromSeconds(1));
            
            Assert.IsTrue(service1.StartCalled);
            Assert.IsTrue(service2.StartCalled);
            Assert.IsTrue(service1.RunningCalled);
            Assert.IsTrue(service2.RunningCalled);
        }

        [Test]
        public async Task StopAsync_Should_Stop_All_Running_Services()
        {
            // Arrange
            var service1 = new TestService();
            var service2 = new TestService();
            
            _serviceManager.RegisterService(service1);
            _serviceManager.RegisterService(service2);

            await _serviceManager.StartAsync(CancellationToken.None);
            await AssertTaskCompletesAsync(service1.StartedTcs.Task, TimeSpan.FromSeconds(1));
            await AssertTaskCompletesAsync(service2.StartedTcs.Task, TimeSpan.FromSeconds(1));

            // Act
            await _serviceManager.StopAsync(CancellationToken.None);

            // Assert
            await AssertTaskCompletesAsync(service1.StoppedTcs.Task, TimeSpan.FromSeconds(1));
            await AssertTaskCompletesAsync(service2.StoppedTcs.Task, TimeSpan.FromSeconds(1));
            
            Assert.IsTrue(service1.StopCalled);
            Assert.IsTrue(service2.StopCalled);
        }

        [Test]
        public async Task Service_Exception_During_Start_Should_Be_Handled()
        {
            // Arrange
            var faultyService = new TestService();
            var goodService = new TestService();
            
            faultyService.ExceptionToThrow = new InvalidOperationException("Test exception");
            
            _serviceManager.RegisterService(faultyService);
            _serviceManager.RegisterService(goodService);

            // Act & Assert
            // Service manager should handle the exception and continue with other services
            await _serviceManager.StartAsync(CancellationToken.None);

            // Good service should still start successfully
            await AssertTaskCompletesAsync(goodService.StartedTcs.Task, TimeSpan.FromSeconds(1));
            Assert.IsTrue(goodService.StartCalled);
        }

        [Test]
        public async Task GetServiceHealth_Should_Return_Service_Status()
        {
            // Arrange
            var service = new TestService();
            _serviceManager.RegisterService(service);

            // Act - Before starting
            var healthBefore = _serviceManager.GetServiceHealth();
            
            await _serviceManager.StartAsync(CancellationToken.None);
            await AssertTaskCompletesAsync(service.StartedTcs.Task, TimeSpan.FromSeconds(1));

            // Act - After starting
            var healthAfter = _serviceManager.GetServiceHealth();

            // Assert
            Assert.IsNotNull(healthBefore);
            Assert.IsNotNull(healthAfter);
            
            // Health should show improvement after starting services
            Assert.GreaterOrEqual(healthAfter.HealthyServices, healthBefore.HealthyServices);
        }

        [Test]
        public async Task RestartService_Should_Stop_And_Start_Service()
        {
            // Arrange
            var service = new TestService();
            _serviceManager.RegisterService(service);
            
            await _serviceManager.StartAsync(CancellationToken.None);
            await AssertTaskCompletesAsync(service.StartedTcs.Task, TimeSpan.FromSeconds(1));

            // Reset for restart test
            service.Reset();

            // Act
            await _serviceManager.RestartServiceAsync(service.Name);

            // Assert
            await AssertTaskCompletesAsync(service.StartedTcs.Task, TimeSpan.FromSeconds(1));
            Assert.IsTrue(service.StartCalled, "Service should be started after restart");
        }

        [Test]
        public async Task Multiple_Service_Types_Should_Be_Managed_Correctly()
        {
            // Arrange
            var services = new TestService[5];
            for (int i = 0; i < 5; i++)
            {
                services[i] = new TestService();
                _serviceManager.RegisterService(services[i]);
            }

            // Act
            await _serviceManager.StartAsync(CancellationToken.None);

            // Assert - All services should start
            foreach (var service in services)
            {
                await AssertTaskCompletesAsync(service.StartedTcs.Task, TimeSpan.FromSeconds(1));
                Assert.IsTrue(service.StartCalled, $"Service should be started");
                Assert.IsTrue(service.RunningCalled, $"Service should be running");
            }

            // Act - Stop all services
            await _serviceManager.StopAsync(CancellationToken.None);

            // Assert - All services should stop
            foreach (var service in services)
            {
                await AssertTaskCompletesAsync(service.StoppedTcs.Task, TimeSpan.FromSeconds(1));
                Assert.IsTrue(service.StopCalled, $"Service should be stopped");
            }
        }

        [Test]
        public async Task Concurrent_Service_Operations_Should_Be_Thread_Safe()
        {
            // Arrange
            const int serviceCount = 20;
            var services = new TestService[serviceCount];
            
            for (int i = 0; i < serviceCount; i++)
            {
                services[i] = new TestService();
            }

            // Act - Register services concurrently
            var registerTasks = new Task[serviceCount];
            for (int i = 0; i < serviceCount; i++)
            {
                var service = services[i];
                registerTasks[i] = Task.Run(() => _serviceManager.RegisterService(service));
            }

            await Task.WhenAll(registerTasks);

            // Start all services
            await _serviceManager.StartAsync(CancellationToken.None);

            // Assert - All services should start successfully
            foreach (var service in services)
            {
                await AssertTaskCompletesAsync(service.StartedTcs.Task, TimeSpan.FromSeconds(2));
                Assert.IsTrue(service.StartCalled);
            }

            var registeredServices = _serviceManager.GetRegisteredServices();
            Assert.AreEqual(serviceCount, registeredServices.Count());
        }

        [Test]
        public async Task Service_Lifecycle_Events_Should_Be_Fired()
        {
            // Arrange
            var service = new TestService();
            var serviceStartedEventFired = false;
            var serviceStoppedEventFired = false;

            // Subscribe to events if available
            // Note: This assumes the ServiceManager has events - adjust based on actual implementation
            
            _serviceManager.RegisterService(service);

            // Act
            await _serviceManager.StartAsync(CancellationToken.None);
            await AssertTaskCompletesAsync(service.StartedTcs.Task, TimeSpan.FromSeconds(1));

            await _serviceManager.StopAsync(CancellationToken.None);
            await AssertTaskCompletesAsync(service.StoppedTcs.Task, TimeSpan.FromSeconds(1));

            // Assert
            Assert.IsTrue(service.StartCalled);
            Assert.IsTrue(service.StopCalled);
        }

        [Test]
        public async Task GetRegisteredServices_Should_Return_All_Services()
        {
            // Arrange
            var service1 = new TestService();
            var service2 = new TestService();
            var service3 = new TestService();

            // Act
            _serviceManager.RegisterService(service1);
            _serviceManager.RegisterService(service2);
            _serviceManager.RegisterService(service3);

            var registeredServices = _serviceManager.GetRegisteredServices();

            // Assert
            Assert.AreEqual(3, registeredServices.Count());
            Assert.Contains(service1, registeredServices.ToArray());
            Assert.Contains(service2, registeredServices.ToArray());
            Assert.Contains(service3, registeredServices.ToArray());
        }

        [Test]
        public async Task Service_Health_Monitoring_Should_Detect_Failures()
        {
            // Arrange
            var healthyService = new TestService();
            var faultyService = new TestService();
            
            faultyService.ExceptionToThrow = new InvalidOperationException("Service failure");

            _serviceManager.RegisterService(healthyService);
            _serviceManager.RegisterService(faultyService);

            // Act
            await _serviceManager.StartAsync(CancellationToken.None);
            
            // Wait for healthy service to start
            await AssertTaskCompletesAsync(healthyService.StartedTcs.Task, TimeSpan.FromSeconds(1));

            // Give some time for health monitoring to detect the faulty service
            await Task.Delay(100);

            // Assert
            var health = _serviceManager.GetServiceHealth();
            Assert.GreaterOrEqual(health.HealthyServices, 1); // At least the healthy service
            
            // If the service manager tracks failed services, verify that
            if (health.GetType().GetProperty("FailedServices") != null)
            {
                Assert.GreaterOrEqual(health.FailedServices, 1);
            }
        }

        [Test]
        public async Task Dispose_Should_Stop_All_Services()
        {
            // Arrange
            var service1 = new TestService();
            var service2 = new TestService();
            
            _serviceManager.RegisterService(service1);
            _serviceManager.RegisterService(service2);

            await _serviceManager.StartAsync(CancellationToken.None);
            await AssertTaskCompletesAsync(service1.StartedTcs.Task, TimeSpan.FromSeconds(1));
            await AssertTaskCompletesAsync(service2.StartedTcs.Task, TimeSpan.FromSeconds(1));

            // Act
            if (_serviceManager is IDisposable disposable)
            {
                disposable.Dispose();
            }

            // Assert
            // Services should be stopped as part of disposal
            // Note: This test depends on the actual disposal implementation
            await Task.Delay(100); // Give time for disposal to complete
            
            // Verify services are in stopped state if possible
            Assert.IsTrue(service1.StartCalled);
            Assert.IsTrue(service2.StartCalled);
        }
    }
}
