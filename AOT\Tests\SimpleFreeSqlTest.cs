using System;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;

namespace HeroYulgang.Tests
{
    /// <summary>
    /// Simple test for new FreeSql system
    /// </summary>
    public static class SimpleFreeSqlTest
    {
        /// <summary>
        /// Test the simple Services.Public.InitializeAsync() approach
        /// </summary>
        public static async Task<bool> TestSimpleServicesAsync()
        {
            try
            {
                Console.WriteLine("=== Simple FreeSql Services Test ===");

                // Test exactly like World.SetItme() would call it
                Console.WriteLine("Testing Services.Public.InitializeAsync()...");
                var success = await HeroYulgang.Database.FreeSql.Services.Public.InitializeAsync();

                if (success)
                {
                    Console.WriteLine("✓ Services.Public.InitializeAsync() completed successfully");
                    
                    // Print stats
                    HeroYulgang.Database.FreeSql.Services.Public.PrintStats();
                    
                    // Test getting some data
                    var itemTemplate = PublicDb.GetItemTemplate(1);
                    if (itemTemplate != null)
                    {
                        Console.WriteLine($"✓ Got item template: {itemTemplate.fld_name}");
                    }
                    else
                    {
                        Console.WriteLine("! No item template found with ID 1");
                    }

                    var monsterTemplate = PublicDb.GetMonsterTemplate(1);
                    if (monsterTemplate != null)
                    {
                        Console.WriteLine($"✓ Got monster template: {monsterTemplate.fld_name}");
                    }
                    else
                    {
                        Console.WriteLine("! No monster template found with ID 1");
                    }

                    return true;
                }
                else
                {
                    Console.WriteLine("✗ Services.Public.InitializeAsync() failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test refresh functionality
        /// </summary>
        public static async Task<bool> TestRefreshAsync()
        {
            try
            {
                Console.WriteLine("\n=== Testing Refresh Functionality ===");
                
                var success = await HeroYulgang.Database.FreeSql.Services.Public.RefreshAsync();
                
                if (success)
                {
                    Console.WriteLine("✓ Refresh completed successfully");
                    HeroYulgang.Database.FreeSql.Services.Public.PrintStats();
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ Refresh failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Refresh test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test all services status
        /// </summary>
        public static void TestServicesStatus()
        {
            Console.WriteLine("\n=== Services Status ===");
            HeroYulgang.Database.FreeSql.Services.PrintStatus();
        }

        /// <summary>
        /// Run all simple tests
        /// </summary>
        public static async Task<bool> RunAllTestsAsync()
        {
            Console.WriteLine("Starting Simple FreeSql Tests...\n");

            var initTest = await TestSimpleServicesAsync();
            var refreshTest = await TestRefreshAsync();
            TestServicesStatus();

            Console.WriteLine("\n=== Test Summary ===");
            Console.WriteLine($"Initialize Test: {(initTest ? "PASS" : "FAIL")}");
            Console.WriteLine($"Refresh Test: {(refreshTest ? "PASS" : "FAIL")}");

            var allPassed = initTest && refreshTest;
            Console.WriteLine($"\nOverall Result: {(allPassed ? "ALL TESTS PASSED" : "SOME TESTS FAILED")}");

            return allPassed;
        }
    }
}
