using System;
using System.Linq;
using HeroYulgang.Core.Network;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer.Commands
{
    /// <summary>
    /// Extension methods for Players class
    /// </summary>
    public static class PlayersExtensions
    {
        /// <summary>
        /// Send message to player using HeThongNhacNho
        /// </summary>
        public static void SendMessage(this Players player, string message)
        {
            player.HeThongNhacNho(message, 7, "System");
        }
    }

    /// <summary>
    /// Command handler for packet batching system management
    /// </summary>
    public static class BatchCommand
    {
        /// <summary>
        /// Handle batch-related commands
        /// Usage: !batch [status|enable|disable|config|test|report]
        /// </summary>
        public static void HandleCommand(Players player, string[] args)
        {
            if (args.Length < 1)
            {
                ShowHelp(player);
                return;
            }

            var subCommand = args[0].ToLower();

            switch (subCommand)
            {
                case "status":
                    ShowStatus(player);
                    break;
                case "enable":
                    EnableBatching(player, args.Skip(1).ToArray());
                    break;
                case "disable":
                    DisableBatching(player, args.Skip(1).ToArray());
                    break;
                case "config":
                    ShowConfig(player);
                    break;
                case "test":
                    RunTest(player, args.Skip(1).ToArray());
                    break;
                case "report":
                    ShowReport(player);
                    break;
                case "preset":
                    ApplyPreset(player, args.Skip(1).ToArray());
                    break;
                default:
                    ShowHelp(player);
                    break;
            }
        }

        private static void ShowHelp(Players player)
        {
            player.SendMessage("=== Packet Batch Commands ===");
            player.SendMessage("!batch status - Show current batch status");
            player.SendMessage("!batch enable [opcode] - Enable batching (optionally for specific opcode)");
            player.SendMessage("!batch disable [opcode] - Disable batching (optionally for specific opcode)");
            player.SendMessage("!batch config - Show current configuration");
            player.SendMessage("!batch test [packets] [players] - Run performance test");
            player.SendMessage("!batch report - Show performance report");
            player.SendMessage("!batch preset [performance|bandwidth|default] - Apply preset configuration");
        }

        private static void ShowStatus(Players player)
        {
            var settings = PacketBatchConfig.Settings;
            var metrics = PacketBatchTester.GetAggregatedMetrics();

            player.SendMessage("=== Packet Batch Status ===");
            player.SendMessage($"Batching Enabled: {(settings.EnableBatching ? "Yes" : "No")}");
            player.SendMessage($"Batch Window: {settings.BatchWindowMs}ms");
            player.SendMessage($"Max Batch Size: {settings.MaxBatchSize}");
            player.SendMessage($"Compression Opcode: {settings.CompressionOpcode}");
            
            if (metrics.TotalPacketsSent > 0)
            {
                player.SendMessage($"Total Packets: {metrics.TotalPacketsSent:N0}");
                player.SendMessage($"Batching Ratio: {metrics.BatchingRatio:P1}");
                player.SendMessage($"Compression Ratio: {metrics.CompressionRatio:F3}");
            }
        }

        private static void EnableBatching(Players player, string[] args)
        {
            if (args.Length > 0 && int.TryParse(args[0], out var opcode))
            {
                PacketBatchConfig.SetOpcodeEnabled(opcode, true);
                player.SendMessage($"Enabled batching for opcode {opcode}");
            }
            else
            {
                var settings = PacketBatchConfig.Settings;
                settings.EnableBatching = true;
                PacketBatchConfig.SaveSettings();
                player.SendMessage("Enabled packet batching globally");
            }
        }

        private static void DisableBatching(Players player, string[] args)
        {
            if (args.Length > 0 && int.TryParse(args[0], out var opcode))
            {
                PacketBatchConfig.SetOpcodeEnabled(opcode, false);
                player.SendMessage($"Disabled batching for opcode {opcode}");
            }
            else
            {
                var settings = PacketBatchConfig.Settings;
                settings.EnableBatching = false;
                PacketBatchConfig.SaveSettings();
                player.SendMessage("Disabled packet batching globally");
            }
        }

        private static void ShowConfig(Players player)
        {
            var settings = PacketBatchConfig.Settings;

            player.SendMessage("=== Packet Batch Configuration ===");
            player.SendMessage($"Global Enable: {settings.EnableBatching}");
            player.SendMessage($"Batch Window: {settings.BatchWindowMs}ms");
            player.SendMessage($"Max Batch Size: {settings.MaxBatchSize}");
            player.SendMessage($"Compression Opcode: {settings.CompressionOpcode}");
            
            player.SendMessage("Opcode Settings:");
            foreach (var kvp in settings.OpcodeSettings)
            {
                var status = kvp.Value ? "Enabled" : "Disabled";
                var opcodeName = GetOpcodeName(kvp.Key);
                player.SendMessage($"  {kvp.Key} ({opcodeName}): {status}");
            }

            if (settings.MapSettings.Any())
            {
                player.SendMessage("Map-specific Settings:");
                foreach (var kvp in settings.MapSettings)
                {
                    var mapSettings = kvp.Value;
                    player.SendMessage($"  Map {kvp.Key}: {(mapSettings.EnableBatching ? "Enabled" : "Disabled")}, " +
                                     $"Window: {mapSettings.BatchWindowMs}ms, Max: {mapSettings.MaxBatchSize}");
                }
            }
        }

        private static void RunTest(Players player, string[] args)
        {
            var numPackets = 1000;
            var numPlayers = 10;

            if (args.Length > 0 && int.TryParse(args[0], out var packets))
                numPackets = packets;

            if (args.Length > 1 && int.TryParse(args[1], out var players))
                numPlayers = players;

            player.SendMessage($"Starting performance test with {numPackets} packets and {numPlayers} players...");

            // Run test asynchronously
            System.Threading.Tasks.Task.Run(async () =>
            {
                try
                {
                    var (batched, individual) = await PacketBatchTester.RunPerformanceTest(numPackets, numPlayers, false);
                    
                    var improvement = batched.PacketsPerSecond / individual.PacketsPerSecond;
                    var bandwidthSavings = 1 - batched.CompressionRatio;

                    player.SendMessage("=== Performance Test Results ===");
                    player.SendMessage($"Batched: {batched.PacketsPerSecond:F2} packets/sec");
                    player.SendMessage($"Individual: {individual.PacketsPerSecond:F2} packets/sec");
                    player.SendMessage($"Performance Improvement: {improvement:F2}x");
                    player.SendMessage($"Compression Ratio: {batched.CompressionRatio:F3}");
                    player.SendMessage($"Bandwidth Savings: {bandwidthSavings:P1}");
                }
                catch (Exception ex)
                {
                    player.SendMessage($"Test failed: {ex.Message}");
                    LogHelper.WriteLine(LogLevel.Error, $"Batch test error: {ex.Message}");
                }
            });
        }

        private static void ShowReport(Players player)
        {
            var report = PacketBatchTester.GeneratePerformanceReport();
            var lines = report.Split('\n');
            
            foreach (var line in lines)
            {
                if (!string.IsNullOrWhiteSpace(line))
                {
                    player.SendMessage(line.Trim());
                }
            }
        }

        private static void ApplyPreset(Players player, string[] args)
        {
            if (args.Length == 0)
            {
                player.SendMessage("Available presets: performance, bandwidth, default");
                return;
            }

            var preset = args[0].ToLower();
            try
            {
                PacketBatchConfig.ApplyPreset(preset);
                player.SendMessage($"Applied preset configuration: {preset}");
            }
            catch (Exception ex)
            {
                player.SendMessage($"Failed to apply preset: {ex.Message}");
            }
        }

        private static string GetOpcodeName(int opcode)
        {
            return opcode switch
            {
                29696 => "Movement",
                26368 => "NPC Info",
                3072 => "Attack",
                _ => "Unknown"
            };
        }
    }
}
