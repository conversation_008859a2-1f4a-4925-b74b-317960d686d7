using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Messaging
{
    /// <summary>
    /// Channel-based message target implementation - thay thế IActorRef
    /// </summary>
    public class ChannelMessageTarget : IMessageTarget, IDisposable
    {
        private readonly Channel<MessageEnvelope<object>> _channel;
        private readonly ChannelWriter<MessageEnvelope<object>> _writer;
        private readonly ChannelReader<MessageEnvelope<object>> _reader;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Task _processingTask;
        private readonly ConcurrentDictionary<Type, Func<object, IMessageContext, Task>> _handlers;
        private volatile bool _isDisposed;

        public string Id { get; }
        public bool IsActive => !_isDisposed && !_cancellationTokenSource.Token.IsCancellationRequested;

        public ChannelMessageTarget(string id, ChannelOptions? options = null)
        {
            Id = id;
            _cancellationTokenSource = new CancellationTokenSource();
            _handlers = new ConcurrentDictionary<Type, Func<object, IMessageContext, Task>>();

            // Tạo channel với options
            var channelOptions = options ?? new BoundedChannelOptions(1000)
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleReader = true,
                SingleWriter = false
            };

            _channel = Channel.CreateBounded<MessageEnvelope<object>>(channelOptions);
            _writer = _channel.Writer;
            _reader = _channel.Reader;

            // Bắt đầu processing task
            _processingTask = ProcessMessagesAsync(_cancellationTokenSource.Token);
        }

        /// <summary>
        /// Đăng ký handler cho message type
        /// </summary>
        public void RegisterHandler<T>(IMessageHandler<T> handler) where T : class
        {
            _handlers[typeof(T)] = async (msg, ctx) =>
            {
                if (msg is T typedMessage)
                {
                    await handler.HandleAsync(typedMessage, ctx);
                }
            };
        }

        /// <summary>
        /// Đăng ký handler với function
        /// </summary>
        public void RegisterHandler<T>(Func<T, IMessageContext, Task> handler) where T : class
        {
            _handlers[typeof(T)] = async (msg, ctx) =>
            {
                if (msg is T typedMessage)
                {
                    await handler(typedMessage, ctx);
                }
            };
        }

        /// <summary>
        /// Gửi message async
        /// </summary>
        public async Task SendAsync<T>(T message, CancellationToken cancellationToken = default) where T : class
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(ChannelMessageTarget));

            var envelope = new MessageEnvelope<object>(message);
            await _writer.WriteAsync(envelope, cancellationToken);
        }

        /// <summary>
        /// Gửi message sync (fire-and-forget)
        /// </summary>
        public void Send<T>(T message) where T : class
        {
            if (_isDisposed)
                return;

            var envelope = new MessageEnvelope<object>(message);
            if (!_writer.TryWrite(envelope))
            {
                // Nếu channel đầy, log warning và drop message
                Logger.Instance.Warning($"Message dropped for target {Id}: channel full");
            }
        }

        /// <summary>
        /// Gửi message với priority
        /// </summary>
        public async Task SendWithPriorityAsync<T>(T message, MessagePriority priority, CancellationToken cancellationToken = default) where T : class
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(ChannelMessageTarget));

            var envelope = new MessageEnvelope<object>(message, null, priority);
            await _writer.WriteAsync(envelope, cancellationToken);
        }

        /// <summary>
        /// Main processing loop
        /// </summary>
        private async Task ProcessMessagesAsync(CancellationToken cancellationToken)
        {
            try
            {
                await foreach (var envelope in _reader.ReadAllAsync(cancellationToken))
                {
                    await ProcessMessageAsync(envelope, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when shutting down
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error in message processing loop for target {Id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý một message
        /// </summary>
        private async Task ProcessMessageAsync(MessageEnvelope<object> envelope, CancellationToken cancellationToken)
        {
            try
            {
                var messageType = envelope.Message.GetType();
                
                if (_handlers.TryGetValue(messageType, out var handler))
                {
                    var context = new MessageContext(envelope.Sender, this, cancellationToken);
                    await handler(envelope.Message, context);
                }
                else
                {
                    // Unhandled message
                    Logger.Instance.Warning($"Unhandled message type {messageType.Name} in target {Id}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error processing message in target {Id}: {ex.Message}");
                
                // Có thể gửi error message đến supervisor
                await HandleErrorAsync(ex, envelope);
            }
        }

        /// <summary>
        /// Xử lý lỗi
        /// </summary>
        private async Task HandleErrorAsync(Exception exception, MessageEnvelope<object> envelope)
        {
            try
            {
                // Có thể implement error handling strategy ở đây
                // Ví dụ: gửi error message đến supervisor, restart, etc.
                
                var errorMessage = new ErrorMessage(exception, envelope.Message as IMessage);
                
                // Log error
                Logger.Instance.Error($"Message processing error in {Id}: {exception}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error in error handler for {Id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Graceful shutdown
        /// </summary>
        public async Task StopAsync(TimeSpan timeout = default)
        {
            if (_isDisposed)
                return;

            try
            {
                // Complete writer để không nhận message mới
                _writer.Complete();

                // Chờ processing task hoàn thành hoặc timeout
                var timeoutTask = timeout == default ? Task.Delay(Timeout.Infinite) : Task.Delay(timeout);
                var completedTask = await Task.WhenAny(_processingTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    Logger.Instance.Warning($"Message target {Id} shutdown timeout");
                    _cancellationTokenSource.Cancel();
                }

                await _processingTask;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error during shutdown of message target {Id}: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_isDisposed)
                return;

            _isDisposed = true;
            
            try
            {
                _writer.Complete();
                _cancellationTokenSource.Cancel();
                _processingTask?.Wait(TimeSpan.FromSeconds(5));
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing message target {Id}: {ex.Message}");
            }
            finally
            {
                _cancellationTokenSource?.Dispose();
            }
        }
    }

    /// <summary>
    /// Message context implementation
    /// </summary>
    internal class MessageContext : IMessageContext
    {
        public IMessageTarget? Sender { get; }
        public IMessageTarget Self { get; }
        public CancellationToken CancellationToken { get; }

        public MessageContext(IMessageTarget? sender, IMessageTarget self, CancellationToken cancellationToken)
        {
            Sender = sender;
            Self = self;
            CancellationToken = cancellationToken;
        }

        public async Task ReplyAsync<T>(T response) where T : class
        {
            if (Sender != null)
            {
                await Sender.SendAsync(response, CancellationToken);
            }
        }
    }
}
