using System;
using Akka.Actor;
using Akka.Hosting;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Quản lý ActorSystem cho toàn bộ <PERSON>ng dụng (chuẩn hóa cho Akka.Hosting)
    /// </summary>
    public class ActorSystemManager
    {
        private readonly ActorSystem _actorSystem;
        private readonly ActorRegistry _actorRegistry;

        public ActorSystemManager(ActorSystem actorSystem, ActorRegistry actorRegistry)
        {
            _actorSystem = actorSystem;
            _actorRegistry = actorRegistry;
        }

        public ActorSystem ActorSystem => _actorSystem;
        public IActorRef TcpManagerActor => _actorRegistry.Get<TcpManagerActor>();
    }
}
