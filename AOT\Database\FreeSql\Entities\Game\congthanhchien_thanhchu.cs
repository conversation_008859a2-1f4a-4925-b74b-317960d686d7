﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class congthanhchien_thanhchu {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string congthanhchien_tenbang { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string tenthanhchu { get; set; }

		[JsonProperty]
		public int? bangphaiid { get; set; }

		[JsonProperty]
		public DateTime? congthanhthoigian { get; set; }

		[JsonProperty]
		public DateTime? congthanhbanthuongthoigian { get; set; }

	}

}
