using FreeSql;
using Microsoft.Extensions.Logging;
using HeroYulgang.Database.FreeSql.Configuration;
using System;
using System.Threading.Tasks;

namespace HeroYulgang.Database.FreeSql.Common
{
    /// <summary>
    /// Base class for all database services
    /// Provides common functionality and error handling
    /// </summary>
    public abstract class DatabaseServiceBase
    {
        protected readonly IFreeSql FreeSql;
        protected readonly ILogger Logger;
        protected readonly DatabaseServiceConfiguration Configuration;

        protected DatabaseServiceBase(
            IFreeSql freeSql, 
            ILogger logger, 
            DatabaseServiceConfiguration configuration)
        {
            FreeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
            Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        /// <summary>
        /// Execute operation with error handling and logging
        /// </summary>
        protected async Task<ServiceResult<T>> ExecuteWithErrorHandlingAsync<T>(
            Func<Task<T>> operation, 
            string operationName,
            object? parameters = null)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                Logger.LogDebug("Starting {OperationName} with parameters: {@Parameters}", 
                    operationName, parameters);

                var result = await operation();
                
                var duration = DateTime.UtcNow - startTime;
                Logger.LogDebug("Completed {OperationName} in {Duration}ms", 
                    operationName, duration.TotalMilliseconds);

                return ServiceResult<T>.Success(result);
            }
            catch (Exception ex)
            {
                var duration = DateTime.UtcNow - startTime;
                Logger.LogError(ex, "Failed {OperationName} after {Duration}ms with parameters: {@Parameters}", 
                    operationName, duration.TotalMilliseconds, parameters);

                return ServiceResult<T>.Failure(ex);
            }
        }

        /// <summary>
        /// Execute operation without return value
        /// </summary>
        protected async Task<ServiceResult> ExecuteWithErrorHandlingAsync(
            Func<Task> operation, 
            string operationName,
            object? parameters = null)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                Logger.LogDebug("Starting {OperationName} with parameters: {@Parameters}", 
                    operationName, parameters);

                await operation();
                
                var duration = DateTime.UtcNow - startTime;
                Logger.LogDebug("Completed {OperationName} in {Duration}ms", 
                    operationName, duration.TotalMilliseconds);

                return ServiceResult.Success();
            }
            catch (Exception ex)
            {
                var duration = DateTime.UtcNow - startTime;
                Logger.LogError(ex, "Failed {OperationName} after {Duration}ms with parameters: {@Parameters}", 
                    operationName, duration.TotalMilliseconds, parameters);

                return ServiceResult.Failure(ex);
            }
        }

        /// <summary>
        /// Execute operation with retry logic
        /// </summary>
        protected async Task<ServiceResult<T>> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            string operationName,
            int maxRetries = 3,
            TimeSpan? retryDelay = null,
            object? parameters = null)
        {
            var delay = retryDelay ?? TimeSpan.FromSeconds(1);
            var lastException = new Exception("Unknown error");

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    if (attempt > 0)
                    {
                        Logger.LogWarning("Retrying {OperationName} (attempt {Attempt}/{MaxRetries})", 
                            operationName, attempt + 1, maxRetries + 1);
                        await Task.Delay(delay);
                    }

                    var result = await ExecuteWithErrorHandlingAsync(operation, operationName, parameters);
                    if (result.IsSuccess)
                    {
                        if (attempt > 0)
                        {
                            Logger.LogInformation("Successfully completed {OperationName} after {Attempt} retries", 
                                operationName, attempt);
                        }
                        return result;
                    }

                    lastException = result.Exception ?? new Exception(result.ErrorMessage);
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    Logger.LogWarning(ex, "Attempt {Attempt} failed for {OperationName}", 
                        attempt + 1, operationName);
                }
            }

            Logger.LogError("All retry attempts failed for {OperationName}", operationName);
            return ServiceResult<T>.Failure(lastException);
        }

        /// <summary>
        /// Validate entity before database operation
        /// </summary>
        protected virtual ServiceResult ValidateEntity<T>(T entity, string operationName)
        {
            if (entity == null)
            {
                return ServiceResult.Failure($"Entity cannot be null for {operationName}");
            }

            // Override in derived classes for specific validation
            return ServiceResult.Success();
        }

        /// <summary>
        /// Log performance metrics
        /// </summary>
        protected void LogPerformanceMetrics(string operationName, TimeSpan duration, int? recordCount = null)
        {
            var metrics = new
            {
                Operation = operationName,
                DurationMs = duration.TotalMilliseconds,
                RecordCount = recordCount,
                Timestamp = DateTime.UtcNow
            };

            if (duration.TotalMilliseconds > 1000) // Log slow operations
            {
                Logger.LogWarning("Slow operation detected: {@Metrics}", metrics);
            }
            else
            {
                Logger.LogDebug("Operation metrics: {@Metrics}", metrics);
            }
        }

    }
}
