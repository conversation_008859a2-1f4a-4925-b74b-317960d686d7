using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Messaging
{
    /// <summary>
    /// Event bus implementation cho publish/subscribe pattern
    /// </summary>
    public class EventBus : IEventBus, IDisposable
    {
        private readonly ConcurrentDictionary<Type, ConcurrentBag<IEventSubscription>> _subscriptions;
        private readonly object _lock = new object();
        private volatile bool _isDisposed;

        public EventBus()
        {
            _subscriptions = new ConcurrentDictionary<Type, ConcurrentBag<IEventSubscription>>();
        }

        /// <summary>
        /// Publish event đến tất cả subscribers
        /// </summary>
        public async Task PublishAsync<T>(T eventData, CancellationToken cancellationToken = default) where T : class
        {
            if (_isDisposed)
                return;

            var eventType = typeof(T);
            
            if (!_subscriptions.TryGetValue(eventType, out var subscriptions))
                return;

            var tasks = new List<Task>();
            
            foreach (var subscription in subscriptions.Where(s => s.IsActive))
            {
                tasks.Add(InvokeSubscriptionAsync(subscription, eventData, cancellationToken));
            }

            if (tasks.Count > 0)
            {
                await Task.WhenAll(tasks);
            }
        }

        /// <summary>
        /// Subscribe với function handler
        /// </summary>
        public IDisposable Subscribe<T>(Func<T, Task> handler) where T : class
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(EventBus));

            var subscription = new FunctionEventSubscription<T>(handler);
            AddSubscription<T>(subscription);
            return subscription;
        }

        /// <summary>
        /// Subscribe với typed handler
        /// </summary>
        public IDisposable Subscribe<T>(IEventHandler<T> handler) where T : class
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(EventBus));

            var subscription = new HandlerEventSubscription<T>(handler);
            AddSubscription<T>(subscription);
            return subscription;
        }

        /// <summary>
        /// Subscribe với message target
        /// </summary>
        public IDisposable Subscribe<T>(IMessageTarget target) where T : class
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(EventBus));

            var subscription = new TargetEventSubscription<T>(target);
            AddSubscription<T>(subscription);
            return subscription;
        }

        /// <summary>
        /// Thêm subscription vào registry
        /// </summary>
        private void AddSubscription<T>(IEventSubscription subscription) where T : class
        {
            var eventType = typeof(T);
            _subscriptions.AddOrUpdate(
                eventType,
                new ConcurrentBag<IEventSubscription> { subscription },
                (key, existing) =>
                {
                    existing.Add(subscription);
                    return existing;
                });
        }

        /// <summary>
        /// Invoke subscription với error handling
        /// </summary>
        private async Task InvokeSubscriptionAsync(IEventSubscription subscription, object eventData, CancellationToken cancellationToken)
        {
            try
            {
                await subscription.InvokeAsync(eventData, cancellationToken);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error in event subscription: {ex.Message}");
                
                // Mark subscription as inactive nếu có lỗi liên tục
                if (subscription is IDisposableSubscription disposable)
                {
                    disposable.MarkInactive();
                }
            }
        }

        /// <summary>
        /// Cleanup inactive subscriptions
        /// </summary>
        public void CleanupInactiveSubscriptions()
        {
            foreach (var kvp in _subscriptions.ToList())
            {
                var activeSubscriptions = kvp.Value.Where(s => s.IsActive).ToList();
                
                if (activeSubscriptions.Count != kvp.Value.Count)
                {
                    _subscriptions.TryUpdate(kvp.Key, new ConcurrentBag<IEventSubscription>(activeSubscriptions), kvp.Value);
                }
            }
        }

        public void Dispose()
        {
            if (_isDisposed)
                return;

            _isDisposed = true;

            // Dispose tất cả subscriptions
            foreach (var subscriptions in _subscriptions.Values)
            {
                foreach (var subscription in subscriptions)
                {
                    if (subscription is IDisposable disposable)
                    {
                        try
                        {
                            disposable.Dispose();
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error($"Error disposing subscription: {ex.Message}");
                        }
                    }
                }
            }

            _subscriptions.Clear();
        }
    }

    /// <summary>
    /// Base interface cho event subscriptions
    /// </summary>
    internal interface IEventSubscription
    {
        bool IsActive { get; }
        Task InvokeAsync(object eventData, CancellationToken cancellationToken);
    }

    /// <summary>
    /// Interface cho disposable subscriptions
    /// </summary>
    internal interface IDisposableSubscription : IEventSubscription, IDisposable
    {
        void MarkInactive();
    }

    /// <summary>
    /// Function-based event subscription
    /// </summary>
    internal class FunctionEventSubscription<T> : IDisposableSubscription where T : class
    {
        private readonly Func<T, Task> _handler;
        private volatile bool _isActive = true;

        public bool IsActive => _isActive;

        public FunctionEventSubscription(Func<T, Task> handler)
        {
            _handler = handler ?? throw new ArgumentNullException(nameof(handler));
        }

        public async Task InvokeAsync(object eventData, CancellationToken cancellationToken)
        {
            if (!_isActive || eventData is not T typedData)
                return;

            await _handler(typedData);
        }

        public void MarkInactive()
        {
            _isActive = false;
        }

        public void Dispose()
        {
            _isActive = false;
        }
    }

    /// <summary>
    /// Handler-based event subscription
    /// </summary>
    internal class HandlerEventSubscription<T> : IDisposableSubscription where T : class
    {
        private readonly IEventHandler<T> _handler;
        private volatile bool _isActive = true;

        public bool IsActive => _isActive;

        public HandlerEventSubscription(IEventHandler<T> handler)
        {
            _handler = handler ?? throw new ArgumentNullException(nameof(handler));
        }

        public async Task InvokeAsync(object eventData, CancellationToken cancellationToken)
        {
            if (!_isActive || eventData is not T typedData)
                return;

            await _handler.HandleEventAsync(typedData, cancellationToken);
        }

        public void MarkInactive()
        {
            _isActive = false;
        }

        public void Dispose()
        {
            _isActive = false;
        }
    }

    /// <summary>
    /// Target-based event subscription
    /// </summary>
    internal class TargetEventSubscription<T> : IDisposableSubscription where T : class
    {
        private readonly IMessageTarget _target;
        private volatile bool _isActive = true;

        public bool IsActive => _isActive && _target.IsActive;

        public TargetEventSubscription(IMessageTarget target)
        {
            _target = target ?? throw new ArgumentNullException(nameof(target));
        }

        public async Task InvokeAsync(object eventData, CancellationToken cancellationToken)
        {
            if (!_isActive || !_target.IsActive || eventData is not T typedData)
                return;

            await _target.SendAsync(typedData, cancellationToken);
        }

        public void MarkInactive()
        {
            _isActive = false;
        }

        public void Dispose()
        {
            _isActive = false;
        }
    }
}
