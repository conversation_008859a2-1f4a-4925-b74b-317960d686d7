using System;
using System.Collections.Generic;
using HeroYulgang.Utils;
namespace RxjhServer.Event
{
	internal class ExtremeArenaPacketFactory
	{
		private class MessageParametes
		{
			public short Paramete1 = 1;
			public short Paramete2 = 1;
			public short Paramete3 = 1;
			public int Paramete4 = 0;
			public int Paramete5 = 0;
			public int Paramete6 = 0;
			public int Paramete7 = 0;
			public int Paramete8 = 0;
		}
		
		private const int _systemGuid = 9999;
		private const int _opcodeMessage = 1328;
		private const int _opcodeReviveCountStatus = 323;
		private const int _opcodeTopThreeRankingPodium = 790;
		private const int _opcodeRankingListOrParticipationRecords = 346;
		private static byte[] GenerateFullPacket(SendingClass stream, MessageParametes msg, int guid)
		{
			stream.Write2(msg.Paramete1);
			stream.Write2(msg.Paramete2);
			stream.Write2(msg.Paramete3);
			stream.Write4(msg.Paramete4);
			stream.Write4(msg.Paramete5);
			stream.Write4(msg.Paramete6);
			stream.Write4(msg.Paramete7);
			stream.Write4(msg.Paramete8);
			return GenerateFullPacket(stream.ToArray(1328, guid));
		}
		public static byte[] SpMessageCompetitionStartsIn3Minutes()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 1,
					Paramete3 = 1,
					Paramete4 = 1,
					Paramete5 = 6679,
					Paramete6 = 50,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageCompetitionStartsIn2Minutes()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 2,
					Paramete3 = 1,
					Paramete4 = 1,
					Paramete5 = 6680,
					Paramete6 = 50,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageRegistrationSuccessful()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 11,
					Paramete3 = 1,
					Paramete4 = 654,
					Paramete5 = 0,
					Paramete6 = 0,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageRegistrationTimeout()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 11,
					Paramete3 = 3,
					Paramete4 = 0,
					Paramete5 = 0,
					Paramete6 = 0,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageRegistrationCriteriaNotMet()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 11,
					Paramete3 = 5,
					Paramete4 = 0,
					Paramete5 = 0,
					Paramete6 = 0,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageRegistrationAlreadySuccessful()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 11,
					Paramete3 = 4,
					Paramete4 = 0,
					Paramete5 = 0,
					Paramete6 = 0,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageCompetitionStartsIn1Minute()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 3,
					Paramete3 = 1,
					Paramete4 = 2,
					Paramete5 = 6681,
					Paramete6 = 50,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageMatchFailed()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 17,
					Paramete3 = 1,
					Paramete4 = 5912,
					Paramete5 = 0,
					Paramete6 = 0,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageUpdateReviveCountStatus(int guid, int reviveCount, bool isShow)
		{
			using (SendingClass SendingClass = new SendingClass())
			{
				SendingClass.Write4(0);
				SendingClass.Write4(0);
				SendingClass.Write4(1000001610);
				SendingClass.Write4(0);
				SendingClass.Write2(118);
				SendingClass.Write2(2);
				SendingClass.Write4(reviveCount);
				SendingClass.Write4(0);
				SendingClass.Write4(isShow ? 1 : 0);
				SendingClass.Write4(2406162017u);
				SendingClass.Write4(0);
				SendingClass.Write4(0);
				return GenerateFullPacket(SendingClass.ToArray(323, guid));
			}
		}
		public static byte[] SpMessageCompetitionCountdown5Seconds()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 14,
					Paramete3 = 1,
					Paramete4 = 45001,
					Paramete5 = 120,
					Paramete6 = 0,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageCompetitionStart()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 5,
					Paramete3 = 2,
					Paramete4 = 0,
					Paramete5 = 0,
					Paramete6 = 0,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageCompetitionCountdownSync(int elapsedSeconds)
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 10,
					Paramete3 = 1,
					Paramete4 = elapsedSeconds,
					Paramete5 = 0,
					Paramete6 = 0,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessagePlayerDeath(int guid)
		{
			using (SendingClass stream = new())
			{
				MessageParametes msg = new()
                {
					Paramete1 = 1,
					Paramete2 = 7,
					Paramete3 = 2,
					Paramete4 = 3,
					Paramete5 = 6677,
					Paramete6 = 5,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageCompetitionEndAnimation(bool isVictory, int points)
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = (short)(isVictory ? 12 : 13),
					Paramete3 = 1,
					Paramete4 = (isVictory ? 6675 : 6676),
					Paramete5 = points,
					Paramete6 = 0,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageVictoryAdditional()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 16,
					Paramete3 = 1,
					Paramete4 = 20,
					Paramete5 = 0,
					Paramete6 = 0,
					Paramete7 = 900000842,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageFailureAdditional()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 16,
					Paramete3 = 1,
					Paramete4 = 5,
					Paramete5 = 0,
					Paramete6 = 0,
					Paramete7 = 900000842,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpMessageRemindPlayerToExit()
		{
			using (SendingClass stream = new SendingClass())
			{
				int guid = 9999;
				MessageParametes msg = new MessageParametes
				{
					Paramete1 = 1,
					Paramete2 = 6,
					Paramete3 = 1,
					Paramete4 = 4,
					Paramete5 = 6678,
					Paramete6 = 10,
					Paramete7 = 0,
					Paramete8 = 0
				};
				return GenerateFullPacket(stream, msg, guid);
			}
		}
		public static byte[] SpTopThreeRankingPodium(int guid, List<IExtremeArenaRanking> rankings)
		{
			using (SendingClass SendingClass = new SendingClass())
			{
				for (int i = 0; i < rankings.Count; i++)
				{
					SendingClass.WriteString(rankings[i].CharacterName, 15);
					SendingClass.Write4(rankings[i].GuildId);
					SendingClass.WriteString(rankings[i].GuildName, 15);
					SendingClass.Write1(0);
					SendingClass.Write1(0);
					SendingClass.Write1(0);
					SendingClass.Write1(rankings[i].Faction);
					SendingClass.Write1(49);
					SendingClass.Write1(rankings[i].Job_level);
					SendingClass.Write1(rankings[i].Player_Job);
					SendingClass.Write1(1);
					SendingClass.Write1(222);
					SendingClass.Write1(243);
					SendingClass.Write1(1);
					SendingClass.WriteString("", 9);
					SendingClass.Write1(1);
					SendingClass.Write1(rankings[i].Player_Sex);
					SendingClass.WriteString("", 16);
					SendingClass.Write8(200303078L);
					SendingClass.Write8(200503078L);
					SendingClass.Write8(200503078L);
					SendingClass.Write8(200803078L);
					SendingClass.Write8(200200368L);
					SendingClass.WriteString("", 24);
					SendingClass.Write1(14);
					SendingClass.Write4((rankings[i].Player_Sex == 1) ? 16903191 : 26903191);
					SendingClass.WriteString("", 41);
					SendingClass.Write1(8);
					SendingClass.Write1(25);
					SendingClass.WriteString("", 95);
					SendingClass.Write1(1);
					SendingClass.Write4(8);
					SendingClass.Write4(8);
					SendingClass.WriteString("", 70);
					SendingClass.Write4(rankings[i].Ranking);
					SendingClass.WriteString("", 83);
				}
				return GenerateFullPacket(SendingClass.ToArray(790, guid));
			}
		}
		public static byte[] SpRankingList(int guid, List<IExtremeArenaRanking> rankings)
		{
			using (SendingClass SendingClass = new SendingClass())
			{
				SendingClass.Write4(rankings.Count);
				SendingClass.Write(0);
				SendingClass.Write(2);
				SendingClass.Write2(7);
				SendingClass.Write4(1);
				for (int i = 0; i < rankings.Count; i++)
				{
					SendingClass.WriteString(rankings[i].CharacterName, 15);
					SendingClass.WriteString(rankings[i].GuildName, 15);
					SendingClass.Write2(rankings[i].Player_Job);
					SendingClass.Write2(rankings[i].Job_level);
					SendingClass.Write2(rankings[i].Faction);
					SendingClass.Write4(rankings[i].Player_Level);
					SendingClass.Write8(rankings[i].ArenaPoint);
					SendingClass.Write4(rankings[i].Ranking);
					SendingClass.Write4(0);
				}
				return GenerateFullPacket(SendingClass.ToArray(346, guid));
			}
		}
		public static byte[] SpParticipationRecords(int guid)
		{
			using (SendingClass SendingClass = new SendingClass())
			{
				SendingClass.Write4(0);
				SendingClass.Write1(253);
				SendingClass.Write1(2);
				SendingClass.Write1(7);
				SendingClass.Write1(11);
				SendingClass.Write4(2);
				return GenerateFullPacket(SendingClass.ToArray(346, guid));
			}
		}
		private static byte[] GenerateFullPacket(byte[] data)
		{
			List<byte> list = new List<byte>();
			list.AddRange(BitConverter.GetBytes((ushort)21930));
			list.AddRange(BitConverter.GetBytes((ushort)data.Length));
			list.AddRange(data);
			list.AddRange(BitConverter.GetBytes((ushort)43605));
			return list.ToArray();
		}
	}
}
