namespace RxjhServer;

public class X_Khi_Cong_Thuoc_Tinh
{
	private double double_0;

	private double double_1;

	private double double_2;

	private double double_3;

	private double double_4;

	private double double_5;

	private double double_6;

	private double double_7;

	private double double_8;

	private double double_9;

	private double double_10;

	private double double_11;

	private double double_12;

	private double double_13;

	private double double_14;

	private double double_15;

	private double double_16;

	private double double_17;

	private double double_18;

	private double double_18_BB_TH;

	private double double_19;

	private double double_20;

	private double double_21;

	private double double_22;

	private double double_23;

	private double double_24;

	private double double_25;

	private double double_26;

	private double double_27;

	private double double_28;

	private double double_29;

	private double double_30;

	private double double_31;

	private double double_32;

	private double double_33;

	private double double_34;

	private double double_35;

	private double double_36;

	private double double_37;

	private double double_38;

	private double double_39;

	private double double_3999;

	private double double_40;

	private double double_41;

	private double double_42;

	private double double_43;

	private double double_44;

	private double double_45;

	private double double_46;

	private double double_47;

	private double double_48;

	private double double_49;

	private double double_50;

	private double double_51;

	private double double_52;

	private double double_53;

	private double double_54;

	private double double_55;

	private double double_56;

	private double double_57;

	private double double_58;

	private double double_59;

	private double double_60;

	private double double_61;

	private double double_62;

	private double double_63;

	private double double_64;

	private double double_65;

	private double double_66;

	private double double_67;

	private double double_68;

	private double double_69;

	private double double_70;

	private double double_71;

	private double double_72;

	private double double_73;

	private double double_74;

	private double double_75;

	private double double_75_BB_TH;

	private double double_76;

	private double double_77;

	private double double_78;

	private double double_79;

	private double double_80;

	private double double_81;

	private double double_82;

	private double double_83;

	private double double_84;

	private double double_85;

	private double double_86;

	private double double_87;

	private double double_88;

	private double double_89;

	private double double_90;

	private double double_91;

	private double double_92;

	private double double_93;

	private double double_94;

	private double double_95;

	private double double_96;

	private double double_97;

	private double double_98;

	private double double_99;

	private double double_100;

	private double double_101;

	private double double_102;

	private double double_103;

	private double double_104;

	private double double_105;

	private double double_106;

	private double double_107;

	private double double_108;

	private double double_109;

	private double double_110;

	private double double_111;

	private double double_112;

	private double double_113;

	private double double_114;

	private double double_115;

	private double double_116;

	private double double_117;

	private double double_118;

	private double double_120;

	private double double_121;

	private double double_122;

	private double double_123;

	private double double_124;

	private double double_125;

	private double double_126;

	private double double_127;

	private double double_128;

	private double double_129;

	private double double_130;

	private double double_131;

	private double double_132;

	private double double_133;

	private double double_134;

	private double double_135;

	private double double_136;

	private double double_137;

	private double double_138;

	private double double_139;

	private double double_140;

	private double double_141;

	private double double_142;

	private double double_143;

	private double double_144;

	private double double_145;

	private double double_146;

	private double double_147;

	private double double_148;

	private double double_149;

	private double double_150;

	private double double_151;

	private double double_152;

	private double double_153;

	private double double_154;

	private double double_155;

	private double double_156;

	private double double_157;

	private double double_158;

	private double double_159;

	private double double_160;

	private double double_161;

	private double double_162;

	private double double_163;

	private double double_164;

	private double double_165;

	private double double_166;

	private double double_167;

	private double double_168;

	private double double_169;

	private double double_170;

	private double double_171;

	private double double_172;

	public double _TuHao_ChuyenCongViThu;

	public double _ThanNu_VanKhiHanhTam;

	public double _ThanNu_ThaiCucTamPhap;

	public double _ThanNu_ThanLucKichPhat;

	public double _ThanNu_SatTinhNghiaHo;

	public double _ThanNu_SatTinhNghiaSat;

	public double _ThanNu_SatTinhNghiaKhi;

	public double _ThanNu_TayTuyDichCan;

	public double _ThanNu_HacHoaManKhai;

	public double _ThanNu_DieuThuHoiXuan;

	public double _ThanNu_TruongCongKichLuc;

	public double _ThanNu_HacHoaTapTrung;

	public double _ThanNu_ChanVu_TuyetKich;

	public double _ThanNu_VanDocBatXam;

	public double _ThanNu_PhanNoDieuTiet;

	public double _ThanNu_CoDocGiaiTru;

	public double _ThanNu_ThanLucBaoHo;

	public double _ThanNu_ThiDocBaoPhat;

	public double _Ve_Rong_Diem_Mat;

	public double _Bach_Doc_Bat_Xam;

	public double _Han_Bang_Linh_Vuc;

	public double _Ac_Tan_Mui_Ten_Ngheo;

	public double _Van_Tam_Nguyet_Tinh;

	public double _Ben_Ngoai_Vua_Ben_Trong_Vua;

	public double _Huyet_Mach_Len_Cao;

	public double _Chan_Khi_Hoan_Nguyen;

	public double _Dien_Quang_Suong_Mai;

	public double _Khong_Chuong_Ngai_Vat;

	public double _Bien_Nguy_Thanh_An;

	public double _Ban_Nguoc_Vo_Hieu;

	public double _Chong_Lai_Than_Phap;

	public double _Huyet_Khi_Phuong_Cuong;

	public double _Tinh_Kim_Bach_Luyen;

	public double _Giam_Bot_CongKich;

	public double _Giam_Bot_CuongKhi;

	public double _PhanKhiCong_17x_NhanKiem_NhatThe_Kiem;

	public double _PhanKhiCong_17x_HoThan_CanhKhi_Kiem;

	public double _PhanKhiCong_17x_TuLuong_ThienKim_Dao;

	public double _PhanKhiCong_17x_BaKhi_PhaGiap_Dao;

	public double _PhanKhiCong_17x_HongNguyen_CuongPhong_Thuong;

	public double _PhanKhiCong_17x_DiaPhan_XungKhi_Thuong;

	public double _PhanKhiCong_17x_VoAnh_AmTien_Cung;

	public double _PhanKhiCong_17x_DocBa_GiangHo_Cung;

	public double _PhanKhiCong_17x_BachSu_NhuY_DaiPhu;

	public double _PhanKhiCong_17x_VanTam_NguyetTinh_DaiPhu;

	public double _PhanKhiCong_17x_LietNhat_DiemDiem_ThichKhach;

	public double _PhanKhiCong_17x_NguKhi_XungTieu_ThichKhach;

	public double _PhanKhiCong_17x_KichLuc_CongThanh_CamSu;

	public double _PhanKhiCong_17x_TienCam_TamPhap_CamSu;

	public double _PhanKhiCong_17x_ThienMa_QuangHuyet_HBQ;

	public double _PhanKhiCong_17x_BaKhi_PhaGiap_HBQ;

	public double _PhanKhiCong_17x_NhuKhac_Cuong_DHL;

	public double _PhanKhiCong_17x_TuNhan_DaiPhap_DHL;

	public double _PhanKhiCong_17x_NoTam_XuatKich_QuyenSu;

	public double _PhanKhiCong_17x_DienQuang_ThachHoa_QuyenSu;

	public double _PhanKhiCong_17x_NoKhi_XungThien_MLC;

	public double _PhanKhiCong_17x_HuyenVu_LoiDien_MLC;

	public double _PhanKhiCong_17x_DiTinh_VanThien_TH;

	public double _PhanKhiCong_17x_DiCong_ViThu_TH;

	public double _PhanKhiCong_17x_HacHoa_TapTrung_TN;

	public double _PhanKhiCong_17x_ThiDoc_BaoPhat_TN;

	public double _PhanKhiCong_17x_VongAm_CoDoc_ALL;

	public double DAO_LienHoanPhiVu
	{
		get
		{
			return double_0;
		}
		set
		{
			double_0 = value;
		}
	}

	public double HanBaoQuan_ThienMaCuongHuyet
	{
		get
		{
			return double_1;
		}
		set
		{
			double_1 = value;
		}
	}

	public double HanBaoQuan_TruyCotHapNguyen
	{
		get
		{
			return double_2;
		}
		set
		{
			double_2 = value;
		}
	}

	public double HanBaoQuan_HoaLongVanDinh
	{
		get
		{
			return double_3;
		}
		set
		{
			double_3 = value;
		}
	}

	public double DamHoaLien_ChieuThucTanPhap
	{
		get
		{
			return double_4;
		}
		set
		{
			double_4 = value;
		}
	}

	public double NhatKich_TriMang_TiLe
	{
		get
		{
			return double_5;
		}
		set
		{
			double_5 = value;
		}
	}

	public double DAO_ThangThien_3_KhiCong_HoaLong_ChiHoa
	{
		get
		{
			return double_6;
		}
		set
		{
			double_6 = value;
		}
	}

	public double KIEM_PhaThien_NhatKiem
	{
		get
		{
			return double_7;
		}
		set
		{
			double_7 = value;
		}
	}

	public double THUONG_ThangThien_3_KhiCong_NoYChiHoa
	{
		get
		{
			return double_8;
		}
		set
		{
			double_8 = value;
		}
	}

	public double QuaiVat_PhanSatThuong_TiLe
	{
		get
		{
			return double_9;
		}
		set
		{
			double_9 = value;
		}
	}

	public double NguoiChoi_PhanSatThuong_Tile
	{
		get
		{
			return double_10;
		}
		set
		{
			double_10 = value;
		}
	}

	public double PhaGiap_TiLe
	{
		get
		{
			return double_11;
		}
		set
		{
			double_11 = value;
		}
	}

	public double ChanVu_TuyetKich
	{
		get
		{
			return double_12;
		}
		set
		{
			double_12 = value;
		}
	}

	public double ThanCo_MinhChau
	{
		get
		{
			return double_13;
		}
		set
		{
			double_13 = value;
		}
	}

	public double AmAnh_TuyetSat
	{
		get
		{
			return double_14;
		}
		set
		{
			double_14 = value;
		}
	}

	public double LuuQuang_LoanVu
	{
		get
		{
			return double_15;
		}
		set
		{
			double_15 = value;
		}
	}

	public double KIEM_ThangThien_1_KhiCong_HoThan_CuongKhi
	{
		get
		{
			return double_16;
		}
		set
		{
			double_16 = value;
		}
	}

	public double KIEM_DiHoa_TiepMoc
	{
		get
		{
			return double_17;
		}
		set
		{
			double_17 = value;
		}
	}

	public double KIEM_HoiLieu_ThanPhap
	{
		get
		{
			return double_18;
		}
		set
		{
			double_18 = value;
		}
	}

	public double KIEM_BachBien_ThanHanh
	{
		get
		{
			return double_18_BB_TH;
		}
		set
		{
			double_18_BB_TH = value;
		}
	}

	public double KIEM_NoHai_CuongLan
	{
		get
		{
			return double_19;
		}
		set
		{
			double_19 = value;
		}
	}

	public double KIEM_TrungQuan_NhatNo
	{
		get
		{
			return double_20;
		}
		set
		{
			double_20 = value;
		}
	}

	public double KIEM_NhanKiem_NhatThe
	{
		get
		{
			return double_21;
		}
		set
		{
			double_21 = value;
		}
	}

	public double THUONG_VanKhi_LieuThuong
	{
		get
		{
			return double_22;
		}
		set
		{
			double_22 = value;
		}
	}

	public double THUONG_LinhGiapHoThan
	{
		get
		{
			return double_23;
		}
		set
		{
			double_23 = value;
		}
	}

	public double THUONG_CanKhonNaDi
	{
		get
		{
			return double_24;
		}
		set
		{
			double_24 = value;
		}
	}

	public double THUONG_CuongThanHangThe
	{
		get
		{
			return double_25;
		}
		set
		{
			double_25 = value;
		}
	}

	public double LuuTinhManThien
	{
		get
		{
			return double_26;
		}
		set
		{
			double_26 = value;
		}
	}

	public double TuHao_ChuyenCongViThu
	{
		get
		{
			return _TuHao_ChuyenCongViThu;
		}
		set
		{
			_TuHao_ChuyenCongViThu = value;
		}
	}

	public double THUONG_ChuyenCongViThu
	{
		get
		{
			return double_27;
		}
		set
		{
			double_27 = value;
		}
	}

	public double THUONG_MatNhatCuongVu
	{
		get
		{
			return double_28;
		}
		set
		{
			double_28 = value;
		}
	}

	public double CUNG_NhueLoiChiTien
	{
		get
		{
			return double_29;
		}
		set
		{
			double_29 = value;
		}
	}

	public double CUNG_LiepUngChiNhan
	{
		get
		{
			return double_30;
		}
		set
		{
			double_30 = value;
		}
	}

	public double CUNG_VoMinhAmThi
	{
		get
		{
			return double_31;
		}
		set
		{
			double_31 = value;
		}
	}

	public double DAIPHU_VanKhiLieuTam
	{
		get
		{
			return double_32;
		}
		set
		{
			double_32 = value;
		}
	}

	public double DAIPHU_TruongCongKichLuc
	{
		get
		{
			return double_33;
		}
		set
		{
			double_33 = value;
		}
	}

	public double DAIPHU_ThaiCucTamPhap
	{
		get
		{
			return double_34;
		}
		set
		{
			double_34 = value;
		}
	}

	public double DAIPHU_DieuThuHoiXuan
	{
		get
		{
			return double_35;
		}
		set
		{
			double_35 = value;
		}
	}

	public double DAIPHU_ThanNongTienThuat
	{
		get
		{
			return double_36;
		}
		set
		{
			double_36 = value;
		}
	}

	public double DAIPHU_CuuThienChanKhi
	{
		get
		{
			return double_37;
		}
		set
		{
			double_37 = value;
		}
	}

	public double DAIPHU_ThangThien_2_KhiCong_VanVatHoiXuan
	{
		get
		{
			return double_38;
		}
		set
		{
			double_38 = value;
		}
	}

	public double DAIPHU_HapTinhDaiPhap
	{
		get
		{
			return double_39;
		}
		set
		{
			double_39 = value;
		}
	}

	public double DaiPhu_TT5_HapTheLieuThuong
	{
		get
		{
			return double_3999;
		}
		set
		{
			double_3999 = value;
		}
	}

	public double NINJA_KinhKhaChiNo
	{
		get
		{
			return double_40;
		}
		set
		{
			double_40 = value;
		}
	}

	public double NINJA_TamHoaTuDinh
	{
		get
		{
			return double_41;
		}
		set
		{
			double_41 = value;
		}
	}

	public double NINJA_LienHoanPhiVu
	{
		get
		{
			return double_42;
		}
		set
		{
			double_42 = value;
		}
	}

	public double CuongPhong_VanPha
	{
		get
		{
			return double_43;
		}
		set
		{
			double_43 = value;
		}
	}

	public double KIEM_LienHoanPhiVu
	{
		get
		{
			return double_44;
		}
		set
		{
			double_44 = value;
		}
	}

	public double THUONG_LienHoanPhiVu
	{
		get
		{
			return double_45;
		}
		set
		{
			double_45 = value;
		}
	}

	public double NINJA_TatSatNhatKich
	{
		get
		{
			return double_46;
		}
		set
		{
			double_46 = value;
		}
	}

	public double CUNG_HoiLuuChanKhi
	{
		get
		{
			return double_47;
		}
		set
		{
			double_47 = value;
		}
	}

	public double CUNG_TamThanNgungTu
	{
		get
		{
			return double_48;
		}
		set
		{
			double_48 = value;
		}
	}

	public double CUNG_LuuTinhTamThi
	{
		get
		{
			return double_49;
		}
		set
		{
			double_49 = value;
		}
	}

	public double CUNG_TriMenhTuyetSat
	{
		get
		{
			return double_50;
		}
		set
		{
			double_50 = value;
		}
	}

	public double NINJA_TamThanNgungTu
	{
		get
		{
			return double_51;
		}
		set
		{
			double_51 = value;
		}
	}

	public bool IsTriggerTamThanNgungTu { get; set; }

	public double NINJA_TriThuTuyetMenh
	{
		get
		{
			return double_52;
		}
		set
		{
			double_52 = value;
		}
	}

	public double NINJA_TienPhatCheNhan
	{
		get
		{
			return double_53;
		}
		set
		{
			double_53 = value;
		}
	}

	public double NINJA_ThienChuVanThu
	{
		get
		{
			return double_54;
		}
		set
		{
			double_54 = value;
		}
	}

	public double NINJA_LienTieuDaiDa
	{
		get
		{
			return double_55;
		}
		set
		{
			double_55 = value;
		}
	}

	public double NINJA_KhoaiDaoLoanVu
	{
		get
		{
			return double_56;
		}
		set
		{
			double_56 = value;
		}
	}

	public double NINJA_NhatChieuTanSat
	{
		get
		{
			return double_57;
		}
		set
		{
			double_57 = value;
		}
	}

	public double NINJA_ThangThien_3_KhiCong_VoTinhDaKich
	{
		get
		{
			return double_58;
		}
		set
		{
			double_58 = value;
		}
	}

	public double CAMSU_TamHoaHuyen_XacXuat_XuatHien
	{
		get
		{
			return double_59;
		}
		set
		{
			double_59 = value;
		}
	}

	public double CAMSU_ChienMaBonDang
	{
		get
		{
			return double_60;
		}
		set
		{
			double_60 = value;
		}
	}

	public double CAMSU_ThuGiangDaBac
	{
		get
		{
			return double_61;
		}
		set
		{
			double_61 = value;
		}
	}

	public double CAMSU_ThanhTamPhoThien
	{
		get
		{
			return double_62;
		}
		set
		{
			double_62 = value;
		}
	}

	public double CAMSU_DuongQuanTamDiep
	{
		get
		{
			return double_63;
		}
		set
		{
			double_63 = value;
		}
	}

	public double CAMSU_HanCungThuNguyet
	{
		get
		{
			return double_64;
		}
		set
		{
			double_64 = value;
		}
	}

	public double CAMSU_CaoSonLuuThuy
	{
		get
		{
			return double_65;
		}
		set
		{
			double_65 = value;
		}
	}

	public double CAMSU_NhacDuongTamTuy
	{
		get
		{
			return double_66;
		}
		set
		{
			double_66 = value;
		}
	}

	public double CAMSU_MaiHoaTamLong
	{
		get
		{
			return double_67;
		}
		set
		{
			double_67 = value;
		}
	}

	public double CAMSU_LoanPhuongHoaMinh
	{
		get
		{
			return double_68;
		}
		set
		{
			double_68 = value;
		}
	}

	public double CAMSU_DuongMinhXuanHieu
	{
		get
		{
			return double_69;
		}
		set
		{
			double_69 = value;
		}
	}

	public double CAMSU_TieuTuongVuDa
	{
		get
		{
			return double_70;
		}
		set
		{
			double_70 = value;
		}
	}

	public double DamHoaLien_LienHoanPhiVu
	{
		get
		{
			return double_71;
		}
		set
		{
			double_71 = value;
		}
	}

	public double DamHoaLien_HoThan_CuongKhi
	{
		get
		{
			return double_72;
		}
		set
		{
			double_72 = value;
		}
	}

	public double DamHoaLien_DiHoa_TiepMoc
	{
		get
		{
			return double_73;
		}
		set
		{
			double_73 = value;
		}
	}

	public double DamHoaLien_TungHoanhVoSong
	{
		get
		{
			return double_74;
		}
		set
		{
			double_74 = value;
		}
	}

	public double DamHoaLien_HoiLieu_ThanPhap
	{
		get
		{
			return double_75;
		}
		set
		{
			double_75 = value;
		}
	}

	public double DamHoaLien_BachBien_ThanHanh
	{
		get
		{
			return double_75_BB_TH;
		}
		set
		{
			double_75_BB_TH = value;
		}
	}

	public double DamHoaLien_NoHai_CuongLan
	{
		get
		{
			return double_76;
		}
		set
		{
			double_76 = value;
		}
	}

	public double DamHoaLien_TrungQuan_NhatNo
	{
		get
		{
			return double_77;
		}
		set
		{
			double_77 = value;
		}
	}

	public double QuyenSu_CuongThanHangThe
	{
		get
		{
			return double_78;
		}
		set
		{
			double_78 = value;
		}
	}

	public double QuyenSu_LucPhachHoaSon
	{
		get
		{
			return double_79;
		}
		set
		{
			double_79 = value;
		}
	}

	public double QuyenSu_KimCuongBatHoai
	{
		get
		{
			return double_80;
		}
		set
		{
			double_80 = value;
		}
	}

	public double QuyenSu_ChuyenCongViThu
	{
		get
		{
			return double_81;
		}
		set
		{
			double_81 = value;
		}
	}

	public double QuyenSu_ThuyHoaNhatThe
	{
		get
		{
			return double_82;
		}
		set
		{
			double_82 = value;
		}
	}

	public double QuyenSu_NoTamXuatKich
	{
		get
		{
			return double_83;
		}
		set
		{
			double_83 = value;
		}
	}

	public double QuyenSu_MaXuThanhCham
	{
		get
		{
			return double_84;
		}
		set
		{
			double_84 = value;
		}
	}

	public double QuyenSu_MatNhatCuongVu
	{
		get
		{
			return double_85;
		}
		set
		{
			double_85 = value;
		}
	}

	public double QuyenSu_ThangThien_DienQuangThachHoa
	{
		get
		{
			return double_86;
		}
		set
		{
			double_86 = value;
		}
	}

	public double QuyenSu_ThangThien_DoatMenhLienHoan
	{
		get
		{
			return double_87;
		}
		set
		{
			double_87 = value;
		}
	}

	public double QuyenSu_ThangThien_TinhIchCauTinh
	{
		get
		{
			return double_88;
		}
		set
		{
			double_88 = value;
		}
	}

	public double MaiLieuChan_ChuongLucKichHoat
	{
		get
		{
			return double_89;
		}
		set
		{
			double_89 = value;
		}
	}

	public double MaiLieuChan_ChuongLucVanDung
	{
		get
		{
			return double_90;
		}
		set
		{
			double_90 = value;
		}
	}

	public double MaiLieuChan_BachBien_ThanHanh
	{
		get
		{
			return double_91;
		}
		set
		{
			double_91 = value;
		}
	}

	public double MaiLieuChan_HuyenVuThanCong
	{
		get
		{
			return double_92;
		}
		set
		{
			double_92 = value;
		}
	}

	public double MaiLieuChan_HuyenVuDichChiDiem
	{
		get
		{
			return double_93;
		}
		set
		{
			double_93 = value;
		}
	}

	public double MaiLieuChan_HuyenVuCuongKich
	{
		get
		{
			return double_94;
		}
		set
		{
			double_94 = value;
		}
	}

	public double MaiLieuChan_HuyenVuNguyHoa
	{
		get
		{
			return double_95;
		}
		set
		{
			double_95 = value;
		}
	}

	public double MaiLieuChan_ChuongLucKhoiPhuc
	{
		get
		{
			return double_96;
		}
		set
		{
			double_96 = value;
		}
	}

	public double MaiLieuChan_TatDoDichHoaThan
	{
		get
		{
			return double_97;
		}
		set
		{
			double_97 = value;
		}
	}

	public double MaiLieuChan_PhanNoBaoPhat
	{
		get
		{
			return double_98;
		}
		set
		{
			double_98 = value;
		}
	}

	public double MaiLieuChan_HapHuyetTienKich
	{
		get
		{
			return double_99;
		}
		set
		{
			double_99 = value;
		}
	}

	public double HanBaoQuan_ThangThien_1_KhiCong_HanhPhongLongVu
	{
		get
		{
			return double_100;
		}
		set
		{
			double_100 = value;
		}
	}

	public double HanBaoQuan_ThangThien_2_KhiCong_ThienMaHoThe
	{
		get
		{
			return double_101;
		}
		set
		{
			double_101 = value;
		}
	}

	public double HanBaoQuan_ThangThien_3_KhiCong_NoiTucHanhTam
	{
		get
		{
			return double_102;
		}
		set
		{
			double_102 = value;
		}
	}

	public double DAO_ThangThien_1_KhiCong_DonXuatNghichCanh
	{
		get
		{
			return double_103;
		}
		set
		{
			double_103 = value;
		}
	}

	public double KIEM_HonNguyen_KiemPhap
	{
		get
		{
			return double_104;
		}
		set
		{
			double_104 = value;
		}
	}

	public double THUONG_ThangThien_1_KhiCong_PhaGiapThuHon
	{
		get
		{
			return double_105;
		}
		set
		{
			double_105 = value;
		}
	}

	public double CUNG_ThangThien_1_KhiCong_TuyetAnhXaHon
	{
		get
		{
			return double_106;
		}
		set
		{
			double_106 = value;
		}
	}

	public double DAIPHU_ThangThien_1_KhiCong_HoThanKhiGiap
	{
		get
		{
			return double_107;
		}
		set
		{
			double_107 = value;
		}
	}

	public double DAIPHU_HoanVuVanHoa
	{
		get
		{
			return double_108;
		}
		set
		{
			double_108 = value;
		}
	}

	public double NINJA_ThangThien_1_KhiCong_DaMaTrienThan
	{
		get
		{
			return double_109;
		}
		set
		{
			double_109 = value;
		}
	}

	public double ThangThien_1_KhiCong_LucPhachHoaSon
	{
		get
		{
			return double_110;
		}
		set
		{
			double_110 = value;
		}
	}

	public double ThangThien_1_KhiCong_TruongHong_QuanNhat
	{
		get
		{
			return double_111;
		}
		set
		{
			double_111 = value;
		}
	}

	public double ThangThien_1_KhiCong_KimChungCuongKhi
	{
		get
		{
			return double_112;
		}
		set
		{
			double_112 = value;
		}
	}

	public double ThangThien_1_KhiCong_VanKhiHanhTam
	{
		get
		{
			return double_113;
		}
		set
		{
			double_113 = value;
		}
	}

	public double ThangThien_1_KhiCong_ChinhBanBoiNguyen
	{
		get
		{
			return double_114;
		}
		set
		{
			double_114 = value;
		}
	}

	public double ThangThien_1_KhiCong_VanKhi_LieuThuong
	{
		get
		{
			return double_115;
		}
		set
		{
			double_115 = value;
		}
	}

	public double ThangThien_1_KhiCong_BachBien_ThanHanh
	{
		get
		{
			return double_116;
		}
		set
		{
			double_116 = value;
		}
	}

	public double ThangThien_1_KhiCong_CuongPhongThienY
	{
		get
		{
			return double_117;
		}
		set
		{
			double_117 = value;
		}
	}

	public double CAMSU_ThangThien_1_KhiCong_PhiHoaDiemThuy
	{
		get
		{
			return double_118;
		}
		set
		{
			double_118 = value;
		}
	}

	public double Quyen_ThangThien_1_KhiCong_DoatMenhLienHoan
	{
		get
		{
			return double_120;
		}
		set
		{
			double_120 = value;
		}
	}

	public double Quyen_ThangThien_2_KhiCong_DienQuangThachHoa
	{
		get
		{
			return double_121;
		}
		set
		{
			double_121 = value;
		}
	}

	public double Quyen_ThangThien_3_KhiCong_TinhIchCauTinh
	{
		get
		{
			return double_122;
		}
		set
		{
			double_122 = value;
		}
	}

	public double MaiLieuChan_ThangThien_1_KhiCong_HuyenVuLoiDien
	{
		get
		{
			return double_123;
		}
		set
		{
			double_123 = value;
		}
	}

	public double MaiLieuChan_ThangThien_2_KhiCong_HuyenVuTroChu
	{
		get
		{
			return double_124;
		}
		set
		{
			double_124 = value;
		}
	}

	public double MaiLieuChan_ThangThien_3_KhiCong_QuySatNhan
	{
		get
		{
			return double_125;
		}
		set
		{
			double_125 = value;
		}
	}

	public double DAO_ThangThien_2_KhiCong_CungDoMatLo
	{
		get
		{
			return double_126;
		}
		set
		{
			double_126 = value;
		}
	}

	public double KIEM_ThangThien_2_KhiCong_ThienDiaDongTho
	{
		get
		{
			return double_127;
		}
		set
		{
			double_127 = value;
		}
	}

	public double DamHoaLien_ThangThien_2_KhiCong_ThienDiaDongTho
	{
		get
		{
			return double_128;
		}
		set
		{
			double_128 = value;
		}
	}

	public double THUONG_ThangThien_2_KhiCong_DiThoiViTien
	{
		get
		{
			return double_129;
		}
		set
		{
			double_129 = value;
		}
	}

	public double CUNG_ThangThien_2_KhiCong_ThienQuanApDa
	{
		get
		{
			return double_130;
		}
		set
		{
			double_130 = value;
		}
	}

	public double DAIPHU_VoTrungSinhHuu
	{
		get
		{
			return double_131;
		}
		set
		{
			double_131 = value;
		}
	}

	public double NINJA_ThangThien_2_KhiCong_ThuanThuyThoiChu
	{
		get
		{
			return double_132;
		}
		set
		{
			double_132 = value;
		}
	}

	public double CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet
	{
		get
		{
			return double_133;
		}
		set
		{
			double_133 = value;
		}
	}

	public double DamHoaLien_ThangThien_2_KhiCong_TungHoanhVoSong
	{
		get
		{
			return double_134;
		}
		set
		{
			double_134 = value;
		}
	}

	public double NINJA_DiNoHoanNo
	{
		get
		{
			return double_135;
		}
		set
		{
			double_135 = value;
		}
	}

	public double DAO_ManhLongSatTran
	{
		get
		{
			return double_136;
		}
		set
		{
			double_136 = value;
		}
	}

	public double KIEM_ThangThien_3_KhiCong_HoaPhuongLamTrieu
	{
		get
		{
			return double_137;
		}
		set
		{
			double_137 = value;
		}
	}

	public double DamHoaLien_ThangThien_3_KhiCong_HoaPhuongLamTrieu
	{
		get
		{
			return double_138;
		}
		set
		{
			double_138 = value;
		}
	}

	public double THUONG_NoYChiHong
	{
		get
		{
			return double_139;
		}
		set
		{
			double_139 = value;
		}
	}

	public double CongPhaNhuocDiem
	{
		get
		{
			return double_140;
		}
		set
		{
			double_140 = value;
		}
	}

	public double KhongGiPhaNoi
	{
		get
		{
			return double_141;
		}
		set
		{
			double_141 = value;
		}
	}

	public double LangKinhToiLuyen
	{
		get
		{
			return double_142;
		}
		set
		{
			double_142 = value;
		}
	}

	public double TuHao_PhaHuyenCuongPhong
	{
		get
		{
			return double_143;
		}
		set
		{
			double_143 = value;
		}
	}

	public double KyQuan_QuanHung
	{
		get
		{
			return double_144;
		}
		set
		{
			double_144 = value;
		}
	}

	public double CUNG_ThangThien_3_KhiCong_ThienNgoaiTamThi
	{
		get
		{
			return double_145;
		}
		set
		{
			double_145 = value;
		}
	}

	public double DAIPHU_ThangThien_3_KhiCong_MinhKinhChiThuy
	{
		get
		{
			return double_146;
		}
		set
		{
			double_146 = value;
		}
	}

	public double CAMSU_ThangThien_3_KhiCong_TuDaThuCa
	{
		get
		{
			return double_147;
		}
		set
		{
			double_147 = value;
		}
	}

	public double DamHoaLien_ThangThien_1_KhiCong_BaVuongQuyDienGiap
	{
		get
		{
			return double_148;
		}
		set
		{
			double_148 = value;
		}
	}

	public double ThangThien_4_HongNguyetCuongPhong
	{
		get
		{
			return double_149;
		}
		set
		{
			double_149 = value;
		}
	}

	public double ThangThien_4_DocXaXuatDong
	{
		get
		{
			return double_150;
		}
		set
		{
			double_150 = value;
		}
	}

	public double ThangThien_4_ManNguyetCuongPhong
	{
		get
		{
			return double_151;
		}
		set
		{
			double_151 = value;
		}
	}

	public double ThangThien_4_LietNhatViemViem
	{
		get
		{
			return double_152;
		}
		set
		{
			double_152 = value;
		}
	}

	public double ThangThien_4_VongMaiThiemHoa
	{
		get
		{
			return double_153;
		}
		set
		{
			double_153 = value;
		}
	}

	public double ThangThien_4_HuyenTiChanMach
	{
		get
		{
			return double_154;
		}
		set
		{
			double_154 = value;
		}
	}

	public double ThangThien_4_TruongHongQuanThien
	{
		get
		{
			return double_155;
		}
		set
		{
			double_155 = value;
		}
	}

	public double ThangThien_4_AiHongBienDa
	{
		get
		{
			return double_156;
		}
		set
		{
			double_156 = value;
		}
	}

	public double ThangThien_1_LangKinhToiLuyen
	{
		get
		{
			return double_157;
		}
		set
		{
			double_157 = value;
		}
	}

	public double ThangThien_2_SatTinhQuangPhu
	{
		get
		{
			return double_158;
		}
		set
		{
			double_158 = value;
		}
	}

	public double ThangThien_3_KyQuan_QuanHung
	{
		get
		{
			return double_159;
		}
		set
		{
			double_159 = value;
		}
	}

	public double ThangThien_5_TriTan
	{
		get
		{
			return double_160;
		}
		set
		{
			double_160 = value;
		}
	}

	public double ThangThien_5_HoaLongPhapChieu
	{
		get
		{
			return double_161;
		}
		set
		{
			double_161 = value;
		}
	}

	public double ThangThien_5_KinhThienDongDia
	{
		get
		{
			return double_162;
		}
		set
		{
			double_162 = value;
		}
	}

	public double ThangThien_5_DietTheCuongVu
	{
		get
		{
			return double_163;
		}
		set
		{
			double_163 = value;
		}
	}

	public double ThangThien_5_ThienLyNhatKich
	{
		get
		{
			return double_164;
		}
		set
		{
			double_164 = value;
		}
	}

	public double ThangThien_5_HinhDiYeuTuong
	{
		get
		{
			return double_165;
		}
		set
		{
			double_165 = value;
		}
	}

	public double ThangThien_5_NhatChieuSatThan
	{
		get
		{
			return double_166;
		}
		set
		{
			double_166 = value;
		}
	}

	public double ThangThien_5_LongTraoTiemChiThu
	{
		get
		{
			return double_167;
		}
		set
		{
			double_167 = value;
		}
	}

	public double ThangThien_5_ThienMaChiLuc
	{
		get
		{
			return double_168;
		}
		set
		{
			double_168 = value;
		}
	}

	public double ThangThien_5_DongLinhDienThuat
	{
		get
		{
			return double_169;
		}
		set
		{
			double_169 = value;
		}
	}

	public double ThangThien_5_BatTu_ChiKhu
	{
		get
		{
			return double_170;
		}
		set
		{
			double_170 = value;
		}
	}

	public double ThangThien_5_MaHonChiLuc
	{
		get
		{
			return double_171;
		}
		set
		{
			double_171 = value;
		}
	}

	public double ThangThien_5_PhaKhongTruyTinh
	{
		get
		{
			return double_172;
		}
		set
		{
			double_172 = value;
		}
	}

	public double ThanNu_VanKhiHanhTam
	{
		get
		{
			return _ThanNu_VanKhiHanhTam;
		}
		set
		{
			_ThanNu_VanKhiHanhTam = value;
		}
	}

	public double ThanNu_ThaiCucTamPhap
	{
		get
		{
			return _ThanNu_ThaiCucTamPhap;
		}
		set
		{
			_ThanNu_ThaiCucTamPhap = value;
		}
	}

	public double ThanNu_ThanLucKichPhat
	{
		get
		{
			return _ThanNu_ThanLucKichPhat;
		}
		set
		{
			_ThanNu_ThanLucKichPhat = value;
		}
	}

	public double ThanNu_SatTinhNghiaHo
	{
		get
		{
			return _ThanNu_SatTinhNghiaHo;
		}
		set
		{
			_ThanNu_SatTinhNghiaHo = value;
		}
	}

	public double ThanNu_SatTinhNghiaSat
	{
		get
		{
			return _ThanNu_SatTinhNghiaSat;
		}
		set
		{
			_ThanNu_SatTinhNghiaSat = value;
		}
	}

	public double ThanNu_SatTinhNghiaKhi
	{
		get
		{
			return _ThanNu_SatTinhNghiaKhi;
		}
		set
		{
			_ThanNu_SatTinhNghiaKhi = value;
		}
	}

	public double ThanNu_TayTuyDichCan
	{
		get
		{
			return _ThanNu_TayTuyDichCan;
		}
		set
		{
			_ThanNu_TayTuyDichCan = value;
		}
	}

	public double ThanNu_HacHoaManKhai
	{
		get
		{
			return _ThanNu_HacHoaManKhai;
		}
		set
		{
			_ThanNu_HacHoaManKhai = value;
		}
	}

	public double ThanNu_DieuThuHoiXuan
	{
		get
		{
			return _ThanNu_DieuThuHoiXuan;
		}
		set
		{
			_ThanNu_DieuThuHoiXuan = value;
		}
	}

	public double ThanNu_TruongCongKichLuc
	{
		get
		{
			return _ThanNu_TruongCongKichLuc;
		}
		set
		{
			_ThanNu_TruongCongKichLuc = value;
		}
	}

	public double ThanNu_HacHoaTapTrung
	{
		get
		{
			return _ThanNu_HacHoaTapTrung;
		}
		set
		{
			_ThanNu_HacHoaTapTrung = value;
		}
	}

	public double ThanNu_ChanVu_TuyetKich
	{
		get
		{
			return _ThanNu_ChanVu_TuyetKich;
		}
		set
		{
			_ThanNu_ChanVu_TuyetKich = value;
		}
	}

	public double ThanNu_VanDocBatXam
	{
		get
		{
			return _ThanNu_VanDocBatXam;
		}
		set
		{
			_ThanNu_VanDocBatXam = value;
		}
	}

	public double ThanNu_PhanNoDieuTiet
	{
		get
		{
			return _ThanNu_PhanNoDieuTiet;
		}
		set
		{
			_ThanNu_PhanNoDieuTiet = value;
		}
	}

	public double ThanNu_CoDocGiaiTru
	{
		get
		{
			return _ThanNu_CoDocGiaiTru;
		}
		set
		{
			_ThanNu_CoDocGiaiTru = value;
		}
	}

	public double ThanNu_ThanLucBaoHo
	{
		get
		{
			return _ThanNu_ThanLucBaoHo;
		}
		set
		{
			_ThanNu_ThanLucBaoHo = value;
		}
	}

	public double ThanNu_ThiDocBaoPhat
	{
		get
		{
			return _ThanNu_ThiDocBaoPhat;
		}
		set
		{
			_ThanNu_ThiDocBaoPhat = value;
		}
	}

	public double Dao_ThangThien_6_VeRongDiemMat
	{
		get
		{
			return _Ve_Rong_Diem_Mat;
		}
		set
		{
			_Ve_Rong_Diem_Mat = value;
		}
	}

	public double Kiem_ThangThien_6_BachDocBatXam
	{
		get
		{
			return _Bach_Doc_Bat_Xam;
		}
		set
		{
			_Bach_Doc_Bat_Xam = value;
		}
	}

	public double Thuong_ThangThien_6_HanBangLinhVuc
	{
		get
		{
			return _Han_Bang_Linh_Vuc;
		}
		set
		{
			_Han_Bang_Linh_Vuc = value;
		}
	}

	public double Cung_ThangThien_6_AcTanMuiTenNgheo
	{
		get
		{
			return _Ac_Tan_Mui_Ten_Ngheo;
		}
		set
		{
			_Ac_Tan_Mui_Ten_Ngheo = value;
		}
	}

	public double DaiPhu_ThangThien_6_VanTamNguyetTinh
	{
		get
		{
			return _Van_Tam_Nguyet_Tinh;
		}
		set
		{
			_Van_Tam_Nguyet_Tinh = value;
		}
	}

	public double Ninja_ThangThien_6_BenNgoaiVuaBenTrongVua
	{
		get
		{
			return _Ben_Ngoai_Vua_Ben_Trong_Vua;
		}
		set
		{
			_Ben_Ngoai_Vua_Ben_Trong_Vua = value;
		}
	}

	public double CamSu_ThangThien_6_HuyetMachLenCao
	{
		get
		{
			return _Huyet_Mach_Len_Cao;
		}
		set
		{
			_Huyet_Mach_Len_Cao = value;
		}
	}

	public double HanBaoQuan_ThangThien_6_ChanKhiHoanNguyen
	{
		get
		{
			return _Chan_Khi_Hoan_Nguyen;
		}
		set
		{
			_Chan_Khi_Hoan_Nguyen = value;
		}
	}

	public double DamHoaLien_ThangThien_6_DienQuangSuongMai
	{
		get
		{
			return _Dien_Quang_Suong_Mai;
		}
		set
		{
			_Dien_Quang_Suong_Mai = value;
		}
	}

	public double QuyenSu_ThangThien_6_KhongChuongNgaiVat
	{
		get
		{
			return _Khong_Chuong_Ngai_Vat;
		}
		set
		{
			_Khong_Chuong_Ngai_Vat = value;
		}
	}

	public double MaiLieuChan_ThangThien_6_BienNguyThanhAn
	{
		get
		{
			return _Bien_Nguy_Thanh_An;
		}
		set
		{
			_Bien_Nguy_Thanh_An = value;
		}
	}

	public double TuHao_ThangThien_6_BanNguocVoHieu
	{
		get
		{
			return _Ban_Nguoc_Vo_Hieu;
		}
		set
		{
			_Ban_Nguoc_Vo_Hieu = value;
		}
	}

	public double ThanNu_ThangThien_6_ChongLaiThanPhap
	{
		get
		{
			return _Chong_Lai_Than_Phap;
		}
		set
		{
			_Chong_Lai_Than_Phap = value;
		}
	}

	public double KCTT_16x_All_TinhKimBachLuyen
	{
		get
		{
			return _Tinh_Kim_Bach_Luyen;
		}
		set
		{
			_Tinh_Kim_Bach_Luyen = value;
		}
	}

	public double KCTT_16x_All_HuyetKhiPhuongCuong
	{
		get
		{
			return _Huyet_Khi_Phuong_Cuong;
		}
		set
		{
			_Huyet_Khi_Phuong_Cuong = value;
		}
	}

	public double Giam_Bot_CongKich
	{
		get
		{
			return _Giam_Bot_CongKich;
		}
		set
		{
			_Giam_Bot_CongKich = value;
		}
	}

	public double Giam_Bot_CuongKhi
	{
		get
		{
			return _Giam_Bot_CuongKhi;
		}
		set
		{
			_Giam_Bot_CuongKhi = value;
		}
	}

	public double PhanKhiCong_17x_NhanKiemNhatThe_Kiem
	{
		get
		{
			return _PhanKhiCong_17x_NhanKiem_NhatThe_Kiem;
		}
		set
		{
			_PhanKhiCong_17x_NhanKiem_NhatThe_Kiem = value;
		}
	}

	public double PhanKhiCong_17x_HoThanCanhKhi_Kiem
	{
		get
		{
			return _PhanKhiCong_17x_HoThan_CanhKhi_Kiem;
		}
		set
		{
			_PhanKhiCong_17x_HoThan_CanhKhi_Kiem = value;
		}
	}

	public double PhanKhiCong_17x_TuLuongThienKim_Dao
	{
		get
		{
			return _PhanKhiCong_17x_TuLuong_ThienKim_Dao;
		}
		set
		{
			_PhanKhiCong_17x_TuLuong_ThienKim_Dao = value;
		}
	}

	public double PhanKhiCong_17x_BaKhiPhaGiap_Dao
	{
		get
		{
			return _PhanKhiCong_17x_BaKhi_PhaGiap_Dao;
		}
		set
		{
			_PhanKhiCong_17x_BaKhi_PhaGiap_Dao = value;
		}
	}

	public double PhanKhiCong_17x_HongNguyenCuongPhong_Thuong
	{
		get
		{
			return _PhanKhiCong_17x_HongNguyen_CuongPhong_Thuong;
		}
		set
		{
			_PhanKhiCong_17x_HongNguyen_CuongPhong_Thuong = value;
		}
	}

	public double PhanKhiCong_17x_DiaPhanXungKhi_Thuong
	{
		get
		{
			return _PhanKhiCong_17x_DiaPhan_XungKhi_Thuong;
		}
		set
		{
			_PhanKhiCong_17x_DiaPhan_XungKhi_Thuong = value;
		}
	}

	public double PhanKhiCong_17x_VoAnhAmTien_Cung
	{
		get
		{
			return _PhanKhiCong_17x_VoAnh_AmTien_Cung;
		}
		set
		{
			_PhanKhiCong_17x_VoAnh_AmTien_Cung = value;
		}
	}

	public double PhanKhiCong_17x_DocBaGiangHo_Cung
	{
		get
		{
			return _PhanKhiCong_17x_DocBa_GiangHo_Cung;
		}
		set
		{
			_PhanKhiCong_17x_DocBa_GiangHo_Cung = value;
		}
	}

	public double PhanKhiCong_17x_BachSuNhuY_DaiPhu
	{
		get
		{
			return _PhanKhiCong_17x_BachSu_NhuY_DaiPhu;
		}
		set
		{
			_PhanKhiCong_17x_BachSu_NhuY_DaiPhu = value;
		}
	}

	public double PhanKhiCong_17x_VanTamNguyetTinh_DaiPhu
	{
		get
		{
			return _PhanKhiCong_17x_VanTam_NguyetTinh_DaiPhu;
		}
		set
		{
			_PhanKhiCong_17x_VanTam_NguyetTinh_DaiPhu = value;
		}
	}

	public double PhanKhiCong_17x_LietNhatDiemDiem_ThichKhach
	{
		get
		{
			return _PhanKhiCong_17x_LietNhat_DiemDiem_ThichKhach;
		}
		set
		{
			_PhanKhiCong_17x_LietNhat_DiemDiem_ThichKhach = value;
		}
	}

	public double PhanKhiCong_17x_NguKhiXungTieu_ThichKhach
	{
		get
		{
			return _PhanKhiCong_17x_NguKhi_XungTieu_ThichKhach;
		}
		set
		{
			_PhanKhiCong_17x_NguKhi_XungTieu_ThichKhach = value;
		}
	}

	public double PhanKhiCong_17x_KichLucCongThanh_CamSu
	{
		get
		{
			return _PhanKhiCong_17x_KichLuc_CongThanh_CamSu;
		}
		set
		{
			_PhanKhiCong_17x_KichLuc_CongThanh_CamSu = value;
		}
	}

	public double PhanKhiCong_17x_TienCamTamPhap_CamSu
	{
		get
		{
			return _PhanKhiCong_17x_TienCam_TamPhap_CamSu;
		}
		set
		{
			_PhanKhiCong_17x_TienCam_TamPhap_CamSu = value;
		}
	}

	public double PhanKhiCong_17x_ThienMaQuangHuyet_HBQ
	{
		get
		{
			return _PhanKhiCong_17x_ThienMa_QuangHuyet_HBQ;
		}
		set
		{
			_PhanKhiCong_17x_ThienMa_QuangHuyet_HBQ = value;
		}
	}

	public double PhanKhiCong_17x_BaKhiPhaGiap_HBQ
	{
		get
		{
			return _PhanKhiCong_17x_BaKhi_PhaGiap_HBQ;
		}
		set
		{
			_PhanKhiCong_17x_BaKhi_PhaGiap_HBQ = value;
		}
	}

	public double PhanKhiCong_17x_NhuKhacCuong_DHL
	{
		get
		{
			return _PhanKhiCong_17x_NhuKhac_Cuong_DHL;
		}
		set
		{
			_PhanKhiCong_17x_NhuKhac_Cuong_DHL = value;
		}
	}

	public double PhanKhiCong_17x_TuNhanDaiPhap_DHL
	{
		get
		{
			return _PhanKhiCong_17x_TuNhan_DaiPhap_DHL;
		}
		set
		{
			_PhanKhiCong_17x_TuNhan_DaiPhap_DHL = value;
		}
	}

	public double PhanKhiCong_17x_NoTamXuatKich_QuyenSu
	{
		get
		{
			return _PhanKhiCong_17x_NoTam_XuatKich_QuyenSu;
		}
		set
		{
			_PhanKhiCong_17x_NoTam_XuatKich_QuyenSu = value;
		}
	}

	public double PhanKhiCong_17x_DienQuangThachHoa_QuyenSu
	{
		get
		{
			return _PhanKhiCong_17x_DienQuang_ThachHoa_QuyenSu;
		}
		set
		{
			_PhanKhiCong_17x_DienQuang_ThachHoa_QuyenSu = value;
		}
	}

	public double PhanKhiCong_17x_NoKhiXungThien_MLC
	{
		get
		{
			return _PhanKhiCong_17x_NoKhi_XungThien_MLC;
		}
		set
		{
			_PhanKhiCong_17x_NoKhi_XungThien_MLC = value;
		}
	}

	public double PhanKhiCong_17x_HuyenVuLoiDien_MLC
	{
		get
		{
			return _PhanKhiCong_17x_HuyenVu_LoiDien_MLC;
		}
		set
		{
			_PhanKhiCong_17x_HuyenVu_LoiDien_MLC = value;
		}
	}

	public double PhanKhiCong_17x_DiTinhVanThien_TH
	{
		get
		{
			return _PhanKhiCong_17x_DiTinh_VanThien_TH;
		}
		set
		{
			_PhanKhiCong_17x_DiTinh_VanThien_TH = value;
		}
	}

	public double PhanKhiCong_17x_DiCongViThu_TH
	{
		get
		{
			return _PhanKhiCong_17x_DiCong_ViThu_TH;
		}
		set
		{
			_PhanKhiCong_17x_DiCong_ViThu_TH = value;
		}
	}

	public double PhanKhiCong_17x_HacHoaTapTrung_TN
	{
		get
		{
			return _PhanKhiCong_17x_HacHoa_TapTrung_TN;
		}
		set
		{
			_PhanKhiCong_17x_HacHoa_TapTrung_TN = value;
		}
	}

	public double PhanKhiCong_17x_ThiDocBaoPhat_TN
	{
		get
		{
			return _PhanKhiCong_17x_ThiDoc_BaoPhat_TN;
		}
		set
		{
			_PhanKhiCong_17x_ThiDoc_BaoPhat_TN = value;
		}
	}

	public double PhanKhiCong_17x_VongAmCoDoc_ALL
	{
		get
		{
			return _PhanKhiCong_17x_VongAm_CoDoc_ALL;
		}
		set
		{
			_PhanKhiCong_17x_VongAm_CoDoc_ALL = value;
		}
	}
}
