﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_truyenthuhethong {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string nguoinhanthu_nhatvatten { get; set; }

		[JsonProperty]
		public int? guithu_npc { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string nguoiguithu_ten { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string truyenthunoidung { get; set; }

		[JsonProperty]
		public DateTime? truyenthuthoigian { get; set; }

		[JsonProperty]
		public int? danhdaudaxem { get; set; }

	}

}
