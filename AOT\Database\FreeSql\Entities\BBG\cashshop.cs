﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.BBG {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class cashshop {

		[JsonProperty]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = 16)]
		public string product_code { get; set; }

		[JsonProperty]
		public int fld_pid { get; set; }

		[JsonProperty]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_price { get; set; }

		[JsonProperty]
		public int? fld_price_old { get; set; }

		[JsonProperty, Column(StringLength = 1000)]
		public string fld_desc { get; set; }

		[JsonProperty]
		public int? fld_return { get; set; }

		[JsonProperty]
		public int? fld_number { get; set; }

		[JsonProperty]
		public int? fld_lock { get; set; }

		[JsonProperty]
		public int? fld_days { get; set; }

		[JsonProperty]
		public int? ORDER { get; set; }

		[JsonProperty]
		public int? category_id { get; set; }

		[JsonProperty]
		public int? fld_magic1 { get; set; }

		[JsonProperty]
		public int? fld_magic2 { get; set; }

		[JsonProperty]
		public int? fld_magic3 { get; set; }

		[JsonProperty]
		public int? fld_magic4 { get; set; }

		[JsonProperty]
		public int? fld_magic5 { get; set; }

		[JsonProperty]
		public int? fld_phaichangkhoalai { get; set; }

		[JsonProperty]
		public int? fld_socapphuhon { get; set; }

		[JsonProperty]
		public int? fld_tienhoa { get; set; }

		[JsonProperty]
		public int? fld_trungcapphuhon { get; set; }

	}

}
