using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer.AOI
{
    /// <summary>
    /// Represents a spatial grid for Area of Interest (AOI) management
    /// Each grid covers a 1024x1024 area of the game world
    /// </summary>
    public class AOIGrid
    {
        #region Properties
        
        /// <summary>
        /// Grid X coordinate (0-4 for 5x5 grid system)
        /// </summary>
        public int GridX { get; set; }
        
        /// <summary>
        /// Grid Y coordinate (0-4 for 5x5 grid system)
        /// </summary>
        public int GridY { get; set; }
        
        /// <summary>
        /// Map ID this grid belongs to
        /// </summary>
        public int MapID { get; set; }
        
        #endregion
        
        #region World Boundaries
        
        /// <summary>
        /// Minimum X coordinate in world space
        /// </summary>
        public float MinX { get; set; }
        
        /// <summary>
        /// Maximum X coordinate in world space
        /// </summary>
        public float MaxX { get; set; }
        
        /// <summary>
        /// Minimum Y coordinate in world space
        /// </summary>
        public float MinY { get; set; }
        
        /// <summary>
        /// Maximum Y coordinate in world space
        /// </summary>
        public float MaxY { get; set; }
        
        #endregion
        
        #region Overlap Boundaries
        
        /// <summary>
        /// Extended minimum X coordinate including overlap zone
        /// </summary>
        public float OverlapMinX { get; set; }
        
        /// <summary>
        /// Extended maximum X coordinate including overlap zone
        /// </summary>
        public float OverlapMaxX { get; set; }
        
        /// <summary>
        /// Extended minimum Y coordinate including overlap zone
        /// </summary>
        public float OverlapMinY { get; set; }
        
        /// <summary>
        /// Extended maximum Y coordinate including overlap zone
        /// </summary>
        public float OverlapMaxY { get; set; }
        
        #endregion
        
        #region Entity Containers - Reference Based (Optimized)

        /// <summary>
        /// Player IDs currently in this grid (references to World.allConnectedChars)
        /// </summary>
        private readonly HashSet<int> _playerIDs;

        /// <summary>
        /// NPC IDs currently in this grid (references to World.NpcList)
        /// </summary>
        private readonly HashSet<int> _npcIDs;

        /// <summary>
        /// Ground item IDs currently in this grid (references to World.GroundItemList)
        /// </summary>
        private readonly HashSet<long> _groundItemIDs;

        /// <summary>
        /// Lock for thread-safe access to player IDs
        /// </summary>
        private readonly ReaderWriterLockSlim _playersLock;

        /// <summary>
        /// Lock for thread-safe access to NPC IDs
        /// </summary>
        private readonly ReaderWriterLockSlim _npcsLock;

        /// <summary>
        /// Lock for thread-safe access to ground item IDs
        /// </summary>
        private readonly ReaderWriterLockSlim _itemsLock;

        /// <summary>
        /// Players currently in this grid (live data from World.allConnectedChars)
        /// </summary>
        public IEnumerable<Players> Players
        {
            get
            {
                _playersLock.EnterReadLock();
                try
                {
                    foreach (var playerID in _playerIDs)
                    {
                        if (World.allConnectedChars.TryGetValue(playerID, out var player))
                        {
                            yield return player;
                        }
                    }
                }
                finally
                {
                    _playersLock.ExitReadLock();
                }
            }
        }

        /// <summary>
        /// Get the count of players currently in this grid (optimized)
        /// </summary>
        public int PlayerCount
        {
            get
            {
                _playersLock.EnterReadLock();
                try
                {
                    return _playerIDs.Count;
                }
                finally
                {
                    _playersLock.ExitReadLock();
                }
            }
        }

        /// <summary>
        /// NPCs currently in this grid (live data from World.NpcList)
        /// </summary>
        public IEnumerable<NpcClass> NPCs
        {
            get
            {
                _npcsLock.EnterReadLock();
                try
                {
                    foreach (var npcID in _npcIDs)
                    {
                        if (World.NpcList.TryGetValue(npcID, out var npc))
                        {
                            yield return npc;
                        }
                    }
                }
                finally
                {
                    _npcsLock.ExitReadLock();
                }
            }
        }

        /// <summary>
        /// Ground items currently in this grid (live data from World.GroundItemList)
        /// </summary>
        public IEnumerable<GroundItem> GroundItems
        {
            get
            {
                _itemsLock.EnterReadLock();
                try
                {
                    foreach (var itemID in _groundItemIDs)
                    {
                        if (World.GroundItemList.TryGetValue(itemID, out var item))
                        {
                            yield return item;
                        }
                    }
                }
                finally
                {
                    _itemsLock.ExitReadLock();
                }
            }
        }

        #endregion
        
        #region Adjacent Grid Management
        
        /// <summary>
        /// List of adjacent grids for AOI calculations
        /// Includes all 8 surrounding grids (if they exist)
        /// </summary>
        public List<AOIGrid> AdjacentGrids { get; set; }
        
        #endregion
        
        #region Performance Optimization
        
        /// <summary>
        /// Flag indicating if this grid has been modified and needs updates
        /// </summary>
        public bool IsDirty { get; set; }
        
        /// <summary>
        /// Timestamp of last update for this grid
        /// </summary>
        public DateTime LastUpdate { get; set; }
        
        /// <summary>
        /// Total number of entities in this grid
        /// </summary>
        public int EntityCount
        {
            get
            {
                _playersLock.EnterReadLock();
                _npcsLock.EnterReadLock();
                _itemsLock.EnterReadLock();
                try
                {
                    return _playerIDs.Count + _npcIDs.Count + _groundItemIDs.Count;
                }
                finally
                {
                    _itemsLock.ExitReadLock();
                    _npcsLock.ExitReadLock();
                    _playersLock.ExitReadLock();
                }
            }
        }
        
        #endregion
        
        #region Constructor
        
        /// <summary>
        /// Initialize a new AOI grid
        /// </summary>
        public AOIGrid()
        {
            _playerIDs = new HashSet<int>();
            _npcIDs = new HashSet<int>();
            _groundItemIDs = new HashSet<long>();

            _playersLock = new ReaderWriterLockSlim();
            _npcsLock = new ReaderWriterLockSlim();
            _itemsLock = new ReaderWriterLockSlim();

            AdjacentGrids = new List<AOIGrid>();
            IsDirty = false;
            LastUpdate = DateTime.Now;
        }
        
        /// <summary>
        /// Initialize a new AOI grid with specific coordinates
        /// </summary>
        /// <param name="gridX">Grid X coordinate</param>
        /// <param name="gridY">Grid Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        public AOIGrid(int gridX, int gridY, int mapID) : this()
        {
            GridX = gridX;
            GridY = gridY;
            MapID = mapID;
        }
        
        #endregion
        
        #region Entity Management
        
        /// <summary>
        /// Add a player to this grid
        /// </summary>
        /// <param name="player">Player to add</param>
        /// <returns>True if added successfully</returns>
        public bool AddPlayer(Players player)
        {
            try
            {
                _playersLock.EnterWriteLock();
                try
                {
                    if (_playerIDs.Add(player.SessionID))
                    {
                        IsDirty = true;
                        //LogHelper.WriteLine(LogLevel.Debug, $"Player {player.CharacterName} added to grid ({GridX},{GridY}) on map {MapID}");
                        return true;
                    }
                    return false;
                }
                finally
                {
                    _playersLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding player to grid: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove a player from this grid
        /// </summary>
        /// <param name="sessionID">Player session ID</param>
        /// <returns>True if removed successfully</returns>
        public bool RemovePlayer(int sessionID)
        {
            try
            {
                _playersLock.EnterWriteLock();
                try
                {
                    if (_playerIDs.Remove(sessionID))
                    {
                        IsDirty = true;
                        // Get player name for logging if still exists in global collection
                        var playerName = World.allConnectedChars.TryGetValue(sessionID, out var player) ? player.CharacterName : "Unknown";
                        LogHelper.WriteLine(LogLevel.Debug, $"Player {playerName} removed from grid ({GridX},{GridY}) on map {MapID}");
                        return true;
                    }
                    return false;
                }
                finally
                {
                    _playersLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing player from grid: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Add an NPC to this grid
        /// </summary>
        /// <param name="npc">NPC to add</param>
        /// <returns>True if added successfully</returns>
        public bool AddNPC(NpcClass npc)
        {
            try
            {
                _npcsLock.EnterWriteLock();
                try
                {
                    if (_npcIDs.Add(npc.NPC_SessionID))
                    {
                        IsDirty = true;
                        //LogHelper.WriteLine(LogLevel.Debug, $"NPC {npc.Name} added to grid ({GridX},{GridY}) on map {MapID}");
                        return true;
                    }
                    return false;
                }
                finally
                {
                    _npcsLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding NPC to grid: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove an NPC from this grid
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if removed successfully</returns>
        public bool RemoveNPC(int npcSessionID)
        {
            try
            {
                _npcsLock.EnterWriteLock();
                try
                {
                    if (_npcIDs.Remove(npcSessionID))
                    {
                        IsDirty = true;
                        // Get NPC name for logging if still exists in global collection
                        var npcName = World.NpcList.TryGetValue(npcSessionID, out var npc) ? npc.Name : "Unknown";
                        LogHelper.WriteLine(LogLevel.Debug, $"NPC {npcName} removed from grid ({GridX},{GridY}) on map {MapID}");
                        return true;
                    }
                    return false;
                }
                finally
                {
                    _npcsLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing NPC from grid: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Add a ground item to this grid
        /// </summary>
        /// <param name="item">Ground item to add</param>
        /// <returns>True if added successfully</returns>
        public bool AddGroundItem(GroundItem item)
        {
            try
            {
                _itemsLock.EnterWriteLock();
                try
                {
                    if (_groundItemIDs.Add(item.id))
                    {
                        IsDirty = true;
                        //LogHelper.WriteLine(LogLevel.Debug, $"Ground item {item.id} added to grid ({GridX},{GridY}) on map {MapID}");
                        return true;
                    }
                    return false;
                }
                finally
                {
                    _itemsLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding ground item to grid: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove a ground item from this grid
        /// </summary>
        /// <param name="itemID">Ground item ID</param>
        /// <returns>True if removed successfully</returns>
        public bool RemoveGroundItem(long itemID)
        {
            try
            {
                _itemsLock.EnterWriteLock();
                try
                {
                    if (_groundItemIDs.Remove(itemID))
                    {
                        IsDirty = true;
                        LogHelper.WriteLine(LogLevel.Debug, $"Ground item {itemID} removed from grid ({GridX},{GridY}) on map {MapID}");
                        return true;
                    }
                    return false;
                }
                finally
                {
                    _itemsLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing ground item from grid: {ex.Message}");
                return false;
            }
        }
        
        #endregion

        #region Optimized Entity Access Methods

        /// <summary>
        /// Execute an action for each player in this grid (optimized enumeration)
        /// </summary>
        /// <param name="action">Action to execute for each player</param>
        public void ForEachPlayer(Action<Players> action)
        {
            _playersLock.EnterReadLock();
            try
            {
                foreach (var playerID in _playerIDs)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                    {
                        action(player);
                    }
                }
            }
            finally
            {
                _playersLock.ExitReadLock();
            }
        }

        /// <summary>
        /// Execute an action for each NPC in this grid (optimized enumeration)
        /// </summary>
        /// <param name="action">Action to execute for each NPC</param>
        public void ForEachNPC(Action<NpcClass> action)
        {
            _npcsLock.EnterReadLock();
            try
            {
                foreach (var npcID in _npcIDs)
                {
                    if (World.NpcList.TryGetValue(npcID, out var npc))
                    {
                        action(npc);
                    }
                }
            }
            finally
            {
                _npcsLock.ExitReadLock();
            }
        }

        /// <summary>
        /// Execute an action for each ground item in this grid (optimized enumeration)
        /// </summary>
        /// <param name="action">Action to execute for each ground item</param>
        public void ForEachGroundItem(Action<GroundItem> action)
        {
            _itemsLock.EnterReadLock();
            try
            {
                foreach (var itemID in _groundItemIDs)
                {
                    if (World.GroundItemList.TryGetValue(itemID, out var item))
                    {
                        action(item);
                    }
                }
            }
            finally
            {
                _itemsLock.ExitReadLock();
            }
        }

        /// <summary>
        /// Check if a player is in this grid
        /// </summary>
        /// <param name="sessionID">Player session ID</param>
        /// <returns>True if player is in this grid</returns>
        public bool ContainsPlayer(int sessionID)
        {
            _playersLock.EnterReadLock();
            try
            {
                return _playerIDs.Contains(sessionID);
            }
            finally
            {
                _playersLock.ExitReadLock();
            }
        }

        /// <summary>
        /// Check if an NPC is in this grid
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if NPC is in this grid</returns>
        public bool ContainsNPC(int npcSessionID)
        {
            _npcsLock.EnterReadLock();
            try
            {
                return _npcIDs.Contains(npcSessionID);
            }
            finally
            {
                _npcsLock.ExitReadLock();
            }
        }

        /// <summary>
        /// Check if a ground item is in this grid
        /// </summary>
        /// <param name="itemID">Ground item ID</param>
        /// <returns>True if ground item is in this grid</returns>
        public bool ContainsGroundItem(long itemID)
        {
            _itemsLock.EnterReadLock();
            try
            {
                return _groundItemIDs.Contains(itemID);
            }
            finally
            {
                _itemsLock.ExitReadLock();
            }
        }

        /// <summary>
        /// Get players within a specific range from a center point (optimized)
        /// </summary>
        /// <param name="centerX">Center X coordinate</param>
        /// <param name="centerY">Center Y coordinate</param>
        /// <param name="range">Range to search within</param>
        /// <returns>List of players within range</returns>
        public List<Players> GetPlayersInRange(float centerX, float centerY, float range)
        {
            var result = new List<Players>();
            var rangeSquared = range * range; // Use squared distance to avoid sqrt calculation

            _playersLock.EnterReadLock();
            try
            {
                foreach (var playerID in _playerIDs)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                    {
                        var deltaX = player.PosX - centerX;
                        var deltaY = player.PosY - centerY;
                        var distanceSquared = deltaX * deltaX + deltaY * deltaY;

                        if (distanceSquared <= rangeSquared)
                        {
                            result.Add(player);
                        }
                    }
                }
            }
            finally
            {
                _playersLock.ExitReadLock();
            }

            return result;
        }

        #endregion

        #region Utility Methods
        
        /// <summary>
        /// Check if a world position is within this grid's boundaries
        /// </summary>
        /// <param name="x">World X coordinate</param>
        /// <param name="y">World Y coordinate</param>
        /// <returns>True if position is within grid boundaries</returns>
        public bool ContainsPosition(float x, float y)
        {
            return x >= MinX && x < MaxX && y >= MinY && y < MaxY;
        }
        
        /// <summary>
        /// Check if a world position is within this grid's overlap boundaries
        /// </summary>
        /// <param name="x">World X coordinate</param>
        /// <param name="y">World Y coordinate</param>
        /// <returns>True if position is within overlap boundaries</returns>
        public bool ContainsPositionWithOverlap(float x, float y)
        {
            return x >= OverlapMinX && x < OverlapMaxX && y >= OverlapMinY && y < OverlapMaxY;
        }
        
        /// <summary>
        /// Mark this grid as clean (no longer dirty)
        /// </summary>
        public void MarkClean()
        {
            IsDirty = false;
            LastUpdate = DateTime.Now;
        }
        
        /// <summary>
        /// Get string representation of this grid
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AOIGrid({GridX},{GridY}) Map:{MapID} Entities:{EntityCount} Dirty:{IsDirty}";
        }

        /// <summary>
        /// Dispose resources used by this grid
        /// </summary>
        public void Dispose()
        {
            try
            {
                _playersLock?.Dispose();
                _npcsLock?.Dispose();
                _itemsLock?.Dispose();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error disposing AOIGrid resources: {ex.Message}");
            }
        }

        #endregion
    }
}
