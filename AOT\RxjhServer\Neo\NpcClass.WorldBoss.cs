
using System;
using System.Collections.Generic;
using System.Linq;
using System.Timers;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using YulgangServer;
using RxjhServer.HeroBoss;
using HeroYulgang.Services;

namespace RxjhServer
{
    public partial class NpcClass
    {
        // Các trạng thái của Boss đặc biệt
        public enum WorldBossState
        {
            Disabled = 0,      // Boss bị vô hiệu hóa
            Waiting = 1,       // Chờ boss được gọi
            Announced = 2,     // Đ<PERSON> thông báo boss sẽ xuất hiện
            Active = 3,        // Boss đang hoạt động
            Killed = 4,        // Boss đã bị giết
            Expired = 5        // Hết thời gian đánh boss
        }

        // <PERSON><PERSON><PERSON> thuộc tính cho Boss đặc biệt
        public WorldBossState BossState { get; private set; } = WorldBossState.Disabled;
        public BossType BossType { get; set; } = BossType.Normal; // <PERSON><PERSON>i boss, mặc định là Normal
        private System.Timers.Timer BossTimer;
        private System.Timers.Timer AnnounceTimer;
        private DateTime LastAnnouncementTime;
        private bool RewardsDistributed;

        // Thời gian còn lại cho boss (tính bằng giây)
        public int RemainingTime { get; private set; }

        // Khởi tạo boss đặc biệt
        public void InitializeAsBoss(BossType bossType, int timeoutInMinutes = 30)
        {
            IsWorldBoss = true; // Đánh dấu là boss đặc biệt
            BossType = bossType;
            BossState = WorldBossState.Active;
            RewardsDistributed = false;
            LastAnnouncementTime = DateTime.Now;

            // Thiết lập timer cho boss
            RemainingTime = timeoutInMinutes * 60;
            BossTimer = new System.Timers.Timer(1000);
            BossTimer.Elapsed += BossTimerElapsed;
            BossTimer.AutoReset = true;
            BossTimer.Enabled = true;

            // Thiết lập timer thông báo định kỳ
            AnnounceTimer = new System.Timers.Timer(30000); // 30 giây thông báo một lần
            AnnounceTimer.Elapsed += AnnounceTimerElapsed;
            AnnounceTimer.AutoReset = true;
            AnnounceTimer.Enabled = true;

            // Thông báo boss đã xuất hiện
            BroadcastBossAppearance();

            LogHelper.WriteLine(LogLevel.Info, $"Boss {BossType} ID:{ID} đã được kích hoạt với thời gian {timeoutInMinutes} phút");
        }

        // Tương thích ngược với phương thức cũ
        public void InitializeAsWorldBoss(int timeoutInMinutes = 30)
        {
            InitializeAsBoss(BossType.WorldBoss, timeoutInMinutes);
        }

        // Khởi tạo boss guild
        public void InitializeAsGuildBoss(int timeoutInMinutes = 30)
        {
            InitializeAsBoss(BossType.GuildBoss, timeoutInMinutes);
        }

        // Khởi tạo boss triệu hồi
        public void InitializeAsSummonBoss(int timeoutInMinutes = 30)
        {
            InitializeAsBoss(BossType.SummonBoss, timeoutInMinutes);
        }

        // Xử lý khi boss chết
        public void HandleWorldBossDeath()
        {
            if (IsWorldBoss && BossState == WorldBossState.Active)
            {
                BossState = WorldBossState.Killed;

                // Dừng các timer
                StopBossTimers();

                // Thông báo boss đã bị giết
                BroadcastBossKilled();

                // Đặt IsSpawned thành false cho schedule tương ứng
                WorldBossManager.Instance.ResetBossSchedule(ID);

                // Kiểm tra xem boss có trong danh sách đóng góp không
                if (!World.List_WorldBossContribute.TryGetValue(ID, out var contribute))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Boss {ID} không tìm thấy trong danh sách đóng góp khi xử lý tử vong");
                    return;
                }

                // Nếu là cross-server boss, xử lý qua CrossServerBossManager
                if (contribute.IsCrossServer)
                {
                    var killerName = GetTopDamagerName(contribute);
                    Task.Run(async () => await CrossServerBossManager.Instance.HandleBossDeath(ID, killerName));
                }
                else
                {
                    // Xử lý phần thưởng sau 1 giây cho boss thường
                    Task.Delay(1000).ContinueWith(_ => DistributeRewards());
                }
            }
        }

        /// <summary>
        /// Lấy tên người gây damage cao nhất
        /// </summary>
        private string GetTopDamagerName(WorldBossContributeClass contribute)
        {
            var allContributions = contribute.GetAllContributions();
            if (allContributions.Count > 0)
            {
                return allContributions.First().PlayerName;
            }
            return "Không xác định";
        }

        // Xử lý đếm ngược thời gian
        private void BossTimerElapsed(object sender, ElapsedEventArgs e)
        {
            if (BossState != WorldBossState.Active)
            {
                StopBossTimers();
                return;
            }

            RemainingTime--;

            // Kiểm tra nếu thời gian đã hết
            if (RemainingTime <= 0)
            {
                BossState = WorldBossState.Expired;
                StopBossTimers();
                BroadcastBossExpired();

                // Đặt IsSpawned thành false cho schedule tương ứng
                WorldBossManager.Instance.ResetBossSchedule(ID);

                // Xóa boss sau khi hết giờ
                Task.Delay(60000).ContinueWith(_ =>
                {
                    if (NPCDeath == false) // Nếu boss chưa chết
                    {
                        // Xóa boss sau 1 phút
                        World.delNpc(Rxjh_Map, ID);
                    }
                });
            }
        }

        // Thông báo định kỳ
        private void AnnounceTimerElapsed(object sender, ElapsedEventArgs e)
        {
            if (BossState != WorldBossState.Active)
            {
                return;
            }

            // Thông báo thông tin về boss và thời gian còn lại
            BroadcastBossStatus();

            // Thông báo top 5 người chơi gây sát thương nhiều nhất
            BroadcastTopDamagers();
        }

        // Dừng các timer
        private void StopBossTimers()
        {
            if (BossTimer != null)
            {
                BossTimer.Enabled = false;
                BossTimer.Dispose();
                BossTimer = null;
            }

            if (AnnounceTimer != null)
            {
                AnnounceTimer.Enabled = false;
                AnnounceTimer.Dispose();
                AnnounceTimer = null;
            }
        }

        // Thông báo boss xuất hiện cho tất cả người chơi
        private void BroadcastBossAppearance()
        {
            string bossTypeString = GetBossTypeString();

            foreach (var player in World.allConnectedChars.Values)
            {
                player.HeThongNhacNho($"{bossTypeString} đã xuất hiện. Hãy nhanh chóng tiêu diệt!");
                player.GuiDi_TheLucChien_DemNguoc(RemainingTime);
            }
        }

        // Thông báo trạng thái boss hiện tại
        private void BroadcastBossStatus()
        {
            double hpPercentage = (double)Rxjh_HP / Max_Rxjh_HP * 100;
            string bossTypeString = GetBossTypeString();

            // Chỉ gửi cho người chơi ở cùng map với boss
            foreach (var player in World.allConnectedChars.Values.Where(p => p.MapID == Rxjh_Map))
            {
                player.HeThongNhacNho($"{bossTypeString} còn {RemainingTime / 60} phút {RemainingTime % 60} giây - HP: {hpPercentage:F1}%");
            }
        }

        // Thông báo top damager
        private void BroadcastTopDamagers()
        {
            if (!World.List_WorldBossContribute.TryGetValue(ID, out var contribute))
                return;

            var orderedDamagers = contribute.Contribute.Values
                .OrderByDescending(x => x.Damage)
                .Take(5)
                .ToList();

            foreach (var player in World.allConnectedChars.Values.Where(p => p.MapID == Rxjh_Map))
            {
                for (int i = 0; i < orderedDamagers.Count; i++)
                {
                    player.HeThongNhacNho($"[TOP {i + 1}] [{orderedDamagers[i].PlayerName}] - Sát thương: {orderedDamagers[i].Damage}");
                }

                // Thông báo vị trí của người chơi hiện tại nếu không nằm trong top 5
                var playerDamage = contribute.Contribute.Values.FirstOrDefault(x => x.SessionID == player.SessionID);
                if (playerDamage != null)
                {
                    int playerRank = contribute.Contribute.Values
                        .OrderByDescending(x => x.Damage)
                        .ToList()
                        .FindIndex(x => x.SessionID == player.SessionID) + 1;

                    if (playerRank > 5)
                    {
                        player.HeThongNhacNho($"Hạng của bạn: [{playerRank}] - Sát thương: {playerDamage.Damage}");
                    }
                }
            }
        }

        // Thông báo boss đã bị giết
        private void BroadcastBossKilled()
        {
            if (!World.List_WorldBossContribute.TryGetValue(ID, out var contribute))
                return;

            string killerName = "Không xác định";
            if (contribute.Contribute.Values.Any())
            {
                var topDamager = contribute.Contribute.Values.OrderByDescending(x => x.Damage).First();
                killerName = topDamager.PlayerName;
                contribute.KilledBy = killerName;
            }

            string bossTypeString = GetBossTypeString();

            foreach (var player in World.allConnectedChars.Values)
            {
                player.HeThongNhacNho($"{bossTypeString} đã bị tiêu diệt bởi [{killerName}]!");
                player.GuiDi_TheLucChien_DemNguoc(60); // 60 giây để nhận phần thưởng
            }
        }

        // Thông báo boss đã hết thời gian
        private void BroadcastBossExpired()
        {
            string bossTypeString = GetBossTypeString();
            foreach (var player in World.allConnectedChars.Values)
            {
                player.HeThongNhacNho($"{bossTypeString} đã biến mất do hết thời gian!");
                player.GuiDi_TheLucChien_DemNguoc(60); // 60 giây để kết thúc sự kiện
            }
        }

        // Phân phối phần thưởng khi boss bị giết
        private void DistributeRewards()
        {
            if (BossState != WorldBossState.Killed || RewardsDistributed)
                return;

            RewardsDistributed = true;

            try
            {
                // Kiểm tra thông tin đóng góp
                if (!World.List_WorldBossContribute.TryGetValue(ID, out var contribute))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Không thể phân phối phần thưởng cho boss {ID}: Không tìm thấy thông tin đóng góp");
                    return;
                }

                // Gọi phương thức phân phối phần thưởng từ HeroWorldBossClass
                // Phương thức này sẽ xử lý cả phần thưởng điểm, phần thưởng đặc biệt và xóa boss khỏi danh sách đóng góp
                World.WorldBossEvent?.DistributeRewards(ID);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi phân phối phần thưởng: {ex.Message}");
            }
        }

        // Lấy chuỗi mô tả loại boss
        private string GetBossTypeString()
        {
            return BossType switch
            {
                BossType.WorldBoss => "Boss Thế Giới",
                BossType.GuildBoss => "Boss Bang Hội",
                BossType.SummonBoss => "Boss Triệu Hồi",
                _ => "Boss"
            };
        }
    }
}