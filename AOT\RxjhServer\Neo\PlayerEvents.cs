using System;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer.GroupQuest;

namespace RxjhServer
{
    /// <summary>
    /// Lớp xử lý các sự kiện của người chơi
    /// </summary>
    public static class PlayerEvents
    {
        /// <summary>
        /// Xử lý sự kiện khi người chơi chết
        /// </summary>
        /// <param name="player">Người chơi đã chết</param>
        /// <param name="killer">Người chơi đã giết</param>
        /// <param name="contributors"><PERSON><PERSON> sách người chơi đóng góp</param>
        public static void OnPlayerDeath(Players player, Players killer, Players[] contributors)
        {
            try
            {
                if (player == null)
                    return;

                // Nếu người chơi bị giết bởi người chơi khác
                if (killer != null && player != killer)
                {
                    // G<PERSON>i sự kiện tiêu diệt người chơi
                    GroupQuestEvent.Instance.RaisePlayerKilled(player, killer, contributors);

                    LogHelper.WriteLine(LogLevel.Info, $"Người chơi {player.CharacterName} đã bị {killer.CharacterName} tiêu diệt");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi xử lý sự kiện người chơi chết: {ex.Message}");
            }
        }
        /// <summary>
        /// Xử lý HP của người chơi thay đổi
        /// </summary>
        public enum HpChangeType
        {
            Damage,         // Mất máu do tấn công, môi trường, v.v.
            Healing,        // Hồi máu từ kỹ năng, vật phẩm, v.v.
            Regeneration,   // Hồi máu tự nhiên (regen)
            Poison,         // Mất máu do độc
            Buff,           // Hồi máu từ buff
            Bleeding,       // Mất máu do chảy máu
            Other           // Các trường hợp khác
        }

        public static bool OnPlayerHpChanged(
            Players targetPlayer,           // Người chơi chịu ảnh hưởng
            int changed,                   // Số lượng HP thay đổi (dương: hồi, âm: mất)
            Players sourcePlayer,          // Người chơi gây ra thay đổi (null nếu không có)
            HpChangeType changeType,       // Loại thay đổi HP
            NpcClass npcClass,
            bool lethal = false,
            string skillOrItemId = null,   // ID của kỹ năng hoặc vật phẩm (nếu có)
            bool isCritical = false,       // Có phải đòn chí mạng không
            long eventTimestamp = 0        // Thời gian sự kiện (để đồng bộ hoặc log)
        )
        {
            try
            {
                // Kiểm tra nếu không có targetPlayer
                if (targetPlayer == null)
                {
                    LogHelper.WriteLine(LogLevel.Info, "Không có người chơi mục tiêu để xử lý thay đổi HP.");
                    return false;
                }

                // Log thông tin sự kiện
                //LogHelper.WriteLine(LogLevel.Info, $"HP của {targetPlayer.CharacterName} thay đổi: {changed}, Loại: {changeType}, Nguồn: {(sourcePlayer != null ? sourcePlayer.CharacterName : "Tự động")}");

                // Xử lý theo loại thay đổi
                switch (changeType)
                {
                    case HpChangeType.Healing:
                    case HpChangeType.Regeneration:
                    case HpChangeType.Buff:
                            // Gọi sự kiện hồi máu
                            // Ví dụ: targetPlayer.TriggerHealingEvent(changed, sourcePlayer, skillOrItemId);
                            targetPlayer.NhanVat_HP += changed;
                            if (targetPlayer.NhanVat_HP > targetPlayer.CharacterMax_HP)
                            {
                                targetPlayer.NhanVat_HP = targetPlayer.CharacterMax_HP;
                            }
                            targetPlayer.CapNhat_HP_MP_SP();
                       
                        break;

                    case HpChangeType.Damage:
                    case HpChangeType.Poison:
                    case HpChangeType.Bleeding:
                            // Gọi sự kiện mất máu
                            // Ví dụ: targetPlayer.TriggerDamageEvent(changed, sourcePlayer, skillOrItemId, isCritical);
                            targetPlayer.NhanVat_HP -= changed;
                            if (targetPlayer.NhanVat_HP < 0)
                            {
                                targetPlayer.NhanVat_HP = 0;
                            }
                            if (sourcePlayer != null)
                            {
                                targetPlayer.AddContributors(sourcePlayer);
                            }
                            targetPlayer.CapNhat_HP_MP_SP();
                        break;

                    case HpChangeType.Other:
                        // Xử lý các trường hợp đặc biệt
                        // Ví dụ: sự kiện hệ thống hoặc môi trường
                        break;

                    default:
                        LogHelper.WriteLine(LogLevel.Error, $"Loại thay đổi HP không xác định: {changeType}");
                        break;
                }

                // Kiểm tra trạng thái người chơi (chết, bất tử, v.v.)
                if (targetPlayer.NhanVat_HP <= 0 && lethal)
                {
                    // Gọi sự kiện người chơi chết
                    if (sourcePlayer != null)
                    {
				        targetPlayer.SetLastAttacker(sourcePlayer);
                    }
                    targetPlayer.Death();
                    return true;
                }
                return false;
                
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi xử lý sự kiện HP người chơi thay đổi: {ex.Message}");
                return false;
            }
        }
    }
}
