using System;
using System.Collections.Generic;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer.AOI
{
    /// <summary>
    /// Configuration settings for the Area of Interest (AOI) system
    /// Allows runtime configuration and tuning of AOI parameters
    /// </summary>
    public class AOIConfiguration
    {
        #region Singleton Pattern
        
        private static AOIConfiguration _instance;
        private static readonly object _lock = new object();
        
        /// <summary>
        /// Get the singleton instance of AOIConfiguration
        /// </summary>
        public static AOIConfiguration Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new AOIConfiguration();
                        }
                    }
                }
                return _instance;
            }
        }
        
        #endregion
        
        #region Configuration Properties
        
        /// <summary>
        /// Whether the AOI system is enabled globally
        /// </summary>
        public bool IsEnabled { get; set; } = false;
        
        /// <summary>
        /// Whether to use AOI system for players
        /// </summary>
        public bool UseAOIForPlayers { get; set; } = true;
        
        /// <summary>
        /// Whether to use AOI system for NPCs
        /// </summary>
        public bool UseAOIForNPCs { get; set; } = true;
        
        /// <summary>
        /// Whether to use AOI system for ground items
        /// </summary>
        public bool UseAOIForGroundItems { get; set; } = true;
        
        /// <summary>
        /// Maximum number of players to process in a single batch update
        /// </summary>
        public int MaxBatchSize { get; set; } = 100;
        
        /// <summary>
        /// Minimum time between AOI updates for the same player (in milliseconds)
        /// </summary>
        public int MinUpdateInterval { get; set; } = 100;
        
        /// <summary>
        /// Maximum time before forcing an AOI update (in seconds)
        /// </summary>
        public int MaxUpdateInterval { get; set; } = 5;
        
        /// <summary>
        /// Whether to enable debug logging for AOI operations
        /// </summary>
        public bool EnableDebugLogging { get; set; } = false;
        
        /// <summary>
        /// Whether to enable performance monitoring
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = false;
        
        /// <summary>
        /// Maps that should not use AOI system (fallback to old system)
        /// </summary>
        public HashSet<int> DisabledMaps { get; set; } = new HashSet<int>();
        
        /// <summary>
        /// Custom AOI radius for specific maps
        /// </summary>
        public Dictionary<int, int> CustomAOIRadius { get; set; } = new Dictionary<int, int>();
        
        /// <summary>
        /// Custom grid size for specific maps
        /// </summary>
        public Dictionary<int, int> CustomGridSize { get; set; } = new Dictionary<int, int>();
        
        #endregion
        
        #region Constructor
        
        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private AOIConfiguration()
        {
            LoadDefaultConfiguration();
            LogHelper.WriteLine(LogLevel.Info, "AOIConfiguration initialized with default settings");
        }
        
        #endregion
        
        #region Configuration Methods
        
        /// <summary>
        /// Load default configuration settings
        /// </summary>
        private void LoadDefaultConfiguration()
        {
            try
            {
                // Set default disabled maps (if any)
                // Example: DisabledMaps.Add(7101); // Special map that should use old system
                
                // Set custom AOI radius for specific maps
                CustomAOIRadius[7101] = 1000; // Larger radius for special maps
                
                // Set custom grid sizes if needed
                // CustomGridSize[someMapId] = 2048; // Larger grids for special maps
                
                LogHelper.WriteLine(LogLevel.Info, "Default AOI configuration loaded");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error loading default AOI configuration: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Check if AOI system should be used for a specific map
        /// </summary>
        /// <param name="mapID">Map ID to check</param>
        /// <returns>True if AOI should be used for this map</returns>
        public bool ShouldUseAOI(int mapID)
        {
            return IsEnabled && !DisabledMaps.Contains(mapID);
        }
        
        /// <summary>
        /// Get AOI radius for a specific map
        /// </summary>
        /// <param name="mapID">Map ID</param>
        /// <returns>AOI radius for the map</returns>
        public int GetAOIRadius(int mapID)
        {
            return CustomAOIRadius.GetValueOrDefault(mapID, AOIManager.AOI_RADIUS);
        }
        
        /// <summary>
        /// Get grid size for a specific map
        /// </summary>
        /// <param name="mapID">Map ID</param>
        /// <returns>Grid size for the map</returns>
        public int GetGridSize(int mapID)
        {
            return CustomGridSize.GetValueOrDefault(mapID, AOIManager.GRID_SIZE);
        }
        
        /// <summary>
        /// Enable AOI system for a specific map
        /// </summary>
        /// <param name="mapID">Map ID to enable</param>
        public void EnableMapAOI(int mapID)
        {
            DisabledMaps.Remove(mapID);
            LogHelper.WriteLine(LogLevel.Info, $"AOI enabled for map {mapID}");
        }
        
        /// <summary>
        /// Disable AOI system for a specific map
        /// </summary>
        /// <param name="mapID">Map ID to disable</param>
        public void DisableMapAOI(int mapID)
        {
            DisabledMaps.Add(mapID);
            LogHelper.WriteLine(LogLevel.Info, $"AOI disabled for map {mapID}");
        }
        
        /// <summary>
        /// Set custom AOI radius for a specific map
        /// </summary>
        /// <param name="mapID">Map ID</param>
        /// <param name="radius">Custom AOI radius</param>
        public void SetCustomAOIRadius(int mapID, int radius)
        {
            CustomAOIRadius[mapID] = radius;
            LogHelper.WriteLine(LogLevel.Info, $"Custom AOI radius {radius} set for map {mapID}");
        }
        
        /// <summary>
        /// Set custom grid size for a specific map
        /// </summary>
        /// <param name="mapID">Map ID</param>
        /// <param name="gridSize">Custom grid size</param>
        public void SetCustomGridSize(int mapID, int gridSize)
        {
            CustomGridSize[mapID] = gridSize;
            LogHelper.WriteLine(LogLevel.Info, $"Custom grid size {gridSize} set for map {mapID}");
        }
        
        /// <summary>
        /// Reset configuration to defaults
        /// </summary>
        public void ResetToDefaults()
        {
            IsEnabled = true;
            UseAOIForPlayers = true;
            UseAOIForNPCs = true;
            UseAOIForGroundItems = true;
            MaxBatchSize = 100;
            MinUpdateInterval = 100;
            MaxUpdateInterval = 5;
            EnableDebugLogging = false;
            EnablePerformanceMonitoring = true;
            DisabledMaps.Clear();
            CustomAOIRadius.Clear();
            CustomGridSize.Clear();
            
            LoadDefaultConfiguration();
            LogHelper.WriteLine(LogLevel.Info, "AOI configuration reset to defaults");
        }
        
        /// <summary>
        /// Get configuration summary
        /// </summary>
        /// <returns>Configuration summary string</returns>
        public string GetConfigurationSummary()
        {
            return $"AOI Configuration - Enabled: {IsEnabled}, " +
                   $"Players: {UseAOIForPlayers}, NPCs: {UseAOIForNPCs}, Items: {UseAOIForGroundItems}, " +
                   $"BatchSize: {MaxBatchSize}, MinInterval: {MinUpdateInterval}ms, MaxInterval: {MaxUpdateInterval}s, " +
                   $"DisabledMaps: {DisabledMaps.Count}, CustomRadius: {CustomAOIRadius.Count}, " +
                   $"Debug: {EnableDebugLogging}, Monitoring: {EnablePerformanceMonitoring}";
        }
        
        #endregion
        
        #region Performance Settings
        
        /// <summary>
        /// Apply performance optimizations based on server load
        /// </summary>
        /// <param name="playerCount">Current number of online players</param>
        public void ApplyPerformanceOptimizations(int playerCount)
        {
            try
            {
                if (playerCount > 1000)
                {
                    // High load - reduce update frequency
                    MinUpdateInterval = 200;
                    MaxUpdateInterval = 10;
                    MaxBatchSize = 50;
                    EnableDebugLogging = false;
                }
                else if (playerCount > 500)
                {
                    // Medium load - moderate settings
                    MinUpdateInterval = 150;
                    MaxUpdateInterval = 7;
                    MaxBatchSize = 75;
                    EnableDebugLogging = false;
                }
                else
                {
                    // Low load - optimal settings
                    MinUpdateInterval = 100;
                    MaxUpdateInterval = 5;
                    MaxBatchSize = 100;
                    EnableDebugLogging = false; // Keep false in production
                }
                
                LogHelper.WriteLine(LogLevel.Info, $"AOI performance optimizations applied for {playerCount} players");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error applying performance optimizations: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Validation
        
        /// <summary>
        /// Validate configuration settings
        /// </summary>
        /// <returns>True if configuration is valid</returns>
        public bool ValidateConfiguration()
        {
            try
            {
                bool isValid = true;
                
                if (MinUpdateInterval < 50 || MinUpdateInterval > 1000)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"MinUpdateInterval {MinUpdateInterval} is outside recommended range (50-1000ms)");
                    isValid = false;
                }
                
                if (MaxUpdateInterval < 1 || MaxUpdateInterval > 60)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"MaxUpdateInterval {MaxUpdateInterval} is outside recommended range (1-60s)");
                    isValid = false;
                }
                
                if (MaxBatchSize < 10 || MaxBatchSize > 500)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"MaxBatchSize {MaxBatchSize} is outside recommended range (10-500)");
                    isValid = false;
                }
                
                foreach (var radius in CustomAOIRadius.Values)
                {
                    if (radius < 100 || radius > 2000)
                    {
                        LogHelper.WriteLine(LogLevel.Warning, $"Custom AOI radius {radius} is outside recommended range (100-2000)");
                        isValid = false;
                    }
                }
                
                foreach (var gridSize in CustomGridSize.Values)
                {
                    if (gridSize < 512 || gridSize > 4096)
                    {
                        LogHelper.WriteLine(LogLevel.Warning, $"Custom grid size {gridSize} is outside recommended range (512-4096)");
                        isValid = false;
                    }
                }
                
                return isValid;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error validating AOI configuration: {ex.Message}");
                return false;
            }
        }
        
        #endregion
    }
}
