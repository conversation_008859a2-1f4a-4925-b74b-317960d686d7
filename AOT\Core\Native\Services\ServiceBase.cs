using System;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Messaging;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Services
{
    /// <summary>
    /// Base service implementation - thay thế ReceiveActor
    /// </summary>
    public abstract class ServiceBase : IService, IDisposable
    {
        private readonly CancellationTokenSource _cancellationTokenSource;
        private Task? _serviceTask;
        private volatile ServiceState _state = ServiceState.NotStarted;
        private readonly object _stateLock = new object();
        private volatile bool _isDisposed;

        protected ServiceBase(string? serviceId = null)
        {
            ServiceId = serviceId ?? GetType().Name + "_" + Guid.NewGuid().ToString("N")[..8];
            _cancellationTokenSource = new CancellationTokenSource();
        }

        public string ServiceId { get; }

        public ServiceState State
        {
            get => _state;
            private set
            {
                lock (_stateLock)
                {
                    if (_state != value)
                    {
                        var oldState = _state;
                        _state = value;
                        OnStateChanged(oldState, value);
                    }
                }
            }
        }

        public object? Configuration { get; set; }

        /// <summary>
        /// Cancellation token cho service
        /// </summary>
        protected CancellationToken CancellationToken => _cancellationTokenSource.Token;

        /// <summary>
        /// Start service
        /// </summary>
        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(GetType().Name);

            lock (_stateLock)
            {
                if (_state != ServiceState.NotStarted && _state != ServiceState.Stopped)
                {
                    Logger.Instance.Warning($"Service {ServiceId} is already started or starting");
                    return;
                }

                State = ServiceState.Starting;
            }

            try
            {
                Logger.Instance.Info($"Starting service {ServiceId}");

                // Initialize service
                await OnStartingAsync(cancellationToken);

                // Start main service task
                _serviceTask = RunServiceAsync(_cancellationTokenSource.Token);

                State = ServiceState.Running;
                Logger.Instance.Info($"Service {ServiceId} started successfully");
            }
            catch (Exception ex)
            {
                State = ServiceState.Failed;
                Logger.Instance.Error($"Failed to start service {ServiceId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Stop service gracefully
        /// </summary>
        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            if (_isDisposed)
                return;

            lock (_stateLock)
            {
                if (_state == ServiceState.Stopped || _state == ServiceState.Stopping)
                {
                    return;
                }

                State = ServiceState.Stopping;
            }

            try
            {
                Logger.Instance.Info($"Stopping service {ServiceId}");

                // Signal cancellation
                _cancellationTokenSource.Cancel();

                // Wait for service task to complete
                if (_serviceTask != null)
                {
                    var timeoutTask = Task.Delay(TimeSpan.FromSeconds(30), cancellationToken);
                    var completedTask = await Task.WhenAny(_serviceTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        Logger.Instance.Warning($"Service {ServiceId} stop timeout");
                    }
                }

                // Cleanup
                await OnStoppingAsync(cancellationToken);

                State = ServiceState.Stopped;
                Logger.Instance.Info($"Service {ServiceId} stopped");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error stopping service {ServiceId}: {ex.Message}");
                State = ServiceState.Failed;
                throw;
            }
        }

        /// <summary>
        /// Health check
        /// </summary>
        public virtual async Task<bool> IsHealthyAsync()
        {
            try
            {
                if (_state != ServiceState.Running)
                    return false;

                return await OnHealthCheckAsync();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Health check failed for service {ServiceId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Main service execution loop
        /// </summary>
        private async Task RunServiceAsync(CancellationToken cancellationToken)
        {
            try
            {
                await OnRunningAsync(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when stopping
                Logger.Instance.Debug($"Service {ServiceId} cancelled");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Service {ServiceId} failed: {ex.Message}");
                State = ServiceState.Failed;
                await OnFailedAsync(ex);
                throw;
            }
        }

        /// <summary>
        /// Called when service is starting
        /// </summary>
        protected virtual Task OnStartingAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Main service logic - override this
        /// </summary>
        protected abstract Task OnRunningAsync(CancellationToken cancellationToken);

        /// <summary>
        /// Called when service is stopping
        /// </summary>
        protected virtual Task OnStoppingAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Health check implementation
        /// </summary>
        protected virtual Task<bool> OnHealthCheckAsync()
        {
            return Task.FromResult(true);
        }

        /// <summary>
        /// Called when service fails
        /// </summary>
        protected virtual Task OnFailedAsync(Exception exception)
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Called when state changes
        /// </summary>
        protected virtual void OnStateChanged(ServiceState oldState, ServiceState newState)
        {
            Logger.Instance.Debug($"Service {ServiceId} state changed: {oldState} -> {newState}");
        }

        /// <summary>
        /// Restart service
        /// </summary>
        public async Task RestartAsync(CancellationToken cancellationToken = default)
        {
            Logger.Instance.Info($"Restarting service {ServiceId}");

            await StopAsync(cancellationToken);

            // Wait a bit before restart
            await Task.Delay(TimeSpan.FromSeconds(1), cancellationToken);

            await StartAsync(cancellationToken);
        }

        public virtual void Dispose()
        {
            if (_isDisposed)
                return;

            _isDisposed = true;

            try
            {
                // Stop service if running
                if (_state == ServiceState.Running || _state == ServiceState.Starting)
                {
                    StopAsync().Wait(TimeSpan.FromSeconds(10));
                }

                _cancellationTokenSource?.Dispose();
                _serviceTask?.Dispose();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing service {ServiceId}: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Message-enabled service base - có thể nhận messages
    /// </summary>
    public abstract class MessageServiceBase : ServiceBase, IMessageService
    {
        private readonly ChannelMessageTarget _messageTarget;

        protected MessageServiceBase(string? serviceId = null) : base(serviceId)
        {
            _messageTarget = new ChannelMessageTarget(ServiceId);
        }

        public IMessageTarget MessageTarget => _messageTarget;

        /// <summary>
        /// Register message handler
        /// </summary>
        protected void RegisterHandler<T>(IMessageHandler<T> handler) where T : class
        {
            _messageTarget.RegisterHandler(handler);
        }

        /// <summary>
        /// Register message handler với function
        /// </summary>
        protected void RegisterHandler<T>(Func<T, IMessageContext, Task> handler) where T : class
        {
            _messageTarget.RegisterHandler(handler);
        }

        protected override async Task OnStoppingAsync(CancellationToken cancellationToken)
        {
            await base.OnStoppingAsync(cancellationToken);
            await _messageTarget.StopAsync(TimeSpan.FromSeconds(10));
        }

        public override void Dispose()
        {
            _messageTarget?.Dispose();
            base.Dispose();
        }
    }
}
