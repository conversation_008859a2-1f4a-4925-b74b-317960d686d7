using System;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.Database;

using HeroYulgang.Services;
namespace RxjhServer;

public class Wedding : IDisposable
{
	private string TenSanhTiecCuoi = string.Empty;

	private Players TanLang;

	private Players TanNuong;

	private DateTime kssj;

	private DateTime kssjgj;

	private DateTime ToanBo_KetThucThoiGian;

	private int kssjint;

	public int FLD_MAP;

	public int WeddingPattern;

	public int Wedding_Progress;

	private System.Timers.Timer ChuanBiBoDemThoiGian;

	private System.Timers.Timer BatDauHenGio;

	private System.Timers.Timer TanNuongBatDau_DemThoiGian;

	private System.Timers.Timer BoDemThoiGianPhatBieu;

	private System.Timers.Timer DatCauHoi_TanNuong_DemThoiGian;

	private System.Timers.Timer DatCauHoi_TanLang_DemThoiGian;

	private System.Timers.Timer TraLoiKetQua_MayDemThoiGian;

	private System.Timers.Timer TraDoiNhanChoNhau_MayDemThoiGian;

	private System.Timers.Timer CamOnQuyKhach_MayDemThoiGian;

	private System.Timers.Timer HenGioKetThuc1;

	private System.Timers.Timer HenGioKetThuc2;

	private System.Timers.Timer HenGioKetThuc3;

	private System.Timers.Timer HenGioKetThuc4;

	public Wedding(Players TanLang, Players TanNuong, int BanDo, int CheDo)
	{
		this.TanLang = TanLang;
		this.TanNuong = TanNuong;
		FLD_MAP = BanDo;
		WeddingPattern = CheDo;
		Wedding_Progress = 0;
		switch (BanDo)
		{
		case 9201:
			TenSanhTiecCuoi = "圣礼殿";
			break;
		case 9101:
			TenSanhTiecCuoi = "龙赡殿";
			break;
		case 9001:
			TenSanhTiecCuoi = "华婚殿";
			break;
		}
		kssj = DateTime.Now.AddMinutes(10.0);
		ChuanBiBoDemThoiGian = new(60000.0);
		ChuanBiBoDemThoiGian.Elapsed += ChuanBi_ThoiGianKetThucSuKien;
		ChuanBiBoDemThoiGian.Enabled = true;
		ChuanBiBoDemThoiGian.AutoReset = true;
		ChuanBi_ThoiGianKetThucSuKien(null, null);
		ToanBo_KetThucThoiGian = DateTime.Now.AddMinutes(30.0);
		HenGioKetThuc4 = new(60000.0);
		HenGioKetThuc4.Elapsed += ToanBo_ThoiGianKetThucSuKien;
		HenGioKetThuc4.Enabled = true;
		HenGioKetThuc4.AutoReset = true;
		if (this.TanLang.PreliminaryApplicationCeremonyTimer != null)
		{
			this.TanLang.PreliminaryApplicationCeremonyTimer.Enabled = false;
			this.TanLang.PreliminaryApplicationCeremonyTimer.Close();
			this.TanLang.PreliminaryApplicationCeremonyTimer.Dispose();
		}
	}

	public bool WhetherOnline()
	{
		if (TanLang.Client != null && TanLang.Client.Running && TanNuong.Client != null)
		{
			return TanNuong.Client.Running;
		}
		return false;
	}

	public void ToanBo_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		if ((int)ToanBo_KetThucThoiGian.Subtract(DateTime.Now).TotalSeconds > 0)
		{
			return;
		}
		Wedding value;
		try
		{
			if (FLD_MAP == 9101)
			{
				World.IsTheDragonHallInUse = false;
			}
			else if (FLD_MAP == 9001)
			{
				World.IsHuaMarriageHallInUse = false;
			}
			else if (FLD_MAP == 9201)
			{
				World.WhetherTheSacramentalHallIsInUse = false;
			}
			if (HenGioKetThuc4 != null)
			{
				HenGioKetThuc4.Enabled = false;
				HenGioKetThuc4.Close();
				HenGioKetThuc4.Dispose();
			}
			if (World.Weddinglist.TryGetValue(FLD_MAP, out value))
			{
				World.Weddinglist.Remove(FLD_MAP);
			}
			LogHelper.WriteLine(LogLevel.Error, "[" + TenSanhTiecCuoi + "] Wedding tất cả các KetThuc [" + TanLang.CharacterName + "][ " + TanNuong.CharacterName + "]");
			Dispose();
		}
		catch
		{
			if (FLD_MAP == 9101)
			{
				World.IsTheDragonHallInUse = false;
			}
			else if (FLD_MAP == 9001)
			{
				World.IsHuaMarriageHallInUse = false;
			}
			else if (FLD_MAP == 9201)
			{
				World.WhetherTheSacramentalHallIsInUse = false;
			}
			if (HenGioKetThuc4 != null)
			{
				HenGioKetThuc4.Enabled = false;
				HenGioKetThuc4.Close();
				HenGioKetThuc4.Dispose();
			}
			if (World.Weddinglist.TryGetValue(FLD_MAP, out value))
			{
				World.Weddinglist.Remove(FLD_MAP);
			}
			Dispose();
		}
		finally
		{
			if (FLD_MAP == 9101)
			{
				World.IsTheDragonHallInUse = false;
			}
			else if (FLD_MAP == 9001)
			{
				World.IsHuaMarriageHallInUse = false;
			}
			else if (FLD_MAP == 9201)
			{
				World.WhetherTheSacramentalHallIsInUse = false;
			}
			if (HenGioKetThuc4 != null)
			{
				HenGioKetThuc4.Enabled = false;
				HenGioKetThuc4.Close();
				HenGioKetThuc4.Dispose();
			}
			if (World.Weddinglist.TryGetValue(FLD_MAP, out value))
			{
				World.Weddinglist.Remove(FLD_MAP);
			}
			Dispose();
		}
	}

	private void ClearWalkingState(Players play)
	{
		try
		{
			if (play.GetAddState(601101))
			{
				play.AppendStatusList[601101].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(601102))
			{
				play.AppendStatusList[601102].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(601103))
			{
				play.AppendStatusList[601103].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001101))
			{
				play.AppendStatusList[1001101].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001102))
			{
				play.AppendStatusList[1001102].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001201))
			{
				play.AppendStatusList[1001201].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001202))
			{
				play.AppendStatusList[1001202].ThoiGianKetThucSuKien();
			}
			if (play.GetAddState(1001202))
			{
				play.AppendStatusList[1001202].ThoiGianKetThucSuKien();
			}
			play.WalkingStatusId = 1;
		}
		catch
		{
		}
	}

	public void ChuanBi_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
			if (num <= 0)
			{
				Wedding_Progress = 1;
			}
			kssjint = num;
			World.GuiThongBao((num + 59) / 60 + "分钟后," + TanLang.CharacterName + "和" + TanNuong.CharacterName + "将举行隆重Wedding仪式.参加可用命令 !DiDong " + TenSanhTiecCuoi);
			if (kssjint > 0)
			{
				return;
			}
			if (!WhetherOnline())
			{
				ChuanBiBoDemThoiGian.Enabled = false;
				ChuanBiBoDemThoiGian.Close();
				ChuanBiBoDemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(9.0);
				HenGioKetThuc1 = new(3000.0);
				HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
				HenGioKetThuc1.Enabled = true;
				HenGioKetThuc1.AutoReset = true;
				return;
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == FLD_MAP)
				{
					ClearWalkingState(value);
					value.CoupleTips(74, TanLang.CharacterName, TanNuong.CharacterName);
				}
			}
			if (FLD_MAP == 9201)
			{
				TanLang.Mobile(-59f, 53f, 15f, 9201, 0);
				TanNuong.Mobile(-48f, -104f, 15f, 9201, 0);
			}
			else if (FLD_MAP == 9101)
			{
				TanLang.Mobile(5f, -170f, 15f, 9101, 0);
				TanNuong.Mobile(-5f, 137f, 15f, 9101, 0);
			}
			else if (FLD_MAP == 9001)
			{
				TanLang.Mobile(-10f, 33f, 15f, 9001, 0);
				TanNuong.Mobile(10f, -101f, 15f, 9001, 0);
			}
			var num2 = 100;
			if (FLD_MAP == 9001 || FLD_MAP == 9101)
			{
				num2 = 80;
			}
			TanLang.HeThongNhacNho("Hôn lễ bắt đầu, hai vị tân nhân hãy giữ tâm tĩnh lặng, chớ để tâm thần dao động, chuyên tâm đón nhận thiên mệnh kết duyên!", 6, "Thiên Cơ Lệnh");
			TanNuong.HeThongNhacNho("Hôn lễ bắt đầu, hai vị tân nhân hãy giữ tâm tĩnh lặng, chớ để tâm thần dao động, chuyên tâm đón nhận thiên mệnh kết duyên!", 6, "Thiên Cơ Lệnh");
			Wedding_Progress = 1;
			ChuanBiBoDemThoiGian.Enabled = false;
			ChuanBiBoDemThoiGian.Close();
			ChuanBiBoDemThoiGian.Dispose();
			kssjgj = DateTime.Now.AddSeconds(num2);
			BatDauHenGio = new(5000.0);
			BatDauHenGio.Elapsed += BatDau_ThoiGianKetThucSuKien;
			BatDauHenGio.Enabled = true;
			BatDauHenGio.AutoReset = true;
		}
		catch
		{
		}
	}

	public void BatDau_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds > 0)
			{
				return;
			}
			if (!WhetherOnline())
			{
				BatDauHenGio.Enabled = false;
				BatDauHenGio.Close();
				BatDauHenGio.Dispose();
				kssjgj = DateTime.Now.AddSeconds(9.0);
				HenGioKetThuc1 = new(3000.0);
				HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
				HenGioKetThuc1.Enabled = true;
				HenGioKetThuc1.AutoReset = true;
				return;
			}
			Wedding_Progress = 2;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == FLD_MAP)
				{
					ClearWalkingState(value);
					value.CoupleTips(75, TanLang.CharacterName, TanNuong.CharacterName);
				}
			}
			var num = 30;
			if (FLD_MAP == 9101)
			{
				num = 35;
			}
			else if (FLD_MAP == 9001)
			{
				num = 30;
			}
			BatDauHenGio.Enabled = false;
			BatDauHenGio.Close();
			BatDauHenGio.Dispose();
			kssjgj = DateTime.Now.AddSeconds(num);
			TanNuongBatDau_DemThoiGian = new(3000.0);
			TanNuongBatDau_DemThoiGian.Elapsed += TanNuongRaTran_ThoiGianKetThucSuKien;
			TanNuongBatDau_DemThoiGian.Enabled = true;
			TanNuongBatDau_DemThoiGian.AutoReset = true;
		}
		catch
		{
		}
	}

	public void TanNuongRaTran_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds > 0)
			{
				return;
			}
			if (!WhetherOnline())
			{
				TanNuongBatDau_DemThoiGian.Enabled = false;
				TanNuongBatDau_DemThoiGian.Close();
				TanNuongBatDau_DemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(9.0);
				HenGioKetThuc1 = new(3000.0);
				HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
				HenGioKetThuc1.Enabled = true;
				HenGioKetThuc1.AutoReset = true;
				return;
			}
			Wedding_Progress = 3;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == FLD_MAP)
				{
					ClearWalkingState(value);
					value.CoupleTips(76, TanLang.CharacterName, TanNuong.CharacterName);
				}
			}
			TanNuongBatDau_DemThoiGian.Enabled = false;
			TanNuongBatDau_DemThoiGian.Close();
			TanNuongBatDau_DemThoiGian.Dispose();
			kssjgj = DateTime.Now.AddSeconds(60.0);
			BoDemThoiGianPhatBieu = new(3000.0);
			BoDemThoiGianPhatBieu.Elapsed += DoiLoiChaoMung_ThoiGianKetThucSuKien;
			BoDemThoiGianPhatBieu.Enabled = true;
			BoDemThoiGianPhatBieu.AutoReset = true;
		}
		catch
		{
		}
	}

	public void DoiLoiChaoMung_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds > 0)
			{
				return;
			}
			if (!WhetherOnline())
			{
				BoDemThoiGianPhatBieu.Enabled = false;
				BoDemThoiGianPhatBieu.Close();
				BoDemThoiGianPhatBieu.Dispose();
				kssjgj = DateTime.Now.AddSeconds(9.0);
				HenGioKetThuc1 = new(3000.0);
				HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
				HenGioKetThuc1.Enabled = true;
				HenGioKetThuc1.AutoReset = true;
				return;
			}
			Wedding_Progress = 4;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == FLD_MAP)
				{
					ClearWalkingState(value);
					value.CoupleTips(77, TanLang.CharacterName, TanNuong.CharacterName, 1);
				}
			}
			BoDemThoiGianPhatBieu.Enabled = false;
			BoDemThoiGianPhatBieu.Close();
			BoDemThoiGianPhatBieu.Dispose();
			kssjgj = DateTime.Now.AddSeconds(60.0);
			DatCauHoi_TanLang_DemThoiGian = new(1000.0);
			DatCauHoi_TanLang_DemThoiGian.Elapsed += DatCauHoiTanLang_ThoiGianKetThucSuKien;
			DatCauHoi_TanLang_DemThoiGian.Enabled = true;
			DatCauHoi_TanLang_DemThoiGian.AutoReset = true;
		}
		catch
		{
		}
	}

	public void DatCauHoiTanLang_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (!WhetherOnline())
			{
				DatCauHoi_TanLang_DemThoiGian.Enabled = false;
				DatCauHoi_TanLang_DemThoiGian.Close();
				DatCauHoi_TanLang_DemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(9.0);
				HenGioKetThuc1 = new(3000.0);
				HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
				HenGioKetThuc1.Enabled = true;
				HenGioKetThuc1.AutoReset = true;
			}
			else if (TanLang.HonNhan_NguoiDat_CauHoiDapAn == 1)
			{
				Wedding_Progress = 5;
				foreach (var value in World.allConnectedChars.Values)
				{
					if (value.MapID == FLD_MAP)
					{
						ClearWalkingState(value);
						value.CoupleTips(77, TanLang.CharacterName, TanNuong.CharacterName, 2);
					}
				}
				DatCauHoi_TanLang_DemThoiGian.Enabled = false;
				DatCauHoi_TanLang_DemThoiGian.Close();
				DatCauHoi_TanLang_DemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(60.0);
				DatCauHoi_TanNuong_DemThoiGian = new(1000.0);
				DatCauHoi_TanNuong_DemThoiGian.Elapsed += DatCauHoiTanNuong_ThoiGianKetThucSuKien;
				DatCauHoi_TanNuong_DemThoiGian.Enabled = true;
				DatCauHoi_TanNuong_DemThoiGian.AutoReset = true;
			}
			else if (TanLang.HonNhan_NguoiDat_CauHoiDapAn == 0)
			{
				Wedding_Progress = 11;
				foreach (var value2 in World.allConnectedChars.Values)
				{
					if (value2.MapID == FLD_MAP)
					{
						ClearWalkingState(value2);
						value2.CoupleTips(83, TanLang.CharacterName, TanNuong.CharacterName);
					}
				}
				DatCauHoi_TanLang_DemThoiGian.Enabled = false;
				DatCauHoi_TanLang_DemThoiGian.Close();
				DatCauHoi_TanLang_DemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(60.0);
				HenGioKetThuc3 = new(3000.0);
				HenGioKetThuc3.Elapsed += ThoiGianKetThucSuKien3;
				HenGioKetThuc3.Enabled = true;
				HenGioKetThuc3.AutoReset = true;
			}
			else
			{
				if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds > 0)
				{
					return;
				}
				if (TanLang.HonNhan_NguoiDat_CauHoiDapAn == 2)
				{
					foreach (var value3 in World.allConnectedChars.Values)
					{
						if (value3.MapID == FLD_MAP)
						{
							ClearWalkingState(value3);
							value3.CoupleTips(77, TanLang.CharacterName, TanNuong.CharacterName, 2);
						}
					}
					DatCauHoi_TanLang_DemThoiGian.Enabled = false;
					DatCauHoi_TanLang_DemThoiGian.Close();
					DatCauHoi_TanLang_DemThoiGian.Dispose();
					kssjgj = DateTime.Now.AddSeconds(60.0);
					DatCauHoi_TanNuong_DemThoiGian = new(1000.0);
					DatCauHoi_TanNuong_DemThoiGian.Elapsed += DatCauHoiTanNuong_ThoiGianKetThucSuKien;
					DatCauHoi_TanNuong_DemThoiGian.Enabled = true;
					DatCauHoi_TanNuong_DemThoiGian.AutoReset = true;
					return;
				}
				Wedding_Progress = 11;
				foreach (var value4 in World.allConnectedChars.Values)
				{
					if (value4.MapID == FLD_MAP)
					{
						ClearWalkingState(value4);
						value4.CoupleTips(83, TanLang.CharacterName, TanNuong.CharacterName);
					}
				}
				DatCauHoi_TanNuong_DemThoiGian.Enabled = false;
				DatCauHoi_TanNuong_DemThoiGian.Close();
				DatCauHoi_TanNuong_DemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(60.0);
				HenGioKetThuc3 = new(3000.0);
				HenGioKetThuc3.Elapsed += ThoiGianKetThucSuKien3;
				HenGioKetThuc3.Enabled = true;
				HenGioKetThuc3.AutoReset = true;
			}
		}
		catch
		{
		}
	}

	public void DatCauHoiTanNuong_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (!WhetherOnline())
			{
				DatCauHoi_TanNuong_DemThoiGian.Enabled = false;
				DatCauHoi_TanNuong_DemThoiGian.Close();
				DatCauHoi_TanNuong_DemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(9.0);
				HenGioKetThuc1 = new(3000.0);
				HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
				HenGioKetThuc1.Enabled = true;
				HenGioKetThuc1.AutoReset = true;
			}
			else if (TanNuong.HonNhan_NguoiDat_CauHoiDapAn == 1)
			{
				Wedding_Progress = 6;
				foreach (var value2 in World.allConnectedChars.Values)
				{
					if (value2.MapID == FLD_MAP)
					{
						ClearWalkingState(value2);
						value2.CoupleTips(78, TanLang.CharacterName, TanNuong.CharacterName);
						if (value2.GetAddState(242))
						{
							value2.AppendStatusList[242].ThoiGianKetThucSuKien();
						}
						X_Them_Vao_Trang_Thai_Loai value = new(value2, 3600000, 242, 0);
						value2.AppendStatusList.Add(242, value);
						value2.StatusEffect(242, 1, 1, 3600000);
						value2.FLD_NhanVat_ThemVao_CongKich += 15;
						value2.FLD_NhanVat_ThemVao_PhongNgu += 15;
						value2.CharactersToAddMax_HP += 300;
						value2.CharactersToAddMax_MP += 300;
						value2.FLD_NhanVat_ThemVao_KinhNghiem_KetHon = 0.2;
						value2.FLD_KetHonLeVat_ThemVaoThuocTinhThach = 1;
						value2.UpdateMartialArtsAndStatus();
						value2.CapNhat_HP_MP_SP();
					}
				}
				BatPhat_Wedding_PhanThuong();
				TanLang.UpdateCharacterData(TanLang);
				TanNuong.UpdateCharacterData(TanNuong);
				TanNuong.UpdateBroadcastCharacterData();
				TanLang.UpdateBroadcastCharacterData();
				DatCauHoi_TanNuong_DemThoiGian.Enabled = false;
				DatCauHoi_TanNuong_DemThoiGian.Close();
				DatCauHoi_TanNuong_DemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(25.0);
				TraLoiKetQua_MayDemThoiGian = new(3000.0);
				TraLoiKetQua_MayDemThoiGian.Elapsed += TraLoiCauHoi_ThoiGianKetThucSuKien;
				TraLoiKetQua_MayDemThoiGian.Enabled = true;
				TraLoiKetQua_MayDemThoiGian.AutoReset = true;
			}
			else if (TanNuong.HonNhan_NguoiDat_CauHoiDapAn == 0)
			{
				Wedding_Progress = 11;
				foreach (var value3 in World.allConnectedChars.Values)
				{
					if (value3.MapID == FLD_MAP)
					{
						ClearWalkingState(value3);
						value3.CoupleTips(83, TanLang.CharacterName, TanNuong.CharacterName);
					}
				}
				DatCauHoi_TanNuong_DemThoiGian.Enabled = false;
				DatCauHoi_TanNuong_DemThoiGian.Close();
				DatCauHoi_TanNuong_DemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(60.0);
				HenGioKetThuc3 = new(3000.0);
				HenGioKetThuc3.Elapsed += ThoiGianKetThucSuKien3;
				HenGioKetThuc3.Enabled = true;
				HenGioKetThuc3.AutoReset = true;
			}
			else
			{
				if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds > 0)
				{
					return;
				}
				if (TanNuong.HonNhan_NguoiDat_CauHoiDapAn == 2)
				{
					foreach (var value4 in World.allConnectedChars.Values)
					{
						if (value4.MapID == FLD_MAP)
						{
							ClearWalkingState(value4);
							value4.CoupleTips(78, TanLang.CharacterName, TanNuong.CharacterName);
						}
					}
					BatPhat_Wedding_PhanThuong();
					DatCauHoi_TanNuong_DemThoiGian.Enabled = false;
					DatCauHoi_TanNuong_DemThoiGian.Close();
					DatCauHoi_TanNuong_DemThoiGian.Dispose();
					kssjgj = DateTime.Now.AddSeconds(25.0);
					TraLoiKetQua_MayDemThoiGian = new(3000.0);
					TraLoiKetQua_MayDemThoiGian.Elapsed += TraLoiCauHoi_ThoiGianKetThucSuKien;
					TraLoiKetQua_MayDemThoiGian.Enabled = true;
					TraLoiKetQua_MayDemThoiGian.AutoReset = true;
					return;
				}
				Wedding_Progress = 11;
				foreach (var value5 in World.allConnectedChars.Values)
				{
					if (value5.MapID == FLD_MAP)
					{
						ClearWalkingState(value5);
						value5.CoupleTips(83, TanLang.CharacterName, TanNuong.CharacterName);
					}
				}
				DatCauHoi_TanNuong_DemThoiGian.Enabled = false;
				DatCauHoi_TanNuong_DemThoiGian.Close();
				DatCauHoi_TanNuong_DemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(60.0);
				HenGioKetThuc3 = new(3000.0);
				HenGioKetThuc3.Elapsed += ThoiGianKetThucSuKien3;
				HenGioKetThuc3.Enabled = true;
				HenGioKetThuc3.AutoReset = true;
			}
		}
		catch
		{
		}
	}

	public void TraLoiCauHoi_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds > 0)
			{
				return;
			}
			if (!WhetherOnline())
			{
				TraLoiKetQua_MayDemThoiGian.Enabled = false;
				TraLoiKetQua_MayDemThoiGian.Close();
				TraLoiKetQua_MayDemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(9.0);
				HenGioKetThuc1 = new(3000.0);
				HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
				HenGioKetThuc1.Enabled = true;
				HenGioKetThuc1.AutoReset = true;
				return;
			}
			Wedding_Progress = 7;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == FLD_MAP)
				{
					ClearWalkingState(value);
					value.CoupleTips(79, TanLang.CharacterName, TanNuong.CharacterName);
				}
			}
			TraLoiKetQua_MayDemThoiGian.Enabled = false;
			TraLoiKetQua_MayDemThoiGian.Close();
			TraLoiKetQua_MayDemThoiGian.Dispose();
			kssjgj = DateTime.Now.AddSeconds(10.0);
			TraDoiNhanChoNhau_MayDemThoiGian = new(1000.0);
			TraDoiNhanChoNhau_MayDemThoiGian.Elapsed += TraoDoiNhanCuoi_ThoiGianKetThucSuKien;
			TraDoiNhanChoNhau_MayDemThoiGian.Enabled = true;
			TraDoiNhanChoNhau_MayDemThoiGian.AutoReset = true;
		}
		catch
		{
		}
	}

	public void TraoDoiNhanCuoi_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds > 0)
			{
				return;
			}
			if (!WhetherOnline())
			{
				TraDoiNhanChoNhau_MayDemThoiGian.Enabled = false;
				TraDoiNhanChoNhau_MayDemThoiGian.Close();
				TraDoiNhanChoNhau_MayDemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(9.0);
				HenGioKetThuc1 = new(3000.0);
				HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
				HenGioKetThuc1.Enabled = true;
				HenGioKetThuc1.AutoReset = true;
				return;
			}
			Wedding_Progress = 8;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == FLD_MAP)
				{
					ClearWalkingState(value);
					value.CoupleTips(80, TanLang.CharacterName, TanNuong.CharacterName);
				}
			}
			TraDoiNhanChoNhau_MayDemThoiGian.Enabled = false;
			TraDoiNhanChoNhau_MayDemThoiGian.Close();
			TraDoiNhanChoNhau_MayDemThoiGian.Dispose();
			kssjgj = DateTime.Now.AddSeconds(10.0);
			CamOnQuyKhach_MayDemThoiGian = new(1000.0);
			CamOnQuyKhach_MayDemThoiGian.Elapsed += TaOnQuyKhach_ThoiGianKetThucSuKien;
			CamOnQuyKhach_MayDemThoiGian.Enabled = true;
			CamOnQuyKhach_MayDemThoiGian.AutoReset = true;
		}
		catch
		{
		}
	}

	public void TaOnQuyKhach_ThoiGianKetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds > 0)
			{
				return;
			}
			if (!WhetherOnline())
			{
				CamOnQuyKhach_MayDemThoiGian.Enabled = false;
				CamOnQuyKhach_MayDemThoiGian.Close();
				CamOnQuyKhach_MayDemThoiGian.Dispose();
				kssjgj = DateTime.Now.AddSeconds(9.0);
				HenGioKetThuc1 = new(3000.0);
				HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
				HenGioKetThuc1.Enabled = true;
				HenGioKetThuc1.AutoReset = true;
				return;
			}
			Wedding_Progress = 9;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == FLD_MAP)
				{
					ClearWalkingState(value);
					value.CoupleTips(81, TanLang.CharacterName, TanNuong.CharacterName);
				}
			}
			var num = 30;
			if (FLD_MAP == 9101)
			{
				num = 35;
			}
			CamOnQuyKhach_MayDemThoiGian.Enabled = false;
			CamOnQuyKhach_MayDemThoiGian.Close();
			CamOnQuyKhach_MayDemThoiGian.Dispose();
			kssjgj = DateTime.Now.AddSeconds(num);
			HenGioKetThuc1 = new(3000.0);
			HenGioKetThuc1.Elapsed += ThoiGianKetThucSuKien1;
			HenGioKetThuc1.Enabled = true;
			HenGioKetThuc1.AutoReset = true;
		}
		catch
		{
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds > 0)
			{
				return;
			}
			Wedding_Progress = 10;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == FLD_MAP)
				{
					ClearWalkingState(value);
					value.CoupleTips(82, TanLang.CharacterName, TanNuong.CharacterName);
				}
			}
			HenGioKetThuc1.Enabled = false;
			HenGioKetThuc1.Close();
			HenGioKetThuc1.Dispose();
			kssjgj = DateTime.Now.AddMinutes(2.0);
			HenGioKetThuc2 = new(5000.0);
			HenGioKetThuc2.Elapsed += ThoiGianKetThucSuKien2;
			HenGioKetThuc2.Enabled = true;
			HenGioKetThuc2.AutoReset = true;
		}
		catch
		{
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)kssjgj.Subtract(DateTime.Now).TotalSeconds <= 0)
			{
				Wedding_Progress = 11;
				HenGioKetThuc2.Enabled = false;
				HenGioKetThuc2.Close();
				HenGioKetThuc2.Dispose();
				Dispose();
			}
		}
		catch
		{
		}
	}

	public void ThoiGianKetThucSuKien3(object sender, ElapsedEventArgs e)
	{
		try
		{
			HenGioKetThuc3.Enabled = false;
			HenGioKetThuc3.Close();
			HenGioKetThuc3.Dispose();
			Dispose();
		}
		catch
		{
		}
	}

	public void BatPhat_Wedding_PhanThuong()
	{
		try
		{
			var packagedItems = TanLang.GetPackagedItems(1000000416);
			if (packagedItems != null)
			{
				TanLang.SubtractItem(packagedItems.VatPhamViTri, 1);
			}
			var packagedItems2 = TanNuong.GetPackagedItems(1000000416);
			if (packagedItems2 != null)
			{
				TanNuong.SubtractItem(packagedItems2.VatPhamViTri, 1);
			}
			var parcelVacancy = TanLang.GetParcelVacancy(TanLang);
			if (parcelVacancy != -1)
			{
				TanLang.AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000415), parcelVacancy, BitConverter.GetBytes(1), new byte[56]);
			}
			var parcelVacancy2 = TanLang.GetParcelVacancy(TanLang);
			if (parcelVacancy2 != -1)
			{
				TanLang.AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000200), parcelVacancy2, BitConverter.GetBytes(1), new byte[56]);
			}
			var parcelVacancy3 = TanLang.GetParcelVacancy(TanLang);
			if (parcelVacancy3 != -1)
			{
				TanLang.AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000213), parcelVacancy3, BitConverter.GetBytes(1), new byte[56]);
			}
			var parcelVacancy4 = TanNuong.GetParcelVacancy(TanNuong);
			if (parcelVacancy4 != -1)
			{
				TanNuong.AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000415), parcelVacancy4, BitConverter.GetBytes(1), new byte[56]);
			}
			var parcelVacancy5 = TanNuong.GetParcelVacancy(TanNuong);
			if (parcelVacancy5 != -1)
			{
				TanNuong.AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000200), parcelVacancy5, BitConverter.GetBytes(1), new byte[56]);
			}
			var parcelVacancy6 = TanNuong.GetParcelVacancy(TanNuong);
			if (parcelVacancy6 != -1)
			{
				TanNuong.AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000000213), parcelVacancy6, BitConverter.GetBytes(1), new byte[56]);
			}
			var now = DateTime.Now;
			TanLang.FLD_NgayKiNiemKetHon = now;
			TanNuong.FLD_NgayKiNiemKetHon = now;
			TanLang.WhetherMarried = 1;
			TanNuong.WhetherMarried = 1;
			TanLang.NhanCuoiKhacChu = TanNuong.CharacterName + ",我爱你一生一世!!!";
			TanNuong.NhanCuoiKhacChu = TanLang.CharacterName + ",我爱你一生一世!!!";
		}
		catch
		{
		}
	}

	public void Dispose()
	{
		try
		{
			Wedding_Progress = 0;
			TanLang.WeddingMap = 0;
			TanLang.WeddingAdmissionTicket = 0;
			TanLang.WeddingPattern = 0;
			TanNuong.WeddingMap = 0;
			TanNuong.WeddingAdmissionTicket = 0;
			TanNuong.WeddingPattern = 0;
			if (ChuanBiBoDemThoiGian != null)
			{
				ChuanBiBoDemThoiGian.Enabled = false;
				ChuanBiBoDemThoiGian.Close();
				ChuanBiBoDemThoiGian.Dispose();
			}
			if (BatDauHenGio != null)
			{
				BatDauHenGio.Enabled = false;
				BatDauHenGio.Close();
				BatDauHenGio.Dispose();
			}
			if (TanNuongBatDau_DemThoiGian != null)
			{
				TanNuongBatDau_DemThoiGian.Enabled = false;
				TanNuongBatDau_DemThoiGian.Close();
				TanNuongBatDau_DemThoiGian.Dispose();
			}
			if (BoDemThoiGianPhatBieu != null)
			{
				BoDemThoiGianPhatBieu.Enabled = false;
				BoDemThoiGianPhatBieu.Close();
				BoDemThoiGianPhatBieu.Dispose();
			}
			if (DatCauHoi_TanNuong_DemThoiGian != null)
			{
				DatCauHoi_TanNuong_DemThoiGian.Enabled = false;
				DatCauHoi_TanNuong_DemThoiGian.Close();
				DatCauHoi_TanNuong_DemThoiGian.Dispose();
			}
			if (DatCauHoi_TanLang_DemThoiGian != null)
			{
				DatCauHoi_TanLang_DemThoiGian.Enabled = false;
				DatCauHoi_TanLang_DemThoiGian.Close();
				DatCauHoi_TanLang_DemThoiGian.Dispose();
			}
			if (TraLoiKetQua_MayDemThoiGian != null)
			{
				TraLoiKetQua_MayDemThoiGian.Enabled = false;
				TraLoiKetQua_MayDemThoiGian.Close();
				TraLoiKetQua_MayDemThoiGian.Dispose();
			}
			if (TraDoiNhanChoNhau_MayDemThoiGian != null)
			{
				TraDoiNhanChoNhau_MayDemThoiGian.Enabled = false;
				TraDoiNhanChoNhau_MayDemThoiGian.Close();
				TraDoiNhanChoNhau_MayDemThoiGian.Dispose();
			}
			if (CamOnQuyKhach_MayDemThoiGian != null)
			{
				CamOnQuyKhach_MayDemThoiGian.Enabled = false;
				CamOnQuyKhach_MayDemThoiGian.Close();
				CamOnQuyKhach_MayDemThoiGian.Dispose();
			}
			if (HenGioKetThuc1 != null)
			{
				HenGioKetThuc1.Enabled = false;
				HenGioKetThuc1.Close();
				HenGioKetThuc1.Dispose();
			}
			if (HenGioKetThuc2 != null)
			{
				HenGioKetThuc2.Enabled = false;
				HenGioKetThuc2.Close();
				HenGioKetThuc2.Dispose();
			}
			if (HenGioKetThuc3 != null)
			{
				HenGioKetThuc3.Enabled = false;
				HenGioKetThuc3.Close();
				HenGioKetThuc3.Dispose();
			}
			if (World.Weddinglist.TryGetValue(FLD_MAP, out var _))
			{
				World.Weddinglist.Remove(FLD_MAP);
			}
			foreach (var value2 in World.allConnectedChars.Values)
			{
				var num = RNG.Next(1, 30);
				if (value2.MapID == FLD_MAP)
				{
					var num2 = RNG.Next(1, 30);
					value2.Mobile(num, num2, 15f, 101, 0);
				}
			}
		}
		catch
		{
		}
	}
}
