using System;

namespace RxjhServer;

public class X_Nguoi_Cua_Hang_Loai : IDisposable
{
	public Players NhanVaoNguoiMua;

	public int StoreType;

	private bool _CuaHangCaNhanPhaiChangMoRa;

	private bool _CuaHangCaNhan_PhaiChangDangSuDungBenTrong;

	private byte[] _StoreName;

	private ThreadSafeDictionary<long, X_Nguoi_Cua_Hang_Vat_Pham_Loai> _StoreItemList;

	public bool CuaHangCaNhanPhaiChangMoRa
	{
		get
		{
			return _CuaHangCaNhanPhaiChangMoRa;
		}
		set
		{
			_CuaHangCaNhanPhaiChangMoRa = value;
		}
	}

	public bool CuaHangCaNhan_PhaiChangDangSuDungBenTrong
	{
		get
		{
			return _CuaHangCaNhan_PhaiChangDangSuDungBenTrong;
		}
		set
		{
			_CuaHangCaNhan_PhaiChangDangSuDungBenTrong = value;
		}
	}

	public byte[] StoreName
	{
		get
		{
			return _StoreName;
		}
		set
		{
			_StoreName = value;
		}
	}

	public ThreadSafeDictionary<long, X_Nguoi_Cua_Hang_Vat_Pham_Loai> StoreItemList
	{
		get
		{
			return _StoreItemList;
		}
		set
		{
			_StoreItemList = value;
		}
	}

	public X_Nguoi_Cua_Hang_Loai()
	{
		_StoreItemList = new();
	}

	~X_Nguoi_Cua_Hang_Loai()
	{
	}

	public void Dispose()
	{
		if (StoreItemList != null)
		{
			StoreItemList.Clear();
			StoreItemList.Dispose();
			StoreItemList = null;
		}
		StoreName = null;
		NhanVaoNguoiMua = null;
	}
}
