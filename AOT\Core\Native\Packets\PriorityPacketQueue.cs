using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Packets
{
    /// <summary>
    /// Priority packet queue implementation - thay thế ConcurrentQueue trong PacketHandlerActor
    /// </summary>
    public class PriorityPacketQueue : IPriorityPacketQueue, IDisposable
    {
        private readonly Channel<QueueItem>[] _priorityChannels;
        private readonly ChannelWriter<QueueItem>[] _writers;
        private readonly ChannelReader<QueueItem>[] _readers;
        private readonly int _maxQueueSize;
        private volatile bool _isDisposed;
        private long _totalCount;

        public PriorityPacketQueue(int maxQueueSize = 10000)
        {
            _maxQueueSize = maxQueueSize;
            
            // Tạo channels cho mỗi priority level
            var priorityCount = Enum.GetValues<PacketPriority>().Length;
            _priorityChannels = new Channel<QueueItem>[priorityCount];
            _writers = new ChannelWriter<QueueItem>[priorityCount];
            _readers = new ChannelReader<QueueItem>[priorityCount];

            for (int i = 0; i < priorityCount; i++)
            {
                var options = new BoundedChannelOptions(_maxQueueSize / priorityCount)
                {
                    FullMode = BoundedChannelFullMode.Wait,
                    SingleReader = false,
                    SingleWriter = false,
                    AllowSynchronousContinuations = false
                };

                _priorityChannels[i] = Channel.CreateBounded<QueueItem>(options);
                _writers[i] = _priorityChannels[i].Writer;
                _readers[i] = _priorityChannels[i].Reader;
            }
        }

        public int Count => (int)Interlocked.Read(ref _totalCount);

        /// <summary>
        /// Enqueue packet với priority
        /// </summary>
        public async Task EnqueueAsync(IPacket packet, IPacketContext context)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(PriorityPacketQueue));

            var priorityIndex = (int)packet.Priority;
            var item = new QueueItem(packet, context);

            try
            {
                await _writers[priorityIndex].WriteAsync(item);
                Interlocked.Increment(ref _totalCount);
                
                Logger.Instance.Debug($"Enqueued packet {packet.Type} with priority {packet.Priority} for session {packet.SessionId}");
            }
            catch (InvalidOperationException)
            {
                // Channel was completed
                throw new InvalidOperationException("Packet queue is closed");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error enqueuing packet: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Dequeue packet với highest priority
        /// </summary>
        public async Task<(IPacket packet, IPacketContext context)?> DequeueAsync(CancellationToken cancellationToken = default)
        {
            if (_isDisposed)
                return null;

            // Kiểm tra từ priority cao nhất xuống thấp nhất
            for (int priority = _readers.Length - 1; priority >= 0; priority--)
            {
                var reader = _readers[priority];
                
                if (reader.TryRead(out var item))
                {
                    Interlocked.Decrement(ref _totalCount);
                    Logger.Instance.Debug($"Dequeued packet {item.Packet.Type} with priority {item.Packet.Priority}");
                    return (item.Packet, item.Context);
                }
            }

            // Nếu không có packet nào, chờ packet mới
            return await WaitForNextPacketAsync(cancellationToken);
        }

        /// <summary>
        /// Chờ packet tiếp theo từ bất kỳ priority nào
        /// </summary>
        private async Task<(IPacket packet, IPacketContext context)?> WaitForNextPacketAsync(CancellationToken cancellationToken)
        {
            var tasks = new Task<QueueItem?>[_readers.Length];
            
            // Tạo tasks để đọc từ mỗi priority channel
            for (int i = 0; i < _readers.Length; i++)
            {
                var reader = _readers[i];
                tasks[i] = ReadFromChannelAsync(reader, cancellationToken);
            }

            try
            {
                // Chờ task đầu tiên complete
                var completedTask = await Task.WhenAny(tasks);
                var result = await completedTask;

                if (result.HasValue)
                {
                    Interlocked.Decrement(ref _totalCount);
                    var item = result.Value;
                    Logger.Instance.Debug($"Dequeued packet {item.Packet.Type} with priority {item.Packet.Priority}");
                    return (item.Packet, item.Context);
                }

                return null;
            }
            catch (OperationCanceledException)
            {
                return null;
            }
        }

        /// <summary>
        /// Đọc từ channel với cancellation support
        /// </summary>
        private async Task<QueueItem?> ReadFromChannelAsync(ChannelReader<QueueItem> reader, CancellationToken cancellationToken)
        {
            try
            {
                if (await reader.WaitToReadAsync(cancellationToken))
                {
                    if (reader.TryRead(out var item))
                    {
                        return item;
                    }
                }
                return null;
            }
            catch (OperationCanceledException)
            {
                return null;
            }
        }

        /// <summary>
        /// Clear all queues
        /// </summary>
        public void Clear()
        {
            for (int i = 0; i < _readers.Length; i++)
            {
                var reader = _readers[i];
                while (reader.TryRead(out _))
                {
                    Interlocked.Decrement(ref _totalCount);
                }
            }

            Logger.Instance.Info("Cleared all packet queues");
        }

        /// <summary>
        /// Get queue statistics
        /// </summary>
        public QueueStatistics GetStatistics()
        {
            var stats = new QueueStatistics
            {
                TotalCount = Count,
                PriorityQueues = new int[_readers.Length]
            };

            for (int i = 0; i < _readers.Length; i++)
            {
                // Estimate count by trying to read without removing
                int count = 0;
                var reader = _readers[i];
                
                // This is an approximation since we can't get exact count without affecting the queue
                while (reader.TryPeek(out _))
                {
                    count++;
                    if (count > 1000) break; // Prevent infinite loop
                }
                
                stats.PriorityQueues[i] = count;
            }

            return stats;
        }

        public void Dispose()
        {
            if (_isDisposed)
                return;

            _isDisposed = true;

            try
            {
                // Complete all writers
                for (int i = 0; i < _writers.Length; i++)
                {
                    _writers[i].Complete();
                }

                Logger.Instance.Info("Priority packet queue disposed");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing priority packet queue: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Queue item wrapper
    /// </summary>
    internal struct QueueItem
    {
        public IPacket Packet { get; }
        public IPacketContext Context { get; }
        public DateTime EnqueueTime { get; }

        public QueueItem(IPacket packet, IPacketContext context)
        {
            Packet = packet;
            Context = context;
            EnqueueTime = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Queue statistics
    /// </summary>
    public class QueueStatistics
    {
        public int TotalCount { get; set; }
        public int[] PriorityQueues { get; set; } = Array.Empty<int>();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Simple priority queue implementation using concurrent collections
    /// Alternative implementation for scenarios where channels are not suitable
    /// </summary>
    public class SimplePriorityPacketQueue : IPriorityPacketQueue
    {
        private readonly ConcurrentQueue<QueueItem>[] _priorityQueues;
        private readonly SemaphoreSlim _semaphore;
        private volatile bool _isDisposed;
        private long _totalCount;

        public SimplePriorityPacketQueue()
        {
            var priorityCount = Enum.GetValues<PacketPriority>().Length;
            _priorityQueues = new ConcurrentQueue<QueueItem>[priorityCount];
            
            for (int i = 0; i < priorityCount; i++)
            {
                _priorityQueues[i] = new ConcurrentQueue<QueueItem>();
            }

            _semaphore = new SemaphoreSlim(0);
        }

        public int Count => (int)Interlocked.Read(ref _totalCount);

        public async Task EnqueueAsync(IPacket packet, IPacketContext context)
        {
            if (_isDisposed)
                throw new ObjectDisposedException(nameof(SimplePriorityPacketQueue));

            var priorityIndex = (int)packet.Priority;
            var item = new QueueItem(packet, context);

            _priorityQueues[priorityIndex].Enqueue(item);
            Interlocked.Increment(ref _totalCount);
            
            _semaphore.Release();
        }

        public async Task<(IPacket packet, IPacketContext context)?> DequeueAsync(CancellationToken cancellationToken = default)
        {
            if (_isDisposed)
                return null;

            await _semaphore.WaitAsync(cancellationToken);

            // Check from highest priority to lowest
            for (int priority = _priorityQueues.Length - 1; priority >= 0; priority--)
            {
                if (_priorityQueues[priority].TryDequeue(out var item))
                {
                    Interlocked.Decrement(ref _totalCount);
                    return (item.Packet, item.Context);
                }
            }

            return null;
        }

        public void Clear()
        {
            for (int i = 0; i < _priorityQueues.Length; i++)
            {
                while (_priorityQueues[i].TryDequeue(out _))
                {
                    Interlocked.Decrement(ref _totalCount);
                }
            }
        }

        public void Dispose()
        {
            if (_isDisposed)
                return;

            _isDisposed = true;
            _semaphore?.Dispose();
        }
    }
}
