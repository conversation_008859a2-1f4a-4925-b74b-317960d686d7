﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_guildmember {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string g_name { get; set; }

		[JsonProperty]
		public int? leve { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty]
		public int? fld_guildpoint { get; set; }

		[JsonProperty]
		public int? fld_newguildpoint { get; set; }

		[JsonProperty]
		public bool active { get; set; } = false;

		[JsonProperty, Column(DbType = "date", InsertValueSql = "now()")]
		public DateTime createdat { get; set; }

		[JsonProperty, Column(DbType = "date", InsertValueSql = "now()")]
		public DateTime updatedat { get; set; }

	}

}
