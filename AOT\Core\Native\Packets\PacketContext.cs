using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Network;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Packets
{
    /// <summary>
    /// Packet context implementation - thông tin về session và connection
    /// </summary>
    public class PacketContext : IPacketContext
    {
        private readonly IClientSession _session;

        public PacketContext(IClientSession session)
        {
            _session = session ?? throw new ArgumentNullException(nameof(session));
        }

        public int SessionId => _session.SessionId;
        public IClientSession Session => _session;
        public IDictionary<string, object> Properties => _session.Properties;

        /// <summary>
        /// Send response packet
        /// </summary>
        public async Task SendAsync(byte[] data)
        {
            try
            {
                await _session.SendAsync(data);
                Logger.Instance.Debug($"Sent {data.Length} bytes to session {SessionId}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error sending data to session {SessionId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Close connection
        /// </summary>
        public async Task CloseAsync()
        {
            try
            {
                await _session.CloseAsync();
                Logger.Instance.Info($"Closed session {SessionId}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error closing session {SessionId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get session property
        /// </summary>
        public T? GetProperty<T>(string key)
        {
            if (_session is ClientSession clientSession)
            {
                return clientSession.GetProperty<T>(key);
            }

            return Properties.TryGetValue(key, out var value) && value is T typedValue ? typedValue : default;
        }

        /// <summary>
        /// Set session property
        /// </summary>
        public void SetProperty<T>(string key, T value)
        {
            if (_session is ClientSession clientSession)
            {
                clientSession.SetProperty(key, value);
            }
            else if (value != null)
            {
                Properties[key] = value;
            }
            else
            {
                Properties.Remove(key);
            }
        }

        /// <summary>
        /// Check if session is authenticated
        /// </summary>
        public bool IsAuthenticated()
        {
            return _session.State == SessionState.Authenticated;
        }

        /// <summary>
        /// Set session as authenticated
        /// </summary>
        public void SetAuthenticated()
        {
            if (_session is ClientSession clientSession)
            {
                clientSession.SetState(SessionState.Authenticated);
            }
        }

        /// <summary>
        /// Get player from session
        /// </summary>
        public T? GetPlayer<T>() where T : class
        {
            return GetProperty<T>("Player");
        }

        /// <summary>
        /// Set player for session
        /// </summary>
        public void SetPlayer<T>(T player) where T : class
        {
            SetProperty("Player", player);
        }

        /// <summary>
        /// Send encrypted packet (for compatibility with existing system)
        /// </summary>
        public async Task SendEncryptedAsync(byte[] data)
        {
            try
            {
                // Apply encryption if needed (similar to ActorNetState.Send)
                var encryptedData = EncryptPacket(data);
                await SendAsync(encryptedData);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error sending encrypted data to session {SessionId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Encrypt packet data (placeholder - implement actual encryption)
        /// </summary>
        private byte[] EncryptPacket(byte[] data)
        {
            // TODO: Implement actual encryption logic from Crypto.EncryptPacket
            // For now, return data as-is
            return data;
        }

        /// <summary>
        /// Send packet with session ID header (for compatibility)
        /// </summary>
        public async Task SendWithSessionHeaderAsync(byte[] data)
        {
            try
            {
                // Add session ID to packet header (similar to existing system)
                var packetWithHeader = new byte[data.Length + 2];
                Array.Copy(data, 0, packetWithHeader, 0, 4); // Copy first 4 bytes
                BitConverter.GetBytes((short)SessionId).CopyTo(packetWithHeader, 4); // Insert session ID
                Array.Copy(data, 4, packetWithHeader, 6, data.Length - 4); // Copy rest

                await SendEncryptedAsync(packetWithHeader);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error sending packet with session header to session {SessionId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get session uptime
        /// </summary>
        public TimeSpan GetUptime()
        {
            return DateTime.UtcNow - _session.CreatedAt;
        }

        /// <summary>
        /// Get idle time
        /// </summary>
        public TimeSpan GetIdleTime()
        {
            return DateTime.UtcNow - _session.LastActivity;
        }

        /// <summary>
        /// Check if session is active
        /// </summary>
        public bool IsActive()
        {
            return _session.IsActive;
        }

        /// <summary>
        /// Get remote endpoint
        /// </summary>
        public string GetRemoteEndPoint()
        {
            return _session.RemoteEndPoint.ToString();
        }
    }

    /// <summary>
    /// Packet context factory
    /// </summary>
    public static class PacketContextFactory
    {
        /// <summary>
        /// Create packet context from session
        /// </summary>
        public static IPacketContext Create(IClientSession session)
        {
            return new PacketContext(session);
        }

        /// <summary>
        /// Create packet context with additional properties
        /// </summary>
        public static IPacketContext Create(IClientSession session, IDictionary<string, object> additionalProperties)
        {
            var context = new PacketContext(session);
            
            foreach (var kvp in additionalProperties)
            {
                context.Properties[kvp.Key] = kvp.Value;
            }

            return context;
        }
    }

    /// <summary>
    /// Packet context extensions
    /// </summary>
    public static class PacketContextExtensions
    {
        /// <summary>
        /// Send string data
        /// </summary>
        public static async Task SendStringAsync(this IPacketContext context, string data)
        {
            var bytes = System.Text.Encoding.UTF8.GetBytes(data);
            await context.SendAsync(bytes);
        }

        /// <summary>
        /// Send JSON data
        /// </summary>
        public static async Task SendJsonAsync<T>(this IPacketContext context, T data)
        {
            var json = System.Text.Json.JsonSerializer.Serialize(data);
            await context.SendStringAsync(json);
        }

        /// <summary>
        /// Log packet context info
        /// </summary>
        public static void LogInfo(this IPacketContext context, string message)
        {
            Logger.Instance.Info($"[Session {context.SessionId}] {message}");
        }

        /// <summary>
        /// Log packet context error
        /// </summary>
        public static void LogError(this IPacketContext context, string message, Exception? exception = null)
        {
            if (exception != null)
            {
                Logger.Instance.Error($"[Session {context.SessionId}] {message}: {exception.Message}");
            }
            else
            {
                Logger.Instance.Error($"[Session {context.SessionId}] {message}");
            }
        }

        /// <summary>
        /// Log packet context debug
        /// </summary>
        public static void LogDebug(this IPacketContext context, string message)
        {
            Logger.Instance.Debug($"[Session {context.SessionId}] {message}");
        }
    }
}
