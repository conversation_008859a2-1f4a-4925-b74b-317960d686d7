using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using YulgangServer;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer.HeroBoss
{
    /// <summary>
    /// Integration test cho toàn bộ Cross Server Boss flow
    /// </summary>
    public class CrossServerBossIntegrationTest
    {
        /// <summary>
        /// Test complete boss lifecycle
        /// </summary>
        public static async Task TestCompleteBossLifecycle()
        {
            LogHelper.WriteLine(LogLevel.Info, "=== Starting Cross Server Boss Integration Test ===");

            try
            {
                var bossId = 88888;
                var serverId = World.ServerID;
                var mapId = 1;
                var x = 200f;
                var y = 200f;
                var duration = 5; // 5 minutes for test

                // Step 1: Spawn Cross Server Boss
                LogHelper.WriteLine(LogLevel.Info, "Step 1: Spawning Cross Server Boss...");

                // Zone system has been removed - spawn boss directly
                World.AddNpcNeo(bossId, x, y, mapId, "Cross Server Boss", 0, 0, 0, true, 0, duration * 60 * 1000);
                
                await Task.Delay(2000); // Wait for spawn to complete

                // Step 2: Simulate damage from multiple servers
                LogHelper.WriteLine(LogLevel.Info, "Step 2: Simulating damage from multiple servers...");
                
                var contributions = new List<(int serverId, int sessionId, string playerName, long damage, int attacks)>
                {
                    (1, 1001, "Player1_Server1", 5000, 10),
                    (1, 1002, "Player2_Server1", 3000, 8),
                    (2, 2001, "Player1_Server2", 4000, 9),
                    (2, 2002, "Player2_Server2", 2000, 6),
                    (3, 3001, "Player1_Server3", 3500, 7),
                    (3, 3002, "Player2_Server3", 1500, 5)
                };

                foreach (var (sId, sessionId, playerName, damage, attacks) in contributions)
                {
                    CrossServerBossManager.Instance.UpdateContribution(
                        bossId, sId, sessionId, playerName, damage, attacks);
                    
                    await Task.Delay(100); // Small delay between contributions
                }

                // Step 3: Simulate boss HP updates
                LogHelper.WriteLine(LogLevel.Info, "Step 3: Simulating boss HP updates...");
                
                var totalDamage = 0L;
                foreach (var (_, _, _, damage, _) in contributions)
                {
                    totalDamage += damage;
                }

                var maxHP = 25000;
                var currentHP = maxHP - (int)totalDamage;
                if (currentHP < 0) currentHP = 0;

                CrossServerBossManager.Instance.UpdateBossState(
                    bossId, currentHP, maxHP, 
                    currentHP <= 0 ? CrossServerBossState.Dead : CrossServerBossState.Active);

                await Task.Delay(1000);

                // Step 4: Simulate boss death
                if (currentHP <= 0)
                {
                    LogHelper.WriteLine(LogLevel.Info, "Step 4: Simulating boss death...");
                    
                    var killerName = "Player1_Server1"; // Top damage dealer
                    await CrossServerBossManager.Instance.HandleBossDeath(bossId, killerName);
                    
                    await Task.Delay(3000); // Wait for death processing
                }

                // Step 5: Verify reward distribution
                LogHelper.WriteLine(LogLevel.Info, "Step 5: Verifying reward distribution...");
                
                // Wait for reward distribution to complete
                await Task.Delay(5000);

                // Step 6: Verify cleanup
                LogHelper.WriteLine(LogLevel.Info, "Step 6: Verifying cleanup process...");
                
                // Wait for cleanup to complete
                await Task.Delay(10000);

                // Check if boss is cleaned up
                var cleanupState = CrossServerCleanupCoordinator.Instance.GetCleanupState(bossId);
                if (cleanupState != null)
                {
                    LogHelper.WriteLine(LogLevel.Info, $"Cleanup state: {cleanupState.State}");
                }

                LogHelper.WriteLine(LogLevel.Info, "=== Cross Server Boss Integration Test COMPLETED ===");
                LogHelper.WriteLine(LogLevel.Info, "✅ All steps executed successfully!");

            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Integration test FAILED: {ex.Message}");
                LogHelper.WriteLine(LogLevel.Error, $"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Test message protocol flow
        /// </summary>
        public static async Task TestMessageProtocolFlow()
        {
            LogHelper.WriteLine(LogLevel.Info, "=== Testing Message Protocol Flow ===");

            try
            {
                var bossId = 77777;

                // Test 1: Boss Spawn Message
                LogHelper.WriteLine(LogLevel.Info, "Testing boss spawn message...");
                var spawnInfo = new CrossServerBossSpawnInfo
                {
                    BossId = bossId,
                    OriginServerId = 1,
                    BossName = "Protocol Test Boss",
                    MapId = 1,
                    X = 300f,
                    Y = 300f,
                    MaxHP = 20000,
                    DurationMinutes = 10,
                    BossType = BossType.WorldBoss
                };

                var spawnMessage = CrossServerBossProtocol.CreateBossSpawnMessage(spawnInfo);
                LogHelper.WriteLine(LogLevel.Debug, $"Spawn message: {spawnMessage}");

                var parsedSpawn = CrossServerBossProtocol.ParseBossSpawnMessage(spawnMessage.Split('|'));
                if (parsedSpawn?.BossId == bossId)
                {
                    LogHelper.WriteLine(LogLevel.Info, "✅ Boss spawn message test PASSED");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Error, "❌ Boss spawn message test FAILED");
                }

                // Test 2: HP Update Message
                LogHelper.WriteLine(LogLevel.Info, "Testing HP update message...");
                var hpInfo = new BossHPUpdateInfo
                {
                    BossId = bossId,
                    CurrentHP = 15000,
                    MaxHP = 20000,
                    DamageDealt = 5000,
                    AttackerServerId = 2,
                    AttackerSessionId = 2001,
                    AttackerName = "TestAttacker"
                };

                var hpMessage = CrossServerBossProtocol.CreateBossHPUpdateMessage(hpInfo);
                LogHelper.WriteLine(LogLevel.Debug, $"HP update message: {hpMessage}");

                var parsedHP = CrossServerBossProtocol.ParseBossHPUpdateMessage(hpMessage.Split('|'));
                if (parsedHP?.BossId == bossId && parsedHP.CurrentHP == 15000)
                {
                    LogHelper.WriteLine(LogLevel.Info, "✅ HP update message test PASSED");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Error, "❌ HP update message test FAILED");
                }

                // Test 3: Damage Contribute Message
                LogHelper.WriteLine(LogLevel.Info, "Testing damage contribute message...");
                var damageMessage = CrossServerBossProtocol.CreateDamageContributeMessage(
                    bossId, 2, 2001, "TestPlayer", 5000, 10);
                LogHelper.WriteLine(LogLevel.Debug, $"Damage message: {damageMessage}");

                var (pBossId, pServerId, pSessionId, pPlayerName, pDamage, pAttackCount) = 
                    CrossServerBossProtocol.ParseDamageContributeMessage(damageMessage.Split('|'));

                if (pBossId == bossId && pDamage == 5000 && pPlayerName == "TestPlayer")
                {
                    LogHelper.WriteLine(LogLevel.Info, "✅ Damage contribute message test PASSED");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Error, "❌ Damage contribute message test FAILED");
                }

                // Test 4: Boss Death Message
                LogHelper.WriteLine(LogLevel.Info, "Testing boss death message...");
                var deathInfo = new BossDeathInfo
                {
                    BossId = bossId,
                    KillerName = "TestKiller",
                    KillerServerId = 2,
                    KillerSessionId = 2001,
                    TotalDamageDealt = 20000,
                    TotalParticipants = 6
                };

                var deathMessage = CrossServerBossProtocol.CreateBossDeathMessage(deathInfo);
                LogHelper.WriteLine(LogLevel.Debug, $"Death message: {deathMessage}");

                var parsedDeath = CrossServerBossProtocol.ParseBossDeathMessage(deathMessage.Split('|'));
                if (parsedDeath?.BossId == bossId && parsedDeath.KillerName == "TestKiller")
                {
                    LogHelper.WriteLine(LogLevel.Info, "✅ Boss death message test PASSED");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Error, "❌ Boss death message test FAILED");
                }

                // Test 5: Reward Distribution Message
                LogHelper.WriteLine(LogLevel.Info, "Testing reward distribution message...");
                var rewards = new List<CrossServerRewardInfo>
                {
                    new CrossServerRewardInfo
                    {
                        BossId = bossId,
                        ServerId = 2,
                        SessionId = 2001,
                        PlayerName = "TestPlayer",
                        TotalDamage = 5000,
                        RewardPoints = 150,
                        HasSpecialReward = true,
                        SpecialRewardItem = "EpicBossReward"
                    }
                };

                var rewardMessage = CrossServerBossProtocol.CreateRewardDistributionMessage(bossId, 2, rewards);
                LogHelper.WriteLine(LogLevel.Debug, $"Reward message: {rewardMessage}");

                var (rBossId, rServerId, rRewards) = 
                    CrossServerBossProtocol.ParseRewardDistributionMessage(rewardMessage.Split('|'));

                if (rBossId == bossId && rRewards.Count == 1 && rRewards[0].RewardPoints == 150)
                {
                    LogHelper.WriteLine(LogLevel.Info, "✅ Reward distribution message test PASSED");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Error, "❌ Reward distribution message test FAILED");
                }

                LogHelper.WriteLine(LogLevel.Info, "=== Message Protocol Flow Test COMPLETED ===");

            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Message protocol test FAILED: {ex.Message}");
            }
        }

        /// <summary>
        /// Test performance với nhiều boss cùng lúc
        /// </summary>
        public static async Task TestPerformanceWithMultipleBosses()
        {
            LogHelper.WriteLine(LogLevel.Info, "=== Testing Performance with Multiple Bosses ===");

            try
            {
                var startTime = DateTime.Now;
                var bossCount = 10;
                var tasks = new List<Task>();

                // Spawn multiple bosses simultaneously
                for (int i = 0; i < bossCount; i++)
                {
                    var bossId = 60000 + i;
                    var task = Task.Run(async () =>
                    {
                        // Register boss
                        CrossServerBossManager.Instance.RegisterCrossServerBoss(
                            bossId, World.ServerID, $"Perf Test Boss {i}", 1, 100 + i * 10, 100 + i * 10, 2);

                        // Simulate some damage
                        for (int j = 0; j < 5; j++)
                        {
                            CrossServerBossManager.Instance.UpdateContribution(
                                bossId, World.ServerID, 1000 + j, $"Player{j}", 1000, 2);
                            await Task.Delay(50);
                        }

                        // Simulate boss death
                        await CrossServerBossManager.Instance.HandleBossDeath(bossId, $"Player0");
                    });

                    tasks.Add(task);
                }

                // Wait for all tasks to complete
                await Task.WhenAll(tasks);

                var endTime = DateTime.Now;
                var duration = endTime - startTime;

                LogHelper.WriteLine(LogLevel.Info, $"Performance test completed in {duration.TotalSeconds:F2} seconds");
                LogHelper.WriteLine(LogLevel.Info, $"Processed {bossCount} bosses simultaneously");
                LogHelper.WriteLine(LogLevel.Info, "✅ Performance test PASSED");

            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Performance test FAILED: {ex.Message}");
            }
        }

        /// <summary>
        /// Chạy tất cả integration tests
        /// </summary>
        public static async Task RunAllIntegrationTests()
        {
            LogHelper.WriteLine(LogLevel.Info, "🚀 Starting All Cross Server Boss Integration Tests...");

            await TestMessageProtocolFlow();
            await Task.Delay(2000);

            await TestPerformanceWithMultipleBosses();
            await Task.Delay(2000);

            await TestCompleteBossLifecycle();

            LogHelper.WriteLine(LogLevel.Info, "🎉 All Integration Tests Completed!");
        }
    }
}
