<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:controls="using:HeroYulgang.Views.Controls"
             mc:Ignorable="d" d:DesignWidth="900" d:DesignHeight="600"
             x:Class="HeroYulgang.Views.MainView"
             x:CompileBindings="False">

    <Grid RowDefinitions="Auto,*">
        <!-- Header with server status and controls -->
        <Grid Grid.Row="0" ColumnDefinitions="Auto,*,Auto" Margin="10">
            <controls:ServerStatusIndicator Grid.Column="0" Width="400" Margin="0,0,10,0"/>

            <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="10">
                <Button x:Name="StartServerButton" Content="Start" Width="100" Height="35" Classes="success"/>
                <Button x:Name="StopServerButton" Content="Stop" Width="100" Height="35" Classes="danger"/>
                <Button x:Name="RestartServerButton" Content="Restart" Width="100" Height="35" Classes="warning"/>
                <Button x:Name="DbStressTestButton" Content="DB Stress Test" Width="120" Height="35" Classes="secondary"/>
                <Button x:Name="TestErrorLogButton" Content="Test Error Log" Width="120" Height="35" Classes="accent"/>
            </StackPanel>
        </Grid>

        <!-- Log viewer -->
        <controls:LogViewer Grid.Row="1" Margin="10"/>
    </Grid>
</UserControl>
