using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Network
{
    /// <summary>
    /// Client session implementation - thay thế ClientActor
    /// </summary>
    public class ClientSession : IClientSession, IDisposable
    {
        private readonly TcpClient _tcpClient;
        private readonly NetworkStream _networkStream;
        private readonly NetworkConfiguration _configuration;
        private readonly ConcurrentDictionary<string, object> _properties;
        private readonly Channel<byte[]> _sendChannel;
        private readonly ChannelWriter<byte[]> _sendWriter;
        private readonly ChannelReader<byte[]> _sendReader;
        private volatile SessionState _state = SessionState.Connecting;
        private volatile DateTime _lastActivity = DateTime.UtcNow;
        private volatile bool _isDisposed;
        private Task? _sendTask;
        private Task? _receiveTask;

        public ClientSession(int sessionId, TcpClient tcpClient, NetworkConfiguration configuration)
        {
            SessionId = sessionId;
            _tcpClient = tcpClient;
            _networkStream = tcpClient.GetStream();
            _configuration = configuration;
            _properties = new ConcurrentDictionary<string, object>();
            CreatedAt = DateTime.UtcNow;
            RemoteEndPoint = (IPEndPoint)tcpClient.Client.RemoteEndPoint!;

            // Configure socket
            ConfigureSocket();

            // Create send channel
            var channelOptions = new UnboundedChannelOptions
            {
                SingleReader = true,
                SingleWriter = false
            };
            _sendChannel = Channel.CreateUnbounded<byte[]>(channelOptions);
            _sendWriter = _sendChannel.Writer;
            _sendReader = _sendChannel.Reader;

            // Start send task
            _sendTask = Task.Run(ProcessSendQueueAsync);

            // Mark as connected
            _state = SessionState.Connected;
        }

        public int SessionId { get; }
        public IPEndPoint RemoteEndPoint { get; }
        public DateTime CreatedAt { get; }
        public DateTime LastActivity => _lastActivity;
        public SessionState State => _state;
        public IDictionary<string, object> Properties => _properties;
        public bool IsActive => !_isDisposed && _tcpClient.Connected && _state != SessionState.Disconnected;

        /// <summary>
        /// Configure socket options
        /// </summary>
        private void ConfigureSocket()
        {
            try
            {
                var socket = _tcpClient.Client;
                socket.NoDelay = _configuration.NoDelay;
                socket.ReceiveBufferSize = _configuration.ReceiveBufferSize;
                socket.SendBufferSize = _configuration.SendBufferSize;
                
                if (_configuration.LingerEnabled)
                {
                    socket.LingerState = new LingerOption(true, _configuration.LingerTimeout);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"Failed to configure socket for session {SessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Send data to client
        /// </summary>
        public async Task SendAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (_isDisposed || !IsActive)
                throw new InvalidOperationException("Session is not active");

            try
            {
                await _sendWriter.WriteAsync(data, cancellationToken);
                _lastActivity = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error queuing data for session {SessionId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Process send queue
        /// </summary>
        private async Task ProcessSendQueueAsync()
        {
            try
            {
                await foreach (var data in _sendReader.ReadAllAsync())
                {
                    if (_isDisposed || !IsActive)
                        break;

                    try
                    {
                        await _networkStream.WriteAsync(data, 0, data.Length);
                        await _networkStream.FlushAsync();
                        _lastActivity = DateTime.UtcNow;
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Error sending data to session {SessionId}: {ex.Message}");
                        await CloseAsync();
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error in send queue for session {SessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Get incoming data stream
        /// </summary>
        public async IAsyncEnumerable<byte[]> GetDataAsync([EnumeratorCancellation] CancellationToken cancellationToken = default)
        {
            var buffer = new byte[_configuration.ReceiveBufferSize];

            try
            {
                while (IsActive && !cancellationToken.IsCancellationRequested)
                {
                    var bytesRead = await _networkStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    
                    if (bytesRead == 0)
                    {
                        // Client disconnected
                        Logger.Instance.Debug($"Client {SessionId} disconnected (0 bytes read)");
                        break;
                    }

                    _lastActivity = DateTime.UtcNow;

                    // Copy data to new array
                    var data = new byte[bytesRead];
                    Array.Copy(buffer, 0, data, 0, bytesRead);
                    
                    yield return data;
                }
            }
            catch (IOException ex) when (ex.InnerException is SocketException)
            {
                Logger.Instance.Debug($"Socket error for session {SessionId}: {ex.Message}");
            }
            catch (ObjectDisposedException)
            {
                // Expected when closing
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error reading data from session {SessionId}: {ex.Message}");
            }
            finally
            {
                await CloseAsync();
            }
        }

        /// <summary>
        /// Close session
        /// </summary>
        public async Task CloseAsync()
        {
            if (_isDisposed)
                return;

            try
            {
                _state = SessionState.Disconnecting;
                
                // Complete send channel
                _sendWriter.Complete();
                
                // Wait for send task to complete
                if (_sendTask != null)
                {
                    await _sendTask.WaitAsync(TimeSpan.FromSeconds(5));
                }

                // Close network stream and client
                _networkStream?.Close();
                _tcpClient?.Close();

                _state = SessionState.Disconnected;
                Logger.Instance.Debug($"Session {SessionId} closed");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error closing session {SessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Set session state
        /// </summary>
        public void SetState(SessionState newState)
        {
            if (_state != newState)
            {
                var oldState = _state;
                _state = newState;
                Logger.Instance.Debug($"Session {SessionId} state changed: {oldState} -> {newState}");
            }
        }

        /// <summary>
        /// Get session property
        /// </summary>
        public T? GetProperty<T>(string key)
        {
            return _properties.TryGetValue(key, out var value) && value is T typedValue ? typedValue : default;
        }

        /// <summary>
        /// Set session property
        /// </summary>
        public void SetProperty<T>(string key, T value)
        {
            if (value != null)
            {
                _properties[key] = value;
            }
            else
            {
                _properties.TryRemove(key, out _);
            }
        }

        public void Dispose()
        {
            if (_isDisposed)
                return;

            _isDisposed = true;

            try
            {
                CloseAsync().Wait(TimeSpan.FromSeconds(5));
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing session {SessionId}: {ex.Message}");
            }
            finally
            {
                _sendTask?.Dispose();
                _receiveTask?.Dispose();
                _networkStream?.Dispose();
                _tcpClient?.Dispose();
            }
        }
    }

    /// <summary>
    /// Session extensions
    /// </summary>
    public static class ClientSessionExtensions
    {
        /// <summary>
        /// Send string data
        /// </summary>
        public static async Task SendStringAsync(this IClientSession session, string data, CancellationToken cancellationToken = default)
        {
            var bytes = System.Text.Encoding.UTF8.GetBytes(data);
            await session.SendAsync(bytes, cancellationToken);
        }

        /// <summary>
        /// Check if session is authenticated
        /// </summary>
        public static bool IsAuthenticated(this IClientSession session)
        {
            return session.State == SessionState.Authenticated;
        }

        /// <summary>
        /// Get session uptime
        /// </summary>
        public static TimeSpan GetUptime(this IClientSession session)
        {
            return DateTime.UtcNow - session.CreatedAt;
        }

        /// <summary>
        /// Get idle time
        /// </summary>
        public static TimeSpan GetIdleTime(this IClientSession session)
        {
            return DateTime.UtcNow - session.LastActivity;
        }
    }
}
