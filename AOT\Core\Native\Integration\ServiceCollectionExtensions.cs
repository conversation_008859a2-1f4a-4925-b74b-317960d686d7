using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using HeroYulgang.Core.Native.Services;
using HeroYulgang.Core.Native.Messaging;
using HeroYulgang.Core.Native.Network;
using HeroYulgang.Core.Native.Packets;
using HeroYulgang.Core.Native.Sessions;

namespace HeroYulgang.Core.Native.Integration
{
    /// <summary>
    /// Service collection extensions for native system registration
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Add native system services - thay thế Akka.NET registration
        /// </summary>
        public static IServiceCollection AddNativeSystem(this IServiceCollection services, Action<NativeSystemConfiguration>? configure = null)
        {
            var configuration = new NativeSystemConfiguration();
            configure?.Invoke(configuration);

            // Core messaging system
            services.AddSingleton<IMessageSystem, ChannelMessageSystem>();
            services.AddSingleton<IEventBus, EventBus>();
            services.AddSingleton<IRequestResponseService, RequestResponseService>();

            // Service management
            services.AddSingleton<IServiceManager, ServiceManager>();

            // Network system
            services.AddSingleton(configuration.NetworkConfiguration);
            services.AddSingleton<INetworkManager, TcpNetworkManager>();
            services.AddSingleton<IClientSessionManager, ClientSessionManager>();

            // Packet processing
            services.AddSingleton(configuration.PacketProcessorConfiguration);
            services.AddSingleton<IPriorityPacketQueue, PriorityPacketQueue>();
            services.AddSingleton<IPacketProcessor, PacketProcessor>();

            // Session management
            services.AddSingleton(configuration.SessionConfiguration);
            services.AddSingleton<IPlayerSessionFactory, PlayerSessionFactory>();
            services.AddSingleton<ISessionManager, SessionManager>();
            services.AddSingleton<IAuthenticationService, AuthenticationService>();

            // Bootstrap service
            services.AddSingleton<NativeSystemBootstrap>();
            services.AddHostedService<NativeSystemBootstrap>();

            return services;
        }

        /// <summary>
        /// Add network configuration
        /// </summary>
        public static IServiceCollection AddNetworkConfiguration(this IServiceCollection services, Action<NetworkConfiguration> configure)
        {
            var config = new NetworkConfiguration();
            configure(config);
            services.AddSingleton(config);
            return services;
        }

        /// <summary>
        /// Add packet processor configuration
        /// </summary>
        public static IServiceCollection AddPacketProcessorConfiguration(this IServiceCollection services, Action<PacketProcessorConfiguration> configure)
        {
            var config = new PacketProcessorConfiguration();
            configure(config);
            services.AddSingleton(config);
            return services;
        }

        /// <summary>
        /// Add session configuration
        /// </summary>
        public static IServiceCollection AddSessionConfiguration(this IServiceCollection services, Action<SessionConfiguration> configure)
        {
            var config = new SessionConfiguration();
            configure(config);
            services.AddSingleton(config);
            return services;
        }

        /// <summary>
        /// Add custom packet handler
        /// </summary>
        public static IServiceCollection AddPacketHandler<TPacket, THandler>(this IServiceCollection services)
            where TPacket : class, IPacket
            where THandler : class, IPacketHandler<TPacket>
        {
            services.AddSingleton<IPacketHandler<TPacket>, THandler>();
            return services;
        }

        /// <summary>
        /// Add custom service
        /// </summary>
        public static IServiceCollection AddNativeService<TInterface, TImplementation>(this IServiceCollection services)
            where TInterface : class, IService
            where TImplementation : class, TInterface
        {
            services.AddSingleton<TInterface, TImplementation>();
            return services;
        }
    }

    /// <summary>
    /// Native system configuration
    /// </summary>
    public class NativeSystemConfiguration
    {
        /// <summary>
        /// Network configuration
        /// </summary>
        public NetworkConfiguration NetworkConfiguration { get; set; } = new();

        /// <summary>
        /// Packet processor configuration
        /// </summary>
        public PacketProcessorConfiguration PacketProcessorConfiguration { get; set; } = new();

        /// <summary>
        /// Session configuration
        /// </summary>
        public SessionConfiguration SessionConfiguration { get; set; } = new();

        /// <summary>
        /// Enable development mode
        /// </summary>
        public bool DevelopmentMode { get; set; } = false;

        /// <summary>
        /// Enable detailed logging
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = true;

        /// <summary>
        /// Enable performance monitoring
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;
    }

    /// <summary>
    /// Builder pattern for native system configuration
    /// </summary>
    public class NativeSystemBuilder
    {
        private readonly IServiceCollection _services;
        private readonly NativeSystemConfiguration _configuration;

        public NativeSystemBuilder(IServiceCollection services)
        {
            _services = services;
            _configuration = new NativeSystemConfiguration();
        }

        /// <summary>
        /// Configure network settings
        /// </summary>
        public NativeSystemBuilder ConfigureNetwork(Action<NetworkConfiguration> configure)
        {
            configure(_configuration.NetworkConfiguration);
            return this;
        }

        /// <summary>
        /// Configure packet processing
        /// </summary>
        public NativeSystemBuilder ConfigurePacketProcessing(Action<PacketProcessorConfiguration> configure)
        {
            configure(_configuration.PacketProcessorConfiguration);
            return this;
        }

        /// <summary>
        /// Configure session management
        /// </summary>
        public NativeSystemBuilder ConfigureSessions(Action<SessionConfiguration> configure)
        {
            configure(_configuration.SessionConfiguration);
            return this;
        }

        /// <summary>
        /// Enable development mode
        /// </summary>
        public NativeSystemBuilder EnableDevelopmentMode()
        {
            _configuration.DevelopmentMode = true;
            _configuration.EnableDetailedLogging = true;
            return this;
        }

        /// <summary>
        /// Add packet handler
        /// </summary>
        public NativeSystemBuilder AddPacketHandler<TPacket, THandler>()
            where TPacket : class, IPacket
            where THandler : class, IPacketHandler<TPacket>
        {
            _services.AddPacketHandler<TPacket, THandler>();
            return this;
        }

        /// <summary>
        /// Add custom service
        /// </summary>
        public NativeSystemBuilder AddService<TInterface, TImplementation>()
            where TInterface : class, IService
            where TImplementation : class, TInterface
        {
            _services.AddNativeService<TInterface, TImplementation>();
            return this;
        }

        /// <summary>
        /// Build and register the native system
        /// </summary>
        public IServiceCollection Build()
        {
            _services.AddNativeSystem(config =>
            {
                config.NetworkConfiguration = _configuration.NetworkConfiguration;
                config.PacketProcessorConfiguration = _configuration.PacketProcessorConfiguration;
                config.SessionConfiguration = _configuration.SessionConfiguration;
                config.DevelopmentMode = _configuration.DevelopmentMode;
                config.EnableDetailedLogging = _configuration.EnableDetailedLogging;
                config.EnablePerformanceMonitoring = _configuration.EnablePerformanceMonitoring;
            });

            return _services;
        }
    }

    /// <summary>
    /// Extension method to create builder
    /// </summary>
    public static class NativeSystemBuilderExtensions
    {
        /// <summary>
        /// Create native system builder
        /// </summary>
        public static NativeSystemBuilder AddNativeSystemBuilder(this IServiceCollection services)
        {
            return new NativeSystemBuilder(services);
        }
    }
}
