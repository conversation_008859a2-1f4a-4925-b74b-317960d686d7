﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_upgrade_item {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public int? itemid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string itemname { get; set; }

		[JsonProperty]
		public int? itemlevel { get; set; }

		[JsonProperty]
		public int? itemtype { get; set; }

		[JsonProperty]
		public int? upgrade_pp { get; set; }

		[JsonProperty]
		public int? nguyenlieu_id { get; set; }

		[JsonProperty]
		public int? giamcuonghoa { get; set; }

		[JsonProperty]
		public int? yeucaucuonghoa { get; set; }

	}

}
