using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HeroYulgang.Database.FreeSql.Common
{
    /// <summary>
    /// Base class for database operations that can be queued
    /// </summary>
    public abstract class DatabaseOperation
    {
        public Guid OperationId { get; } = Guid.NewGuid();
        public DateTime CreatedAt { get; } = DateTime.UtcNow;
        public string OperationType { get; protected set; } = string.Empty;
        public int Priority { get; set; } = 0; // Higher number = higher priority
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;
        public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Execute the database operation
        /// </summary>
        public abstract Task<ServiceResult> ExecuteAsync();

        /// <summary>
        /// Check if operation can be retried
        /// </summary>
        public virtual bool CanRetry()
        {
            return RetryCount < MaxRetries;
        }

        /// <summary>
        /// Increment retry count
        /// </summary>
        public void IncrementRetry()
        {
            RetryCount++;
        }

        /// <summary>
        /// Check if operation has expired
        /// </summary>
        public bool IsExpired()
        {
            return DateTime.UtcNow - CreatedAt > Timeout;
        }
    }

    /// <summary>
    /// Generic database operation with result
    /// </summary>
    public abstract class DatabaseOperation<T> : DatabaseOperation
    {
        /// <summary>
        /// Execute the database operation and return typed result
        /// </summary>
        public abstract Task<ServiceResult<T>> ExecuteTypedAsync();

        /// <summary>
        /// Execute the database operation (base implementation)
        /// </summary>
        public override async Task<ServiceResult> ExecuteAsync()
        {
            var result = await ExecuteTypedAsync();
            return result.IsSuccess ? ServiceResult.Success() : ServiceResult.Failure(result.ErrorMessage!, result.Exception!);
        }
    }

    /// <summary>
    /// Batch write operation for multiple database operations
    /// </summary>
    public class BatchWriteOperation : DatabaseOperation
    {
        private readonly List<DatabaseOperation> _operations;

        public BatchWriteOperation(IEnumerable<DatabaseOperation> operations)
        {
            _operations = operations.ToList();
            OperationType = "BatchWrite";
            Priority = _operations.Max(op => op.Priority);
        }

        public IReadOnlyList<DatabaseOperation> Operations => _operations.AsReadOnly();

        public override async Task<ServiceResult> ExecuteAsync()
        {
            var results = new List<ServiceResult>();
            var failedOperations = new List<(DatabaseOperation Operation, ServiceResult Result)>();

            foreach (var operation in _operations)
            {
                try
                {
                    var result = await operation.ExecuteAsync();
                    results.Add(result);

                    if (!result.IsSuccess)
                    {
                        failedOperations.Add((operation, result));
                    }
                }
                catch (Exception ex)
                {
                    var errorResult = ServiceResult.Failure(ex);
                    results.Add(errorResult);
                    failedOperations.Add((operation, errorResult));
                }
            }

            if (failedOperations.Any())
            {
                var errorMessage = $"Batch operation failed. {failedOperations.Count}/{_operations.Count} operations failed.";
                return ServiceResult.Failure(errorMessage);
            }

            return ServiceResult.Success();
        }
    }

    /// <summary>
    /// Operation priority levels
    /// </summary>
    public static class OperationPriority
    {
        public const int Low = 0;
        public const int Normal = 10;
        public const int High = 20;
        public const int Critical = 30;
        public const int Emergency = 40;
    }

    /// <summary>
    /// Operation status for tracking
    /// </summary>
    public enum OperationStatus
    {
        Pending,
        Processing,
        Completed,
        Failed,
        Cancelled,
        Expired
    }

    /// <summary>
    /// Operation result with tracking information
    /// </summary>
    public class OperationResult
    {
        public Guid OperationId { get; set; }
        public OperationStatus Status { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public TimeSpan? Duration => CompletedAt?.Subtract(StartedAt);
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public int RetryCount { get; set; }

        public static OperationResult Success(Guid operationId, DateTime startedAt)
        {
            return new OperationResult
            {
                OperationId = operationId,
                Status = OperationStatus.Completed,
                StartedAt = startedAt,
                CompletedAt = DateTime.UtcNow
            };
        }

        public static OperationResult Failure(Guid operationId, DateTime startedAt, string errorMessage, Exception? exception = null)
        {
            return new OperationResult
            {
                OperationId = operationId,
                Status = OperationStatus.Failed,
                StartedAt = startedAt,
                CompletedAt = DateTime.UtcNow,
                ErrorMessage = errorMessage,
                Exception = exception
            };
        }
    }
}
