using System;
using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Services;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Sessions
{
    /// <summary>
    /// Authentication service implementation
    /// </summary>
    public class AuthenticationService : ServiceBase, IAuthenticationService
    {
        private readonly ConcurrentDictionary<int, SessionToken> _sessionTokens;
        private readonly ConcurrentDictionary<int, DateTime> _accountLogins;
        private readonly Timer _tokenCleanupTimer;
        private readonly TimeSpan _tokenLifetime;

        public AuthenticationService(TimeSpan? tokenLifetime = null) : base("AuthenticationService")
        {
            _sessionTokens = new ConcurrentDictionary<int, SessionToken>();
            _accountLogins = new ConcurrentDictionary<int, DateTime>();
            _tokenLifetime = tokenLifetime ?? TimeSpan.FromHours(24);
            
            // Cleanup expired tokens every hour
            _tokenCleanupTimer = new Timer(CleanupExpiredTokens, null, TimeSpan.FromHours(1), TimeSpan.FromHours(1));
            
            Logger.Instance.Info($"AuthenticationService created with token lifetime: {_tokenLifetime}");
        }

        /// <summary>
        /// Authenticate user credentials
        /// </summary>
        public async Task<AuthenticationResult> AuthenticateAsync(string username, string password, int sessionId)
        {
            try
            {
                Logger.Instance.Debug($"Authenticating user {username} for session {sessionId}");

                // TODO: Implement actual database authentication
                // For now, this is a placeholder implementation
                var authResult = await AuthenticateWithDatabaseAsync(username, password);
                
                if (authResult.Success)
                {
                    // Create session token
                    var token = await CreateSessionTokenAsync(authResult.AccountId, sessionId);
                    authResult.SessionToken = token;
                    
                    // Track login
                    _accountLogins[authResult.AccountId] = DateTime.UtcNow;
                    
                    Logger.Instance.Info($"Successfully authenticated user {username} (Account: {authResult.AccountId}) for session {sessionId}");
                }
                else
                {
                    Logger.Instance.Warning($"Authentication failed for user {username} on session {sessionId}: {authResult.ErrorMessage}");
                }

                return authResult;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error authenticating user {username}: {ex.Message}");
                return new AuthenticationResult
                {
                    Success = false,
                    ErrorMessage = "Authentication service error"
                };
            }
        }

        /// <summary>
        /// Validate session token
        /// </summary>
        public async Task<bool> ValidateSessionAsync(int sessionId, string token)
        {
            try
            {
                if (!_sessionTokens.TryGetValue(sessionId, out var sessionToken))
                {
                    Logger.Instance.Debug($"No token found for session {sessionId}");
                    return false;
                }

                if (sessionToken.Token != token)
                {
                    Logger.Instance.Warning($"Invalid token for session {sessionId}");
                    return false;
                }

                if (sessionToken.ExpiresAt < DateTime.UtcNow)
                {
                    Logger.Instance.Debug($"Expired token for session {sessionId}");
                    _sessionTokens.TryRemove(sessionId, out _);
                    return false;
                }

                // Update last access
                sessionToken.LastAccess = DateTime.UtcNow;
                
                Logger.Instance.Debug($"Valid token for session {sessionId}");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error validating session {sessionId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Create session token
        /// </summary>
        public async Task<string> CreateSessionTokenAsync(int accountId, int sessionId)
        {
            try
            {
                var token = GenerateSecureToken();
                var sessionToken = new SessionToken
                {
                    Token = token,
                    AccountId = accountId,
                    SessionId = sessionId,
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.Add(_tokenLifetime),
                    LastAccess = DateTime.UtcNow
                };

                _sessionTokens[sessionId] = sessionToken;
                
                Logger.Instance.Debug($"Created session token for account {accountId}, session {sessionId}");
                return await Task.FromResult(token);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error creating session token: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Revoke session
        /// </summary>
        public async Task RevokeSessionAsync(int sessionId)
        {
            try
            {
                if (_sessionTokens.TryRemove(sessionId, out var sessionToken))
                {
                    Logger.Instance.Info($"Revoked session {sessionId} for account {sessionToken.AccountId}");
                }
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error revoking session {sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if account is already logged in
        /// </summary>
        public async Task<bool> IsAccountLoggedInAsync(int accountId)
        {
            try
            {
                // Check if account has recent login
                if (_accountLogins.TryGetValue(accountId, out var loginTime))
                {
                    var isRecent = DateTime.UtcNow - loginTime < _tokenLifetime;
                    return await Task.FromResult(isRecent);
                }

                // Check if account has active session token
                foreach (var kvp in _sessionTokens)
                {
                    var sessionToken = kvp.Value;
                    if (sessionToken.AccountId == accountId && sessionToken.ExpiresAt > DateTime.UtcNow)
                    {
                        return await Task.FromResult(true);
                    }
                }

                return await Task.FromResult(false);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error checking if account {accountId} is logged in: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Authenticate with database (placeholder implementation)
        /// </summary>
        private async Task<AuthenticationResult> AuthenticateWithDatabaseAsync(string username, string password)
        {
            // TODO: Replace with actual database authentication
            // This is a placeholder implementation for testing
            
            await Task.Delay(100); // Simulate database call
            
            // Simple test authentication
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                return new AuthenticationResult
                {
                    Success = false,
                    ErrorMessage = "Username and password are required"
                };
            }

            if (username.Length < 3)
            {
                return new AuthenticationResult
                {
                    Success = false,
                    ErrorMessage = "Username too short"
                };
            }

            if (password.Length < 4)
            {
                return new AuthenticationResult
                {
                    Success = false,
                    ErrorMessage = "Password too short"
                };
            }

            // Generate fake account ID based on username hash
            var accountId = Math.Abs(username.GetHashCode()) % 100000 + 1;
            
            return new AuthenticationResult
            {
                Success = true,
                AccountId = accountId,
                CharacterName = $"Player_{username}",
                PlayerData = new { Username = username, Level = 1, Experience = 0 }
            };
        }

        /// <summary>
        /// Generate secure token
        /// </summary>
        private string GenerateSecureToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[32];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// Cleanup expired tokens
        /// </summary>
        private void CleanupExpiredTokens(object? state)
        {
            try
            {
                var expiredSessions = new List<int>();
                var now = DateTime.UtcNow;

                foreach (var kvp in _sessionTokens)
                {
                    if (kvp.Value.ExpiresAt < now)
                    {
                        expiredSessions.Add(kvp.Key);
                    }
                }

                foreach (var sessionId in expiredSessions)
                {
                    _sessionTokens.TryRemove(sessionId, out _);
                }

                // Cleanup old account logins
                var expiredAccounts = new List<int>();
                foreach (var kvp in _accountLogins)
                {
                    if (now - kvp.Value > _tokenLifetime)
                    {
                        expiredAccounts.Add(kvp.Key);
                    }
                }

                foreach (var accountId in expiredAccounts)
                {
                    _accountLogins.TryRemove(accountId, out _);
                }

                if (expiredSessions.Count > 0 || expiredAccounts.Count > 0)
                {
                    Logger.Instance.Debug($"Cleaned up {expiredSessions.Count} expired tokens and {expiredAccounts.Count} old logins");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error during token cleanup: {ex.Message}");
            }
        }

        /// <summary>
        /// Get authentication statistics
        /// </summary>
        public AuthenticationStatistics GetStatistics()
        {
            return new AuthenticationStatistics
            {
                ActiveTokens = _sessionTokens.Count,
                ActiveLogins = _accountLogins.Count,
                TokenLifetime = _tokenLifetime
            };
        }

        protected override async Task OnStoppingAsync(CancellationToken cancellationToken)
        {
            Logger.Instance.Info("Stopping authentication service...");
            
            _tokenCleanupTimer?.Dispose();
            _sessionTokens.Clear();
            _accountLogins.Clear();
            
            await base.OnStoppingAsync(cancellationToken);
        }

        public override void Dispose()
        {
            _tokenCleanupTimer?.Dispose();
            base.Dispose();
        }
    }

    /// <summary>
    /// Session token data
    /// </summary>
    internal class SessionToken
    {
        public string Token { get; set; } = string.Empty;
        public int AccountId { get; set; }
        public int SessionId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public DateTime LastAccess { get; set; }
    }

    /// <summary>
    /// Authentication statistics
    /// </summary>
    public class AuthenticationStatistics
    {
        public int ActiveTokens { get; set; }
        public int ActiveLogins { get; set; }
        public TimeSpan TokenLifetime { get; set; }
    }
}
