using System;

namespace RxjhServer;

public class MoveVector
{
	private float float_0;

	private float float_1;

	private float float_2;

	private float float_3;

	private float float_4;

	private float float_5;

	private float float_6;

	private float float_7;

	private DateTime dateTime_0;

	public MoveVector(Players players_0, float float_8, float float_9, float float_10)
	{
		dateTime_0 = DateTime.Now;
		float_0 = float_8;
		float_1 = float_9;
		float_2 = float_10;
		float_3 = players_0.PosX;
		float_4 = players_0.PosY;
		float_5 = players_0.PosZ;
		float_7 = players_0.Speed;
		var num = float_8 - players_0.PosX;
		var num2 = float_9 - players_0.PosY;
		float_6 = (float)Math.Sqrt(num * (double)num + num2 * (double)num2) / (players_0.Speed / 1000f);
	}

	public MoveVector(Players players_0, float float_8, float float_9, float float_10, float float_11, float float_12, float float_13)
	{
		dateTime_0 = DateTime.Now;
		float_0 = float_8;
		float_1 = float_9;
		float_2 = float_10;
		float_3 = float_11;
		float_4 = float_12;
		float_5 = float_13;
		float_7 = players_0.Speed;
		var num = float_8 - players_0.PosX;
		var num2 = float_9 - players_0.PosY;
		float_6 = (float)Math.Sqrt(num * (double)num + num2 * (double)num2) / (players_0.Speed / 1000f);
	}

	public void Get(out float float_8, out float float_9, out float float_10)
	{
		var num = (float)DateTime.Now.Subtract(dateTime_0).TotalMilliseconds / float_6;
		if (float_6 != 15.0 && num <= 31.0)
		{
			var num2 = float_0 - float_3;
			var num3 = float_1 - float_4;
			var num4 = float_2 - (double)float_5;
			var num5 = num2 * num;
			var num6 = num3 * num;
			double num7 = num;
			var num8 = (float)(num4 * num7);
			float_8 = float_3 + num5;
			float_9 = float_4 + num6;
			float_10 = float_5 + num8;
		}
		else
		{
			float_8 = float_0;
			float_9 = float_1;
			float_10 = float_2;
		}
	}

	public bool NearDestination()
	{
		return DateTime.Now.Subtract(dateTime_0).TotalMilliseconds - float_6 < 1000.0;
	}

	public bool ReachDestination()
	{
		if (!(DateTime.Now.Subtract(dateTime_0).TotalMilliseconds / float_6 > 1.0))
		{
			return float_6 == 0.0;
		}
		return true;
	}

	public void Update(float float_8, float float_9, float float_10, float float_11)
	{
		Get(out float_3, out float_4, out float_5);
		dateTime_0 = DateTime.Now;
		var num = float_8 - float_3;
		var num2 = float_9 - float_4;
		float_0 = float_8;
		float_1 = float_9;
		float_2 = float_10;
		float_6 = (float)Math.Sqrt(num * (double)num + num2 * (double)num2) / (float_11 / 1000f);
	}
}
