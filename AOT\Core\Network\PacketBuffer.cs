using HeroYulgang.Services;
using System;
using System.Collections.Generic;

namespace HeroYulgang.Core.Network
{
    /// <summary>
    /// Buffer để xử lý packet gộp từ TCP stream
    /// X<PERSON> lý trường hợp nhiều packet được gộp lại trong một TCP message
    /// </summary>
    public class PacketBuffer
    {
        private readonly List<byte> _buffer = [];
        private const byte PACKET_HEADER_1 = 0xAA;
        private const byte PACKET_HEADER_2 = 0x55;
        private const byte PACKET_FOOTER_1 = 0x55;
        private const byte PACKET_FOOTER_2 = 0xAA;
        private const int MIN_PACKET_SIZE = 6; // Header(2) + Length(2) + Footer(2)

        /// <summary>
        /// Thêm dữ liệu mới vào buffer
        /// </summary>
        /// <param name="data">Dữ liệu nhận đư<PERSON><PERSON> từ TCP</param>
        public void AddData(byte[] data)
        {
            _buffer.AddRange(data);
        }

        /// <summary>
        /// Trích xuất tất cả các packet hoàn chỉnh từ buffer
        /// </summary>
        /// <returns>Danh sách các packet hoàn chỉnh</returns>
        public List<byte[]> ExtractCompletePackets()
        {
            var packets = new List<byte[]>();

            while (true)
            {
                var packet = ExtractSinglePacket();
                if (packet == null)
                    break;
                
                packets.Add(packet);
            }

            return packets;
        }

        /// <summary>
        /// Trích xuất một packet hoàn chỉnh từ buffer
        /// </summary>
        /// <returns>Packet data hoặc null nếu không có packet hoàn chỉnh</returns>
        private byte[]? ExtractSinglePacket()
        {
            if (_buffer.Count < MIN_PACKET_SIZE)
                return null;

            // Tìm vị trí bắt đầu của packet (header AA55)
            int headerIndex = FindPacketHeader();
            if (headerIndex == -1)
            {
                // Không tìm thấy header hợp lệ, xóa buffer
                Logger.Instance.Warning($"Không tìm thấy packet header hợp lệ, xóa {_buffer.Count} bytes");
                _buffer.Clear();
                return null;
            }

            // Nếu header không ở đầu buffer, xóa dữ liệu rác trước header
            if (headerIndex > 0)
            {
                Logger.Instance.Debug($"Xóa {headerIndex} bytes rác trước packet header");
                _buffer.RemoveRange(0, headerIndex);
            }

            // Kiểm tra xem có đủ dữ liệu để đọc packet length không
            if (_buffer.Count < 4)
                return null;

            // Đọc packet length từ bytes 2-3 (sau header AA55)
            int dataLength = BitConverter.ToUInt16(_buffer.ToArray(), 2);
            int totalPacketLength = dataLength + 6; // +6 cho header(2) + length(2) + footer(2)

            // Validate packet length
            if (dataLength < 0 || totalPacketLength > 65535) // Max reasonable packet size
            {
                Logger.Instance.Warning($"Packet length không hợp lệ: {dataLength}, loại bỏ header hiện tại");
                _buffer.RemoveAt(0); // Xóa byte đầu tiên và thử lại
                return ExtractSinglePacket();
            }

            // Kiểm tra xem buffer có đủ dữ liệu cho packet hoàn chỉnh không
            if (_buffer.Count < totalPacketLength)
            {
                Logger.Instance.Debug($"Buffer chưa đủ dữ liệu: có {_buffer.Count}, cần {totalPacketLength}");
                return null;
            }

            // Kiểm tra footer
            int footerIndex = totalPacketLength - 2;
            if (!IsValidFooter(footerIndex))
            {
                Logger.Instance.Warning($"Packet footer không hợp lệ tại vị trí {footerIndex}");
                LogPacketDebugInfo(totalPacketLength);
                
                // Xóa byte đầu tiên và thử lại
                _buffer.RemoveAt(0);
                return ExtractSinglePacket();
            }

            // Trích xuất packet hoàn chỉnh
            byte[] packetData = new byte[totalPacketLength];
            _buffer.CopyTo(0, packetData, 0, totalPacketLength);

            // Xóa packet đã xử lý khỏi buffer
            _buffer.RemoveRange(0, totalPacketLength);

            Logger.Instance.Debug($"Trích xuất packet thành công: length={totalPacketLength}, buffer còn lại={_buffer.Count}");

            return packetData;
        }

        /// <summary>
        /// Tìm vị trí header packet trong buffer
        /// </summary>
        /// <returns>Index của header hoặc -1 nếu không tìm thấy</returns>
        private int FindPacketHeader()
        {
            for (int i = 0; i <= _buffer.Count - 2; i++)
            {
                if (_buffer[i] == PACKET_HEADER_1 && _buffer[i + 1] == PACKET_HEADER_2)
                {
                    return i;
                }
            }
            return -1;
        }

        /// <summary>
        /// Kiểm tra footer có hợp lệ không
        /// </summary>
        /// <param name="footerIndex">Vị trí bắt đầu của footer</param>
        /// <returns>True nếu footer hợp lệ</returns>
        private bool IsValidFooter(int footerIndex)
        {
            if (footerIndex + 1 >= _buffer.Count)
                return false;

            return _buffer[footerIndex] == PACKET_FOOTER_1 && 
                   _buffer[footerIndex + 1] == PACKET_FOOTER_2;
        }

        /// <summary>
        /// Log thông tin debug cho packet
        /// </summary>
        /// <param name="packetLength">Độ dài packet</param>
        private void LogPacketDebugInfo(int packetLength)
        {
            int debugLength = Math.Min(packetLength, 32); // Chỉ log 32 bytes đầu
            var debugBytes = new byte[debugLength];
            _buffer.CopyTo(0, debugBytes, 0, debugLength);
            
            string hexString = Convert.ToHexString(debugBytes);
            Logger.Instance.Debug($"Packet debug (first {debugLength} bytes): {hexString}");
        }

        /// <summary>
        /// Xóa toàn bộ buffer (dùng khi có lỗi nghiêm trọng)
        /// </summary>
        public void Clear()
        {
            _buffer.Clear();
        }

        /// <summary>
        /// Lấy số bytes hiện có trong buffer
        /// </summary>
        public int BufferSize => _buffer.Count;

        /// <summary>
        /// Kiểm tra xem buffer có dữ liệu không
        /// </summary>
        public bool HasData => _buffer.Count > 0;
    }
}
