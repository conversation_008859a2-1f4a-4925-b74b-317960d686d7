using System;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;

namespace HeroYulgang.Core.Native.Messaging
{
    /// <summary>
    /// Core message system interfaces - thay thế Akka.NET message passing
    /// </summary>
    
    /// <summary>
    /// Thay thế IActorRef - target để gửi message
    /// </summary>
    public interface IMessageTarget
    {
        /// <summary>
        /// Gửi message async (thay thế actor.Tell)
        /// </summary>
        Task SendAsync<T>(T message, CancellationToken cancellationToken = default) where T : class;
        
        /// <summary>
        /// Gửi message sync (fire-and-forget)
        /// </summary>
        void Send<T>(T message) where T : class;
        
        /// <summary>
        /// Unique identifier cho message target
        /// </summary>
        string Id { get; }
        
        /// <summary>
        /// Kiểm tra target có còn active không
        /// </summary>
        bool IsActive { get; }
    }

    /// <summary>
    /// Context cho message processing
    /// </summary>
    public interface IMessageContext
    {
        /// <summary>
        /// Sender của message (nếu có)
        /// </summary>
        IMessageTarget? Sender { get; }
        
        /// <summary>
        /// Target hiện tại đang xử lý message
        /// </summary>
        IMessageTarget Self { get; }
        
        /// <summary>
        /// Cancellation token cho operation
        /// </summary>
        CancellationToken CancellationToken { get; }
        
        /// <summary>
        /// Reply lại cho sender (thay thế Sender.Tell)
        /// </summary>
        Task ReplyAsync<T>(T response) where T : class;
    }

    /// <summary>
    /// Handler cho một loại message cụ thể
    /// </summary>
    public interface IMessageHandler<in T> where T : class
    {
        /// <summary>
        /// Xử lý message (thay thế Receive<T> trong Actor)
        /// </summary>
        Task HandleAsync(T message, IMessageContext context);
    }

    /// <summary>
    /// Request-Response pattern (thay thế Ask)
    /// </summary>
    public interface IRequestResponseService
    {
        /// <summary>
        /// Gửi request và chờ response (thay thế actor.Ask)
        /// </summary>
        Task<TResponse> SendRequestAsync<TRequest, TResponse>(
            IMessageTarget target, 
            TRequest request, 
            TimeSpan? timeout = null,
            CancellationToken cancellationToken = default)
            where TRequest : class
            where TResponse : class;
    }

    /// <summary>
    /// Event bus cho publish/subscribe pattern
    /// </summary>
    public interface IEventBus
    {
        /// <summary>
        /// Publish event đến tất cả subscribers
        /// </summary>
        Task PublishAsync<T>(T eventData, CancellationToken cancellationToken = default) where T : class;
        
        /// <summary>
        /// Subscribe với function handler
        /// </summary>
        IDisposable Subscribe<T>(Func<T, Task> handler) where T : class;
        
        /// <summary>
        /// Subscribe với typed handler
        /// </summary>
        IDisposable Subscribe<T>(IEventHandler<T> handler) where T : class;
        
        /// <summary>
        /// Subscribe với message target
        /// </summary>
        IDisposable Subscribe<T>(IMessageTarget target) where T : class;
    }

    /// <summary>
    /// Event handler interface
    /// </summary>
    public interface IEventHandler<in T> where T : class
    {
        Task HandleEventAsync(T eventData, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Message routing service
    /// </summary>
    public interface IMessageRouter
    {
        /// <summary>
        /// Route message đến target phù hợp
        /// </summary>
        Task RouteAsync<T>(T message, string routingKey, CancellationToken cancellationToken = default) where T : class;
        
        /// <summary>
        /// Register routing rule
        /// </summary>
        void RegisterRoute<T>(string routingKey, IMessageTarget target) where T : class;
        
        /// <summary>
        /// Register routing rule với selector
        /// </summary>
        void RegisterRoute<T>(Func<T, string> keySelector, IMessageTarget target) where T : class;
    }

    /// <summary>
    /// Message priority levels
    /// </summary>
    public enum MessagePriority
    {
        Low = 0,
        Normal = 1,
        High = 2,
        Critical = 3
    }

    /// <summary>
    /// Priority message wrapper
    /// </summary>
    public class PriorityMessage<T> where T : class
    {
        public T Message { get; }
        public MessagePriority Priority { get; }
        public DateTime Timestamp { get; }
        
        public PriorityMessage(T message, MessagePriority priority = MessagePriority.Normal)
        {
            Message = message;
            Priority = priority;
            Timestamp = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Message với metadata
    /// </summary>
    public class MessageEnvelope<T> where T : class
    {
        public T Message { get; }
        public IMessageTarget? Sender { get; }
        public string MessageId { get; }
        public DateTime Timestamp { get; }
        public MessagePriority Priority { get; }
        
        public MessageEnvelope(T message, IMessageTarget? sender = null, MessagePriority priority = MessagePriority.Normal)
        {
            Message = message;
            Sender = sender;
            MessageId = Guid.NewGuid().ToString();
            Timestamp = DateTime.UtcNow;
            Priority = priority;
        }
    }

    /// <summary>
    /// Base message interface
    /// </summary>
    public interface IMessage
    {
        string MessageId { get; }
        DateTime Timestamp { get; }
    }

    /// <summary>
    /// Base message implementation
    /// </summary>
    public abstract class MessageBase : IMessage
    {
        public string MessageId { get; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; } = DateTime.UtcNow;
    }

    /// <summary>
    /// System messages
    /// </summary>
    public class SystemMessage : MessageBase
    {
        public string Type { get; }
        public object? Data { get; }
        
        public SystemMessage(string type, object? data = null)
        {
            Type = type;
            Data = data;
        }
    }

    /// <summary>
    /// Lifecycle messages
    /// </summary>
    public class StartMessage : SystemMessage
    {
        public StartMessage() : base("Start") { }
    }

    public class StopMessage : SystemMessage
    {
        public StopMessage() : base("Stop") { }
    }

    public class RestartMessage : SystemMessage
    {
        public RestartMessage() : base("Restart") { }
    }

    /// <summary>
    /// Error handling message
    /// </summary>
    public class ErrorMessage : SystemMessage
    {
        public Exception Exception { get; }
        public IMessage? OriginalMessage { get; }
        
        public ErrorMessage(Exception exception, IMessage? originalMessage = null) 
            : base("Error", exception)
        {
            Exception = exception;
            OriginalMessage = originalMessage;
        }
    }
}
