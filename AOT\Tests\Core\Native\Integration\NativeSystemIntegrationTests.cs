using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NUnit.Framework;
using HeroYulgang.Core.Native.Integration;
using HeroYulgang.Core.Native.Messaging;
using HeroYulgang.Core.Native.Network;
using HeroYulgang.Core.Native.Packets;
using HeroYulgang.Core.Native.Sessions;

namespace HeroYulgang.Tests.Core.Native.Integration
{
    /// <summary>
    /// Integration tests for the complete native system
    /// </summary>
    [TestFixture]
    public class NativeSystemIntegrationTests
    {
        private IHost _host = null!;
        private IServiceProvider _serviceProvider = null!;
        private int _serverPort;
        private CancellationTokenSource _cancellationTokenSource = null!;

        [SetUp]
        public async Task SetUp()
        {
            _cancellationTokenSource = new CancellationTokenSource();
            _serverPort = GetAvailablePort();

            var builder = Host.CreateDefaultBuilder()
                .ConfigureServices(services =>
                {
                    services.AddNativeSystemBuilder()
                        .ConfigureNetwork(config =>
                        {
                            config.Port = _serverPort;
                            config.MaxConnections = 10;
                            config.ReceiveBufferSize = 1024;
                            config.SendBufferSize = 1024;
                            config.KeepAlive = true;
                            config.NoDelay = true;
                        })
                        .ConfigurePacketProcessing(config =>
                        {
                            config.WorkerThreadCount = 2;
                            config.MaxQueueSize = 100;
                            config.BatchProcessingEnabled = false;
                            config.EnablePacketLogging = true;
                        })
                        .ConfigureSessions(config =>
                        {
                            config.SessionTimeout = TimeSpan.FromMinutes(5);
                            config.AuthenticationTimeout = TimeSpan.FromMinutes(1);
                            config.MaxSessionsPerAccount = 1;
                            config.CleanupInterval = TimeSpan.FromSeconds(30);
                        })
                        .AddPacketHandler<Packet, TestLoginPacketHandler>()
                        .AddPacketHandler<Packet, TestChatPacketHandler>()
                        .EnableDevelopmentMode()
                        .Build();
                });

            _host = builder.Build();
            _serviceProvider = _host.Services;

            // Start the host
            await _host.StartAsync(_cancellationTokenSource.Token);
            
            // Wait for server to be ready
            await Task.Delay(500);
        }

        [TearDown]
        public async Task TearDown()
        {
            try
            {
                if (_host != null)
                {
                    await _host.StopAsync(_cancellationTokenSource.Token);
                    _host.Dispose();
                }
            }
            finally
            {
                _cancellationTokenSource?.Dispose();
            }
        }

        [Test]
        public async Task Complete_Client_Connection_Workflow_Should_Work()
        {
            // Arrange
            var eventBus = _serviceProvider.GetRequiredService<IEventBus>();
            var sessionManager = _serviceProvider.GetRequiredService<ISessionManager>();
            
            var clientConnectedReceived = new TaskCompletionSource<ClientConnectedEvent>();
            var clientDisconnectedReceived = new TaskCompletionSource<ClientDisconnectedEvent>();

            eventBus.Subscribe<ClientConnectedEvent>(evt =>
            {
                clientConnectedReceived.TrySetResult(evt);
                return Task.CompletedTask;
            });

            eventBus.Subscribe<ClientDisconnectedEvent>(evt =>
            {
                clientDisconnectedReceived.TrySetResult(evt);
                return Task.CompletedTask;
            });

            // Act - Connect client
            using var client = new TcpClient();
            await client.ConnectAsync(IPAddress.Loopback, _serverPort);

            // Assert - Connection event should be received
            var connectedEvent = await WaitForTaskAsync(clientConnectedReceived.Task, TimeSpan.FromSeconds(2));
            Assert.IsNotNull(connectedEvent);
            Assert.Greater(connectedEvent.SessionId, 0);

            // Verify session was created
            var session = await sessionManager.GetPlayerSessionAsync(connectedEvent.SessionId);
            Assert.IsNotNull(session);
            Assert.IsFalse(session.IsAuthenticated);

            // Act - Disconnect client
            client.Close();

            // Assert - Disconnection event should be received
            var disconnectedEvent = await WaitForTaskAsync(clientDisconnectedReceived.Task, TimeSpan.FromSeconds(2));
            Assert.IsNotNull(disconnectedEvent);
            Assert.AreEqual(connectedEvent.SessionId, disconnectedEvent.SessionId);

            // Verify session was removed
            await Task.Delay(100); // Give time for cleanup
            var sessionAfterDisconnect = await sessionManager.GetPlayerSessionAsync(connectedEvent.SessionId);
            Assert.IsNull(sessionAfterDisconnect);
        }

        [Test]
        public async Task Login_Packet_Processing_Workflow_Should_Work()
        {
            // Arrange
            var eventBus = _serviceProvider.GetRequiredService<IEventBus>();
            var sessionManager = _serviceProvider.GetRequiredService<ISessionManager>();
            
            var clientConnectedReceived = new TaskCompletionSource<ClientConnectedEvent>();
            var loginProcessedReceived = new TaskCompletionSource<bool>();

            eventBus.Subscribe<ClientConnectedEvent>(evt =>
            {
                clientConnectedReceived.TrySetResult(evt);
                return Task.CompletedTask;
            });

            // Subscribe to login completion (this would be implemented in the actual handler)
            var testHandler = _serviceProvider.GetServices<IPacketHandler<Packet>>()
                .OfType<TestLoginPacketHandler>()
                .FirstOrDefault();
            
            if (testHandler != null)
            {
                testHandler.LoginProcessed += () => loginProcessedReceived.TrySetResult(true);
            }

            // Act - Connect and send login packet
            using var client = new TcpClient();
            await client.ConnectAsync(IPAddress.Loopback, _serverPort);

            var connectedEvent = await WaitForTaskAsync(clientConnectedReceived.Task, TimeSpan.FromSeconds(2));
            Assert.IsNotNull(connectedEvent);

            // Send login packet
            var loginPacket = CreateLoginPacket();
            var stream = client.GetStream();
            await stream.WriteAsync(loginPacket);

            // Assert - Login should be processed
            var loginProcessed = await WaitForTaskAsync(loginProcessedReceived.Task, TimeSpan.FromSeconds(3));
            Assert.IsTrue(loginProcessed);

            // Verify session is authenticated
            var session = await sessionManager.GetPlayerSessionAsync(connectedEvent.SessionId);
            Assert.IsNotNull(session);
            Assert.IsTrue(session.IsAuthenticated);
            Assert.IsNotNull(session.AccountId);
            Assert.IsNotNull(session.CharacterName);
        }

        [Test]
        public async Task Multiple_Concurrent_Clients_Should_Be_Handled()
        {
            // Arrange
            const int clientCount = 5;
            var eventBus = _serviceProvider.GetRequiredService<IEventBus>();
            var sessionManager = _serviceProvider.GetRequiredService<ISessionManager>();
            
            var connectedEvents = new List<ClientConnectedEvent>();
            var connectedEventsReceived = new TaskCompletionSource<bool>();
            var lockObject = new object();

            eventBus.Subscribe<ClientConnectedEvent>(evt =>
            {
                lock (lockObject)
                {
                    connectedEvents.Add(evt);
                    if (connectedEvents.Count >= clientCount)
                    {
                        connectedEventsReceived.TrySetResult(true);
                    }
                }
                return Task.CompletedTask;
            });

            // Act - Connect multiple clients concurrently
            var clients = new TcpClient[clientCount];
            var connectTasks = new Task[clientCount];

            for (int i = 0; i < clientCount; i++)
            {
                var clientIndex = i;
                connectTasks[i] = Task.Run(async () =>
                {
                    clients[clientIndex] = new TcpClient();
                    await clients[clientIndex].ConnectAsync(IPAddress.Loopback, _serverPort);
                });
            }

            await Task.WhenAll(connectTasks);

            // Assert - All connections should be received
            await WaitForTaskAsync(connectedEventsReceived.Task, TimeSpan.FromSeconds(5));
            Assert.AreEqual(clientCount, connectedEvents.Count);

            // Verify all sessions were created
            var sessionIds = connectedEvents.Select(e => e.SessionId).ToArray();
            Assert.AreEqual(clientCount, sessionIds.Distinct().Count()); // All unique session IDs

            foreach (var sessionId in sessionIds)
            {
                var session = await sessionManager.GetPlayerSessionAsync(sessionId);
                Assert.IsNotNull(session, $"Session {sessionId} should exist");
            }

            // Cleanup
            foreach (var client in clients)
            {
                client?.Close();
                client?.Dispose();
            }
        }

        [Test]
        public async Task Packet_Priority_Processing_Should_Work_Correctly()
        {
            // Arrange
            var eventBus = _serviceProvider.GetRequiredService<IEventBus>();
            var packetProcessor = _serviceProvider.GetRequiredService<IPacketProcessor>();
            
            var processedPackets = new List<(PacketType Type, DateTime ProcessedAt)>();
            var allProcessed = new TaskCompletionSource<bool>();
            var lockObject = new object();

            var clientConnectedReceived = new TaskCompletionSource<ClientConnectedEvent>();
            eventBus.Subscribe<ClientConnectedEvent>(evt =>
            {
                clientConnectedReceived.TrySetResult(evt);
                return Task.CompletedTask;
            });

            // Register test handler to track processing order
            packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                lock (lockObject)
                {
                    processedPackets.Add((packet.Type, DateTime.UtcNow));
                    if (processedPackets.Count >= 3)
                    {
                        allProcessed.TrySetResult(true);
                    }
                }
            });

            // Act - Connect client
            using var client = new TcpClient();
            await client.ConnectAsync(IPAddress.Loopback, _serverPort);

            var connectedEvent = await WaitForTaskAsync(clientConnectedReceived.Task, TimeSpan.FromSeconds(2));
            var session = await _serviceProvider.GetRequiredService<ISessionManager>()
                .GetPlayerSessionAsync(connectedEvent.SessionId);

            // Send packets with different priorities (in reverse priority order)
            var lowPriorityPacket = new Packet(PacketType.Movement, new byte[] { 0x01 }, connectedEvent.SessionId);
            var normalPriorityPacket = new Packet(PacketType.Chat, new byte[] { 0x02 }, connectedEvent.SessionId);
            var highPriorityPacket = new Packet(PacketType.Login, new byte[] { 0x03 }, connectedEvent.SessionId);

            await packetProcessor.ProcessAsync(lowPriorityPacket, session!.PacketContext, PacketPriority.Low);
            await packetProcessor.ProcessAsync(normalPriorityPacket, session.PacketContext, PacketPriority.Normal);
            await packetProcessor.ProcessAsync(highPriorityPacket, session.PacketContext, PacketPriority.Critical);

            // Assert - High priority should be processed first
            await WaitForTaskAsync(allProcessed.Task, TimeSpan.FromSeconds(3));
            Assert.AreEqual(3, processedPackets.Count);

            // Verify processing order (high priority first)
            Assert.AreEqual(PacketType.Login, processedPackets[0].Type);
        }

        [Test]
        public async Task System_Statistics_Should_Be_Accurate()
        {
            // Arrange
            var sessionManager = _serviceProvider.GetRequiredService<ISessionManager>();
            var packetProcessor = _serviceProvider.GetRequiredService<IPacketProcessor>();
            var eventBus = _serviceProvider.GetRequiredService<IEventBus>();

            var clientConnectedReceived = new TaskCompletionSource<ClientConnectedEvent>();
            eventBus.Subscribe<ClientConnectedEvent>(evt =>
            {
                clientConnectedReceived.TrySetResult(evt);
                return Task.CompletedTask;
            });

            // Act - Connect client and process some packets
            using var client = new TcpClient();
            await client.ConnectAsync(IPAddress.Loopback, _serverPort);

            var connectedEvent = await WaitForTaskAsync(clientConnectedReceived.Task, TimeSpan.FromSeconds(2));
            var session = await sessionManager.GetPlayerSessionAsync(connectedEvent.SessionId);

            // Process some packets
            for (int i = 0; i < 5; i++)
            {
                var packet = new Packet(PacketType.Chat, new byte[] { (byte)i }, connectedEvent.SessionId);
                await packetProcessor.ProcessAsync(packet, session!.PacketContext);
            }

            await Task.Delay(100); // Allow processing to complete

            // Assert - Statistics should reflect activity
            var sessionStats = sessionManager.GetStatistics();
            Assert.AreEqual(1, sessionStats.TotalSessions);
            Assert.AreEqual(0, sessionStats.AuthenticatedSessions); // Not authenticated yet

            var packetStats = packetProcessor.GetStatistics();
            Assert.GreaterOrEqual(packetStats.TotalPacketsProcessed, 5);
            Assert.GreaterOrEqual(packetStats.PacketsPerSecond, 0);
        }

        [Test]
        public async Task Error_Handling_Should_Not_Crash_System()
        {
            // Arrange
            var eventBus = _serviceProvider.GetRequiredService<IEventBus>();
            var packetProcessor = _serviceProvider.GetRequiredService<IPacketProcessor>();

            var clientConnectedReceived = new TaskCompletionSource<ClientConnectedEvent>();
            eventBus.Subscribe<ClientConnectedEvent>(evt =>
            {
                clientConnectedReceived.TrySetResult(evt);
                return Task.CompletedTask;
            });

            // Register a handler that throws exceptions
            packetProcessor.RegisterHandler<Packet>(async (packet, context) =>
            {
                throw new InvalidOperationException("Test exception");
            });

            // Act - Connect and send packets that will cause exceptions
            using var client = new TcpClient();
            await client.ConnectAsync(IPAddress.Loopback, _serverPort);

            var connectedEvent = await WaitForTaskAsync(clientConnectedReceived.Task, TimeSpan.FromSeconds(2));
            var session = await _serviceProvider.GetRequiredService<ISessionManager>()
                .GetPlayerSessionAsync(connectedEvent.SessionId);

            // Send multiple packets that will cause exceptions
            for (int i = 0; i < 3; i++)
            {
                var packet = new Packet(PacketType.Chat, new byte[] { (byte)i }, connectedEvent.SessionId);
                
                // This should not crash the system
                try
                {
                    await packetProcessor.ProcessAsync(packet, session!.PacketContext);
                }
                catch
                {
                    // Exceptions may be thrown, but system should remain stable
                }
            }

            // Assert - System should still be responsive
            var newClient = new TcpClient();
            await newClient.ConnectAsync(IPAddress.Loopback, _serverPort);
            
            // If we can connect, the system is still working
            Assert.IsTrue(newClient.Connected);
            newClient.Close();
        }

        // Helper methods
        private int GetAvailablePort()
        {
            var listener = new TcpListener(IPAddress.Loopback, 0);
            listener.Start();
            var port = ((IPEndPoint)listener.LocalEndpoint).Port;
            listener.Stop();
            return port;
        }

        private async Task<T> WaitForTaskAsync<T>(Task<T> task, TimeSpan timeout)
        {
            using var cts = new CancellationTokenSource(timeout);
            try
            {
                return await task.WaitAsync(cts.Token);
            }
            catch (OperationCanceledException)
            {
                throw new TimeoutException($"Task did not complete within {timeout}");
            }
        }

        private byte[] CreateLoginPacket()
        {
            // Create a simple login packet for testing
            // In real implementation, this would follow the actual packet format
            return new byte[] { 0x01, 0x00, 0x04, 0x00, 0x01, 0x02, 0x03, 0x04 };
        }
    }

    /// <summary>
    /// Test login packet handler for integration tests
    /// </summary>
    public class TestLoginPacketHandler : IPacketHandler<Packet>
    {
        public event Action? LoginProcessed;

        public async Task HandleAsync(Packet packet, IPacketContext context)
        {
            if (packet.Type == PacketType.Login)
            {
                // Simulate login processing
                await Task.Delay(50);
                
                // Authenticate the session (simplified)
                if (context is PacketContext pc && pc.Session is IPlayerSession session)
                {
                    // This would normally be done through SessionManager
                    // For testing, we'll simulate it
                    await Task.Delay(10);
                }

                LoginProcessed?.Invoke();
            }
        }

        public bool CanHandle(PacketType packetType) => packetType == PacketType.Login;
    }

    /// <summary>
    /// Test chat packet handler for integration tests
    /// </summary>
    public class TestChatPacketHandler : IPacketHandler<Packet>
    {
        public async Task HandleAsync(Packet packet, IPacketContext context)
        {
            if (packet.Type == PacketType.Chat)
            {
                // Simulate chat processing
                await Task.Delay(10);
            }
        }

        public bool CanHandle(PacketType packetType) => packetType == PacketType.Chat;
    }
}
