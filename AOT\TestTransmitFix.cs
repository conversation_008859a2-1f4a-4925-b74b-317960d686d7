using System;
using System.Threading.Tasks;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Tests
{
    /// <summary>
    /// Test script để kiểm tra fix cho lỗi "Can't write the message because the previous write is in progress"
    /// </summary>
    public class TestTransmitFix
    {
        public static async Task RunConcurrentTransmitTest()
        {
            Console.WriteLine("🧪 Bắt đầu test concurrent transmit messages...");
            
            var loginClient = LoginServerClient.Instance;
            
            // Tạo nhiều task gửi message đồng thời
            var tasks = new Task[10];
            
            for (int i = 0; i < 10; i++)
            {
                int taskId = i;
                tasks[i] = Task.Run(async () =>
                {
                    try
                    {
                        string message = $"Test message {taskId} - {DateTime.Now:HH:mm:ss.fff}";
                        Console.WriteLine($"📤 Task {taskId}: Đang gửi message: {message}");
                        
                        await loginClient.SendTransmitMessageAsync(message);
                        
                        Console.WriteLine($"✅ Task {taskId}: G<PERSON><PERSON> thành công!");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ Task {taskId}: Lỗi - {ex.Message}");
                    }
                });
            }
            
            // Chờ tất cả tasks hoàn thành
            await Task.WhenAll(tasks);
            
            Console.WriteLine("🎉 Hoàn thành test concurrent transmit messages!");
        }
        
        public static async Task RunStressTest()
        {
            Console.WriteLine("🔥 Bắt đầu stress test với 50 messages...");
            
            var loginClient = LoginServerClient.Instance;
            var tasks = new Task[50];
            
            for (int i = 0; i < 50; i++)
            {
                int taskId = i;
                tasks[i] = Task.Run(async () =>
                {
                    try
                    {
                        // Random delay để tạo race condition
                        await Task.Delay(Random.Shared.Next(1, 100));
                        
                        string message = $"Stress test {taskId} - {DateTime.Now:HH:mm:ss.fff}";
                        await loginClient.SendTransmitMessageAsync(message);
                        
                        Console.WriteLine($"✅ Stress {taskId}: OK");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ Stress {taskId}: {ex.Message}");
                    }
                });
            }
            
            var startTime = DateTime.Now;
            await Task.WhenAll(tasks);
            var endTime = DateTime.Now;
            
            Console.WriteLine($"🎉 Stress test hoàn thành trong {(endTime - startTime).TotalMilliseconds}ms");
        }
        
        public static async Task RunSequentialTest()
        {
            Console.WriteLine("📋 Bắt đầu sequential test...");
            
            var loginClient = LoginServerClient.Instance;
            
            for (int i = 0; i < 5; i++)
            {
                try
                {
                    string message = $"Sequential message {i} - {DateTime.Now:HH:mm:ss.fff}";
                    Console.WriteLine($"📤 Gửi sequential message {i}");
                    
                    await loginClient.SendTransmitMessageAsync(message);
                    
                    Console.WriteLine($"✅ Sequential {i}: Thành công");
                    
                    // Delay nhỏ giữa các message
                    await Task.Delay(100);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Sequential {i}: {ex.Message}");
                }
            }
            
            Console.WriteLine("🎉 Sequential test hoàn thành!");
        }
    }
}
