using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using HeroYulgang.Core.Native.Sessions;

namespace HeroYulgang.Tests.Core.Native.Sessions
{
    /// <summary>
    /// Tests for SessionManager implementation
    /// </summary>
    [TestFixture]
    public class SessionManagerTests : TestBase
    {
        private ISessionManager _sessionManager = null!;
        private IAuthenticationService _authService = null!;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            _sessionManager = ServiceProvider.GetRequiredService<ISessionManager>();
            _authService = ServiceProvider.GetRequiredService<IAuthenticationService>();
        }

        [Test]
        public async Task CreatePlayerSessionAsync_Should_Create_Valid_Session()
        {
            // Arrange
            var mockNetworkSession = new MockClientSession(1);

            // Act
            var playerSession = await _sessionManager.CreatePlayerSessionAsync(mockNetworkSession);

            // Assert
            Assert.IsNotNull(playerSession);
            Assert.AreEqual(1, playerSession.SessionId);
            Assert.IsFalse(playerSession.IsAuthenticated);
            Assert.IsNull(playerSession.AccountId);
            Assert.IsNull(playerSession.CharacterName);
            Assert.IsNotNull(playerSession.PacketContext);
        }

        [Test]
        public async Task GetPlayerSessionAsync_Should_Return_Existing_Session()
        {
            // Arrange
            var mockNetworkSession = new MockClientSession(1);
            var createdSession = await _sessionManager.CreatePlayerSessionAsync(mockNetworkSession);

            // Act
            var retrievedSession = await _sessionManager.GetPlayerSessionAsync(1);

            // Assert
            Assert.IsNotNull(retrievedSession);
            Assert.AreEqual(createdSession.SessionId, retrievedSession.SessionId);
            Assert.AreSame(createdSession, retrievedSession);
        }

        [Test]
        public async Task GetPlayerSessionAsync_Should_Return_Null_For_Nonexistent_Session()
        {
            // Act
            var session = await _sessionManager.GetPlayerSessionAsync(999);

            // Assert
            Assert.IsNull(session);
        }

        [Test]
        public async Task AuthenticateSessionAsync_Should_Authenticate_Valid_Session()
        {
            // Arrange
            var mockNetworkSession = new MockClientSession(1);
            var playerSession = await _sessionManager.CreatePlayerSessionAsync(mockNetworkSession);

            // Act
            var result = await _sessionManager.AuthenticateSessionAsync(1, 12345, "TestPlayer");

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(playerSession.IsAuthenticated);
            Assert.AreEqual(12345, playerSession.AccountId);
            Assert.AreEqual("TestPlayer", playerSession.CharacterName);
        }

        [Test]
        public async Task AuthenticateSessionAsync_Should_Fail_For_Nonexistent_Session()
        {
            // Act
            var result = await _sessionManager.AuthenticateSessionAsync(999, 12345, "TestPlayer");

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public async Task SetPlayerAsync_Should_Set_Player_Data()
        {
            // Arrange
            var mockNetworkSession = new MockClientSession(1);
            var playerSession = await _sessionManager.CreatePlayerSessionAsync(mockNetworkSession);
            var playerData = new { Name = "TestPlayer", Level = 50, Experience = 12345 };

            // Act
            var result = await _sessionManager.SetPlayerAsync(1, playerData);

            // Assert
            Assert.IsTrue(result);
            Assert.AreEqual(playerData, playerSession.Player);
        }

        [Test]
        public async Task ClosePlayerSessionAsync_Should_Remove_Session()
        {
            // Arrange
            var mockNetworkSession = new MockClientSession(1);
            var playerSession = await _sessionManager.CreatePlayerSessionAsync(mockNetworkSession);

            // Act
            await _sessionManager.ClosePlayerSessionAsync(1, "Test close");

            // Assert
            var retrievedSession = await _sessionManager.GetPlayerSessionAsync(1);
            Assert.IsNull(retrievedSession);
            Assert.IsFalse(mockNetworkSession.IsConnected);
        }

        [Test]
        public async Task GetSessionByAccountAsync_Should_Return_Authenticated_Session()
        {
            // Arrange
            var mockNetworkSession = new MockClientSession(1);
            var playerSession = await _sessionManager.CreatePlayerSessionAsync(mockNetworkSession);
            await _sessionManager.AuthenticateSessionAsync(1, 12345, "TestPlayer");

            // Act
            var retrievedSession = await _sessionManager.GetSessionByAccountAsync(12345);

            // Assert
            Assert.IsNotNull(retrievedSession);
            Assert.AreEqual(1, retrievedSession.SessionId);
            Assert.AreEqual(12345, retrievedSession.AccountId);
        }

        [Test]
        public async Task GetSessionByAccountAsync_Should_Return_Null_For_Unauthenticated_Session()
        {
            // Arrange
            var mockNetworkSession = new MockClientSession(1);
            await _sessionManager.CreatePlayerSessionAsync(mockNetworkSession);
            // Don't authenticate

            // Act
            var retrievedSession = await _sessionManager.GetSessionByAccountAsync(12345);

            // Assert
            Assert.IsNull(retrievedSession);
        }

        [Test]
        public async Task Multiple_Sessions_Should_Be_Managed_Independently()
        {
            // Arrange
            var session1 = new MockClientSession(1);
            var session2 = new MockClientSession(2);
            var session3 = new MockClientSession(3);

            // Act
            var playerSession1 = await _sessionManager.CreatePlayerSessionAsync(session1);
            var playerSession2 = await _sessionManager.CreatePlayerSessionAsync(session2);
            var playerSession3 = await _sessionManager.CreatePlayerSessionAsync(session3);

            await _sessionManager.AuthenticateSessionAsync(1, 100, "Player1");
            await _sessionManager.AuthenticateSessionAsync(2, 200, "Player2");
            // Leave session 3 unauthenticated

            // Assert
            var stats = _sessionManager.GetStatistics();
            Assert.AreEqual(3, stats.TotalSessions);
            Assert.AreEqual(2, stats.AuthenticatedSessions);
            Assert.AreEqual(1, stats.UnauthenticatedSessions);

            // Verify individual sessions
            var retrieved1 = await _sessionManager.GetPlayerSessionAsync(1);
            var retrieved2 = await _sessionManager.GetPlayerSessionAsync(2);
            var retrieved3 = await _sessionManager.GetPlayerSessionAsync(3);

            Assert.IsTrue(retrieved1!.IsAuthenticated);
            Assert.IsTrue(retrieved2!.IsAuthenticated);
            Assert.IsFalse(retrieved3!.IsAuthenticated);

            Assert.AreEqual("Player1", retrieved1.CharacterName);
            Assert.AreEqual("Player2", retrieved2.CharacterName);
            Assert.IsNull(retrieved3.CharacterName);
        }

        [Test]
        public async Task Duplicate_Account_Login_Should_Be_Handled_According_To_Configuration()
        {
            // Arrange
            var session1 = new MockClientSession(1);
            var session2 = new MockClientSession(2);

            var playerSession1 = await _sessionManager.CreatePlayerSessionAsync(session1);
            var playerSession2 = await _sessionManager.CreatePlayerSessionAsync(session2);

            // Authenticate first session
            await _sessionManager.AuthenticateSessionAsync(1, 12345, "TestPlayer");

            // Act - Try to authenticate second session with same account
            var result = await _sessionManager.AuthenticateSessionAsync(2, 12345, "TestPlayer");

            // Assert - Behavior depends on configuration
            // If PreventDuplicateLogins is true, second authentication should fail
            // If false, first session should be disconnected
            var session1Retrieved = await _sessionManager.GetPlayerSessionAsync(1);
            var session2Retrieved = await _sessionManager.GetPlayerSessionAsync(2);

            // At least one should be authenticated
            Assert.IsTrue(session1Retrieved?.IsAuthenticated == true || session2Retrieved?.IsAuthenticated == true);
        }

        [Test]
        public async Task Session_Activity_Should_Be_Tracked()
        {
            // Arrange
            var mockNetworkSession = new MockClientSession(1);
            var playerSession = await _sessionManager.CreatePlayerSessionAsync(mockNetworkSession);

            var initialActivity = playerSession.LastActivity;

            // Act
            await Task.Delay(100); // Wait a bit
            
            if (playerSession is PlayerSession ps)
            {
                ps.UpdateActivity();
            }

            // Assert
            Assert.Greater(playerSession.LastActivity, initialActivity);
            Assert.Greater(playerSession.GetIdleTime().TotalMilliseconds, 0);
        }

        [Test]
        public async Task Session_Cleanup_Should_Remove_Expired_Sessions()
        {
            // This test would require configuring a very short session timeout
            // and waiting for cleanup to occur. Implementation depends on
            // the actual cleanup mechanism in SessionManager.

            // Arrange
            var mockNetworkSession = new MockClientSession(1);
            var playerSession = await _sessionManager.CreatePlayerSessionAsync(mockNetworkSession);

            // For this test, we'll simulate an expired session by
            // manually triggering cleanup if the API supports it
            
            // Act & Assert
            // This is a placeholder - actual implementation would depend
            // on the SessionManager's cleanup mechanism
            Assert.IsNotNull(playerSession);
        }

        [Test]
        public async Task Concurrent_Session_Operations_Should_Be_Thread_Safe()
        {
            // Arrange
            const int sessionCount = 50;
            var sessions = new MockClientSession[sessionCount];
            var createTasks = new Task<IPlayerSession>[sessionCount];

            // Act - Create sessions concurrently
            for (int i = 0; i < sessionCount; i++)
            {
                sessions[i] = new MockClientSession(i + 1);
                createTasks[i] = _sessionManager.CreatePlayerSessionAsync(sessions[i]);
            }

            var playerSessions = await Task.WhenAll(createTasks);

            // Authenticate half of them concurrently
            var authTasks = new Task<bool>[sessionCount / 2];
            for (int i = 0; i < sessionCount / 2; i++)
            {
                var sessionId = i + 1;
                var accountId = 1000 + i;
                authTasks[i] = _sessionManager.AuthenticateSessionAsync(sessionId, accountId, $"Player{i}");
            }

            var authResults = await Task.WhenAll(authTasks);

            // Assert
            Assert.AreEqual(sessionCount, playerSessions.Length);
            Assert.IsTrue(authResults.All(r => r)); // All authentications should succeed

            var stats = _sessionManager.GetStatistics();
            Assert.AreEqual(sessionCount, stats.TotalSessions);
            Assert.AreEqual(sessionCount / 2, stats.AuthenticatedSessions);
            Assert.AreEqual(sessionCount / 2, stats.UnauthenticatedSessions);

            // Verify all sessions are accessible
            for (int i = 1; i <= sessionCount; i++)
            {
                var session = await _sessionManager.GetPlayerSessionAsync(i);
                Assert.IsNotNull(session, $"Session {i} should exist");
                Assert.AreEqual(i, session.SessionId);
            }
        }

        [Test]
        public async Task GetStatistics_Should_Return_Accurate_Counts()
        {
            // Arrange
            var session1 = new MockClientSession(1);
            var session2 = new MockClientSession(2);
            var session3 = new MockClientSession(3);

            // Act
            await _sessionManager.CreatePlayerSessionAsync(session1);
            await _sessionManager.CreatePlayerSessionAsync(session2);
            await _sessionManager.CreatePlayerSessionAsync(session3);

            await _sessionManager.AuthenticateSessionAsync(1, 100, "Player1");
            await _sessionManager.AuthenticateSessionAsync(2, 200, "Player2");
            // Leave session 3 unauthenticated

            // Assert
            var stats = _sessionManager.GetStatistics();
            Assert.AreEqual(3, stats.TotalSessions);
            Assert.AreEqual(2, stats.AuthenticatedSessions);
            Assert.AreEqual(1, stats.UnauthenticatedSessions);
            Assert.GreaterOrEqual(stats.AverageSessionDuration.TotalMilliseconds, 0);
        }
    }
}
