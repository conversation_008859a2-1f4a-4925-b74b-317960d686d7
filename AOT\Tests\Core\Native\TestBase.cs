using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NUnit.Framework;
using HeroYulgang.Core.Native.Integration;
using HeroYulgang.Core.Native.Services;
using HeroYulgang.Core.Native.Messaging;
using HeroYulgang.Core.Native.Network;
using HeroYulgang.Core.Native.Packets;
using HeroYulgang.Core.Native.Sessions;

namespace HeroYulgang.Tests.Core.Native
{
    /// <summary>
    /// Base class for native system tests
    /// </summary>
    [TestFixture]
    public abstract class TestBase
    {
        protected IServiceProvider ServiceProvider { get; private set; } = null!;
        protected IHost Host { get; private set; } = null!;
        protected CancellationTokenSource CancellationTokenSource { get; private set; } = null!;

        [SetUp]
        public virtual async Task SetUp()
        {
            CancellationTokenSource = new CancellationTokenSource();
            
            var builder = CreateHostBuilder();
            Host = builder.Build();
            ServiceProvider = Host.Services;

            // Start host services
            await Host.StartAsync(CancellationTokenSource.Token);
        }

        [TearDown]
        public virtual async Task TearDown()
        {
            try
            {
                if (Host != null)
                {
                    await Host.StopAsync(CancellationTokenSource.Token);
                    Host.Dispose();
                }
            }
            finally
            {
                CancellationTokenSource?.Dispose();
            }
        }

        /// <summary>
        /// Create host builder for testing
        /// </summary>
        protected virtual IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureLogging(logging =>
                {
                    logging.ClearProviders();
                    logging.AddConsole();
                    logging.SetMinimumLevel(LogLevel.Debug);
                })
                .ConfigureServices(ConfigureServices);
        }

        /// <summary>
        /// Configure services for testing
        /// </summary>
        protected virtual void ConfigureServices(IServiceCollection services)
        {
            services.AddNativeSystemBuilder()
                .ConfigureNetwork(config =>
                {
                    config.Port = GetAvailablePort();
                    config.MaxConnections = 10;
                    config.ReceiveBufferSize = 1024;
                    config.SendBufferSize = 1024;
                })
                .ConfigurePacketProcessing(config =>
                {
                    config.WorkerThreadCount = 1;
                    config.MaxQueueSize = 100;
                    config.BatchProcessingEnabled = false;
                    config.EnablePacketLogging = true;
                })
                .ConfigureSessions(config =>
                {
                    config.SessionTimeout = TimeSpan.FromMinutes(1);
                    config.AuthenticationTimeout = TimeSpan.FromSeconds(30);
                    config.MaxSessionsPerAccount = 1;
                    config.CleanupInterval = TimeSpan.FromSeconds(10);
                })
                .EnableDevelopmentMode()
                .Build();
        }

        /// <summary>
        /// Get available port for testing
        /// </summary>
        protected int GetAvailablePort()
        {
            var random = new Random();
            return random.Next(10000, 20000);
        }

        /// <summary>
        /// Create test packet
        /// </summary>
        protected Packet CreateTestPacket(PacketType type = PacketType.Login, int sessionId = 1)
        {
            var data = type switch
            {
                PacketType.Login => new byte[] { 0x01, 0x02, 0x03, 0x04 },
                PacketType.Chat => new byte[] { 0x05, 0x06, 0x07, 0x08 },
                PacketType.Movement => new byte[] { 0x09, 0x0A, 0x0B, 0x0C },
                PacketType.Heartbeat => new byte[] { 0x0D, 0x0E },
                _ => new byte[] { 0xFF }
            };

            return new Packet(type, data, sessionId);
        }

        /// <summary>
        /// Create test session
        /// </summary>
        protected async Task<IPlayerSession> CreateTestSessionAsync(int sessionId = 1)
        {
            var sessionManager = ServiceProvider.GetRequiredService<ISessionManager>();
            var mockNetworkSession = new MockClientSession(sessionId);
            
            return await sessionManager.CreatePlayerSessionAsync(mockNetworkSession);
        }

        /// <summary>
        /// Wait for condition with timeout
        /// </summary>
        protected async Task<bool> WaitForConditionAsync(Func<bool> condition, TimeSpan timeout)
        {
            var endTime = DateTime.UtcNow.Add(timeout);
            
            while (DateTime.UtcNow < endTime)
            {
                if (condition())
                    return true;
                    
                await Task.Delay(10);
            }
            
            return false;
        }

        /// <summary>
        /// Assert that task completes within timeout
        /// </summary>
        protected async Task AssertTaskCompletesAsync(Task task, TimeSpan timeout, string? message = null)
        {
            using var cts = new CancellationTokenSource(timeout);
            
            try
            {
                await task.WaitAsync(cts.Token);
            }
            catch (OperationCanceledException)
            {
                Assert.Fail(message ?? $"Task did not complete within {timeout}");
            }
        }

        /// <summary>
        /// Assert that condition becomes true within timeout
        /// </summary>
        protected async Task AssertConditionAsync(Func<bool> condition, TimeSpan timeout, string? message = null)
        {
            var result = await WaitForConditionAsync(condition, timeout);
            Assert.IsTrue(result, message ?? $"Condition was not met within {timeout}");
        }

        /// <summary>
        /// Assert that async condition becomes true within timeout
        /// </summary>
        protected async Task AssertConditionAsync(Func<Task<bool>> condition, TimeSpan timeout, string? message = null)
        {
            var endTime = DateTime.UtcNow.Add(timeout);
            
            while (DateTime.UtcNow < endTime)
            {
                if (await condition())
                    return;
                    
                await Task.Delay(10);
            }
            
            Assert.Fail(message ?? $"Async condition was not met within {timeout}");
        }
    }

    /// <summary>
    /// Mock client session for testing
    /// </summary>
    public class MockClientSession : IClientSession
    {
        public int SessionId { get; }
        public bool IsConnected { get; private set; } = true;
        public DateTime ConnectedAt { get; } = DateTime.UtcNow;
        public string RemoteEndPoint { get; } = "127.0.0.1:12345";

        private readonly List<byte[]> _sentData = new();
        private readonly TaskCompletionSource<bool> _disconnectedTcs = new();

        public MockClientSession(int sessionId)
        {
            SessionId = sessionId;
        }

        public async Task SendAsync(byte[] data)
        {
            if (!IsConnected)
                throw new InvalidOperationException("Session is disconnected");
                
            _sentData.Add(data);
            await Task.CompletedTask;
        }

        public async Task SendAsync(ReadOnlyMemory<byte> data)
        {
            await SendAsync(data.ToArray());
        }

        public async Task DisconnectAsync(string reason = "Test disconnect")
        {
            IsConnected = false;
            _disconnectedTcs.SetResult(true);
            await Task.CompletedTask;
        }

        public void Dispose()
        {
            IsConnected = false;
            _disconnectedTcs.TrySetResult(true);
        }

        // Test helpers
        public IReadOnlyList<byte[]> GetSentData() => _sentData.AsReadOnly();
        public void ClearSentData() => _sentData.Clear();
        public Task WaitForDisconnectAsync() => _disconnectedTcs.Task;
        
        public void SimulateDisconnect()
        {
            IsConnected = false;
            _disconnectedTcs.TrySetResult(true);
        }
    }

    /// <summary>
    /// Test packet handler for testing
    /// </summary>
    public class TestPacketHandler : IPacketHandler<Packet>
    {
        public List<(Packet Packet, IPacketContext Context)> HandledPackets { get; } = new();
        public TaskCompletionSource<bool> HandlerCalledTcs { get; } = new();
        public Exception? ExceptionToThrow { get; set; }
        public TimeSpan ProcessingDelay { get; set; } = TimeSpan.Zero;

        public async Task HandleAsync(Packet packet, IPacketContext context)
        {
            if (ExceptionToThrow != null)
                throw ExceptionToThrow;
                
            if (ProcessingDelay > TimeSpan.Zero)
                await Task.Delay(ProcessingDelay);
                
            HandledPackets.Add((packet, context));
            HandlerCalledTcs.TrySetResult(true);
        }

        public bool CanHandle(PacketType packetType) => true;

        public void Reset()
        {
            HandledPackets.Clear();
            HandlerCalledTcs.TrySetResult(false);
        }
    }

    /// <summary>
    /// Test event for testing event bus
    /// </summary>
    public class TestEvent
    {
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public int Value { get; set; }
    }

    /// <summary>
    /// Test service for testing service management
    /// </summary>
    public class TestService : ServiceBase
    {
        public bool StartCalled { get; private set; }
        public bool StopCalled { get; private set; }
        public bool RunningCalled { get; private set; }
        public Exception? ExceptionToThrow { get; set; }
        public TaskCompletionSource<bool> StartedTcs { get; } = new();
        public TaskCompletionSource<bool> StoppedTcs { get; } = new();

        public TestService() : base("TestService") { }

        protected override async Task OnStartingAsync(CancellationToken cancellationToken)
        {
            if (ExceptionToThrow != null)
                throw ExceptionToThrow;
                
            StartCalled = true;
            StartedTcs.TrySetResult(true);
            await base.OnStartingAsync(cancellationToken);
        }

        protected override async Task OnRunningAsync(CancellationToken cancellationToken)
        {
            RunningCalled = true;
            await Task.Delay(Timeout.Infinite, cancellationToken);
        }

        protected override async Task OnStoppingAsync(CancellationToken cancellationToken)
        {
            StopCalled = true;
            StoppedTcs.TrySetResult(true);
            await base.OnStoppingAsync(cancellationToken);
        }

        public void Reset()
        {
            StartCalled = false;
            StopCalled = false;
            RunningCalled = false;
            ExceptionToThrow = null;
        }
    }
}
