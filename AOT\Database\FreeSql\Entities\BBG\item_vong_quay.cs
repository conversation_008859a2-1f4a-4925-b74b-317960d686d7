﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.BBG {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class item_vong_quay {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_price { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_desc { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_type { get; set; }

		[JsonProperty]
		public int? fld_return { get; set; }

		[JsonProperty]
		public int? fld_number { get; set; }

		[JsonProperty]
		public int? fld_magic1 { get; set; }

		[JsonProperty]
		public int? fld_magic2 { get; set; }

		[JsonProperty]
		public int? fld_magic3 { get; set; }

		[JsonProperty]
		public int? fld_magic4 { get; set; }

		[JsonProperty]
		public int? fld_magic5 { get; set; }

		[JsonProperty]
		public int? fld_socapphuhon { get; set; }

		[JsonProperty]
		public int? fld_trungcapphuhon { get; set; }

		[JsonProperty]
		public int? fld_tienhoa { get; set; }

		[JsonProperty]
		public int? fld_phaichangkhoalai { get; set; }

		[JsonProperty]
		public int? fld_days { get; set; }

		[JsonProperty]
		public int? fld_percentage { get; set; }

		[JsonProperty]
		public int? show_hide { get; set; }

	}

}
