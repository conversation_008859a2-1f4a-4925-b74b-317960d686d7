

using static RxjhServer.Systems.AttackLimiterSystem;

namespace RxjhServer.Systems;

  public static class JobConfig
        {
            // Job 1: <PERSON><PERSON> (Dao)
            public static readonly AttackLimiterConfig Dao = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 2: <PERSON><PERSON><PERSON> (Kiem)
            public static readonly AttackLimiterConfig Kiem = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 3: <PERSON><PERSON><PERSON><PERSON><PERSON> (Thuong)
            public static readonly AttackLimiterConfig Thuong = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 4: Cung (Cung) - Special handling for TamThanNgungTu
            public static readonly AttackLimiterConfig Cung = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 5: <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)
            public static readonly AttackLimiterConfig Quyen = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 6: Ninja - Special handling for attack speed
            public static readonly AttackLimiterConfig Ninja = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 7: Cẩm Sư (CamSu)
            public static readonly AttackLimiterConfig CamSu = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 8: Hàn Bảo Quân (HanBaoQuan)
            public static readonly AttackLimiterConfig HanBaoQuan = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 9: Đàm Hoa Liên (DamHoaLien)
            public static readonly AttackLimiterConfig DamHoaLien = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 10: Quyền Sư (QuyenSu)
            public static readonly AttackLimiterConfig QuyenSu = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 11: Mai Liều Chân (MaiLieuChan)
            public static readonly AttackLimiterConfig MaiLieuChan = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 12: Tử Hao (TuHao)
            public static readonly AttackLimiterConfig TuHao = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            // Job 13: Thần Nữ (ThanNu)
            public static readonly AttackLimiterConfig ThanNu = new()
            {
                BaseCooldown = 1000,
                MaxBonusTime = 1500,
                MinDamagePercent = 10,
                MaxDamagePercent = 120
            };

            /// <summary>
            /// Get attack limiter config for specific job
            /// Lấy cấu hình giới hạn tấn công cho nghề cụ thể
            /// </summary>
            public static AttackLimiterConfig GetConfigForJob(int playerJob)
            {
                return playerJob switch
                {
                    1 => Dao,
                    2 => Kiem,
                    3 => Thuong,
                    4 => Cung,
                    5 => Quyen,
                    6 => Ninja,
                    7 => CamSu,
                    8 => HanBaoQuan,
                    9 => DamHoaLien,
                    10 => QuyenSu,
                    11 => MaiLieuChan,
                    12 => TuHao,
                    13 => ThanNu,
                    _ => Dao // Default fallback
                };
            }

            /// <summary>
            /// Update attack limiter config for specific job
            /// Cập nhật cấu hình giới hạn tấn công cho nghề cụ thể
            /// </summary>
            /// <param name="jobId">Job ID (1-13)</param>
            /// <param name="baseCooldown">Base cooldown in milliseconds</param>
            /// <param name="maxBonusTime">Max bonus time in milliseconds</param>
            /// <param name="minDamagePercent">Minimum damage percentage</param>
            /// <param name="maxDamagePercent">Maximum damage percentage</param>
            /// <returns>True if update successful, false otherwise</returns>
            public static bool UpdateJobConfig(int jobId, int baseCooldown, int maxBonusTime, int minDamagePercent, int maxDamagePercent)
            {
                // Validate parameters
                if (jobId < 1 || jobId > 13)
                    return false;

                if (baseCooldown < 100 || baseCooldown > 5000)
                    return false;

                if (maxBonusTime < baseCooldown || maxBonusTime > 10000)
                    return false;

                if (minDamagePercent < 1 || minDamagePercent > 100)
                    return false;

                if (maxDamagePercent < 100 || maxDamagePercent > 300)
                    return false;

                // Get the config object to update
                AttackLimiterConfig config = GetConfigForJob(jobId);

                // Update the config values
                config.BaseCooldown = baseCooldown;
                config.MaxBonusTime = maxBonusTime;
                config.MinDamagePercent = minDamagePercent;
                config.MaxDamagePercent = maxDamagePercent;

                return true;
            }

            /// <summary>
            /// Get job config information as string
            /// Lấy thông tin cấu hình nghề dưới dạng chuỗi
            /// </summary>
            /// <param name="jobId">Job ID (1-13)</param>
            /// <returns>Config information string</returns>
            public static string GetJobConfigInfo(int jobId)
            {
                if (jobId < 1 || jobId > 13)
                    return "Job ID không hợp lệ (1-13)";

                var config = GetConfigForJob(jobId);
                var jobName = GetJobName(jobId);

                return $"Job {jobId} ({jobName}):\n" +
                       $"- Base Cooldown: {config.BaseCooldown}ms\n" +
                       $"- Max Bonus Time: {config.MaxBonusTime}ms\n" +
                       $"- Min Damage: {config.MinDamagePercent}%\n" +
                       $"- Max Damage: {config.MaxDamagePercent}%";
            }

            /// <summary>
            /// Get job name by ID
            /// Lấy tên nghề theo ID
            /// </summary>
            /// <param name="jobId">Job ID</param>
            /// <returns>Job name</returns>
            public static string GetJobName(int jobId)
            {
                return jobId switch
                {
                    1 => "Đao",
                    2 => "Kiếm",
                    3 => "Thương",
                    4 => "Cung",
                    5 => "Quyền",
                    6 => "Ninja",
                    7 => "Cẩm Sư",
                    8 => "Hàn Bảo Quân",
                    9 => "Đàm Hoa Liên",
                    10 => "Quyền Sư",
                    11 => "Mai Liều Chân",
                    12 => "Tử Hao",
                    13 => "Thần Nữ",
                    _ => "Unknown"
                };
            }
        }