using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Services;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Network
{
    /// <summary>
    /// TCP Network Manager implementation - thay thế TcpManagerActor
    /// </summary>
    public class TcpNetworkManager : ServiceBase, INetworkManager
    {
        private readonly NetworkConfiguration _configuration;
        private readonly ConcurrentDictionary<int, ClientSession> _sessions;
        private readonly Channel<NetworkEvent> _eventChannel;
        private readonly ChannelWriter<NetworkEvent> _eventWriter;
        private readonly ChannelReader<NetworkEvent> _eventReader;
        private TcpListener? _tcpListener;
        private volatile bool _isListening;
        private int _nextSessionId = 1;
        private readonly ConnectionStatistics _statistics;
        private readonly Timer _cleanupTimer;

        public TcpNetworkManager(NetworkConfiguration configuration) : base("TcpNetworkManager")
        {
            _configuration = configuration;
            _sessions = new ConcurrentDictionary<int, ClientSession>();
            _statistics = new ConnectionStatistics { StartTime = DateTime.UtcNow };

            // Create event channel
            var channelOptions = new UnboundedChannelOptions
            {
                SingleReader = false,
                SingleWriter = false
            };
            _eventChannel = Channel.CreateUnbounded<NetworkEvent>(channelOptions);
            _eventWriter = _eventChannel.Writer;
            _eventReader = _eventChannel.Reader;

            // Cleanup timer
            _cleanupTimer = new Timer(CleanupInactiveSessions, null, 
                _configuration.CleanupInterval, _configuration.CleanupInterval);
        }

        public int SessionCount => _sessions.Count;
        public bool IsListening => _isListening;

        /// <summary>
        /// Start listening on specified port
        /// </summary>
        public async Task StartListeningAsync(int port, CancellationToken cancellationToken = default)
        {
            if (_isListening)
            {
                Logger.Instance.Warning("Network manager is already listening");
                return;
            }

            try
            {
                _tcpListener = new TcpListener(IPAddress.Any, port);
                _tcpListener.Start();
                _isListening = true;

                Logger.Instance.Info($"TCP Network Manager started listening on port {port}");

                // Start accepting connections
                _ = Task.Run(() => AcceptConnectionsAsync(cancellationToken), cancellationToken);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Failed to start listening on port {port}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Stop listening
        /// </summary>
        public async Task StopListeningAsync()
        {
            if (!_isListening)
                return;

            try
            {
                _isListening = false;
                _tcpListener?.Stop();

                // Close all sessions
                var closeTasks = _sessions.Values.Select(session => session.CloseAsync()).ToArray();
                await Task.WhenAll(closeTasks);

                Logger.Instance.Info("TCP Network Manager stopped listening");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error stopping network manager: {ex.Message}");
            }
        }

        /// <summary>
        /// Accept incoming connections
        /// </summary>
        private async Task AcceptConnectionsAsync(CancellationToken cancellationToken)
        {
            while (_isListening && !cancellationToken.IsCancellationRequested)
            {
                try
                {
                    if (_tcpListener == null)
                        break;

                    var tcpClient = await _tcpListener.AcceptTcpClientAsync();
                    
                    // Check connection limit
                    if (_sessions.Count >= _configuration.MaxConnections)
                    {
                        Logger.Instance.Warning($"Connection limit reached ({_configuration.MaxConnections}), rejecting connection");
                        tcpClient.Close();
                        continue;
                    }

                    // Create session
                    var sessionId = Interlocked.Increment(ref _nextSessionId);
                    var session = new ClientSession(sessionId, tcpClient, _configuration);
                    
                    _sessions[sessionId] = session;
                    _statistics.TotalConnections++;

                    // Start session processing
                    _ = Task.Run(() => ProcessSessionAsync(session, cancellationToken), cancellationToken);

                    // Publish event
                    await PublishEventAsync(new ClientConnectedEvent(session));

                    Logger.Instance.Info($"Client connected: {session.RemoteEndPoint} (SessionID: {sessionId})");
                }
                catch (ObjectDisposedException)
                {
                    // Expected when stopping
                    break;
                }
                catch (Exception ex)
                {
                    if (_isListening)
                    {
                        Logger.Instance.Error($"Error accepting connection: {ex.Message}");
                        await PublishEventAsync(new NetworkErrorEvent(ex));
                    }
                }
            }
        }

        /// <summary>
        /// Process session data
        /// </summary>
        private async Task ProcessSessionAsync(ClientSession session, CancellationToken cancellationToken)
        {
            try
            {
                await foreach (var data in session.GetDataAsync(cancellationToken))
                {
                    _statistics.PacketsReceived++;
                    _statistics.BytesReceived += data.Length;

                    // Publish data received event
                    await PublishEventAsync(new DataReceivedEvent(session.SessionId, data));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error processing session {session.SessionId}: {ex.Message}");
                await PublishEventAsync(new NetworkErrorEvent(ex, session.SessionId));
            }
            finally
            {
                // Remove session
                _sessions.TryRemove(session.SessionId, out _);
                await PublishEventAsync(new ClientDisconnectedEvent(session.SessionId, "Session ended"));
                
                Logger.Instance.Info($"Session {session.SessionId} ended");
            }
        }

        /// <summary>
        /// Get session by ID
        /// </summary>
        public IClientSession? GetSession(int sessionId)
        {
            return _sessions.TryGetValue(sessionId, out var session) ? session : null;
        }

        /// <summary>
        /// Get all active sessions
        /// </summary>
        public IEnumerable<IClientSession> GetActiveSessions()
        {
            return _sessions.Values.Where(s => s.IsActive).ToArray();
        }

        /// <summary>
        /// Close session
        /// </summary>
        public async Task CloseSessionAsync(int sessionId)
        {
            if (_sessions.TryGetValue(sessionId, out var session))
            {
                await session.CloseAsync();
            }
        }

        /// <summary>
        /// Broadcast data to all sessions
        /// </summary>
        public async Task BroadcastAsync(byte[] data, Predicate<IClientSession>? filter = null)
        {
            var sessions = GetActiveSessions();
            if (filter != null)
            {
                sessions = sessions.Where(s => filter(s));
            }

            var tasks = sessions.Select(session => session.SendAsync(data)).ToArray();
            await Task.WhenAll(tasks);

            _statistics.PacketsSent += tasks.Length;
            _statistics.BytesSent += data.Length * tasks.Length;
        }

        /// <summary>
        /// Get network events
        /// </summary>
        public IAsyncEnumerable<NetworkEvent> GetEventsAsync()
        {
            return _eventReader.ReadAllAsync();
        }

        /// <summary>
        /// Publish network event
        /// </summary>
        private async Task PublishEventAsync(NetworkEvent networkEvent)
        {
            try
            {
                await _eventWriter.WriteAsync(networkEvent);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error publishing network event: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleanup inactive sessions
        /// </summary>
        private async void CleanupInactiveSessions(object? state)
        {
            try
            {
                var now = DateTime.UtcNow;
                var inactiveSessions = _sessions.Values
                    .Where(s => !s.IsActive || (now - s.LastActivity) > _configuration.ConnectionTimeout)
                    .ToArray();

                foreach (var session in inactiveSessions)
                {
                    await session.CloseAsync();
                    Logger.Instance.Debug($"Cleaned up inactive session {session.SessionId}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error in session cleanup: {ex.Message}");
            }
        }

        protected override async Task OnRunningAsync(CancellationToken cancellationToken)
        {
            // Start listening on configured port
            await StartListeningAsync(_configuration.Port, cancellationToken);

            // Keep running until cancelled
            try
            {
                await Task.Delay(Timeout.Infinite, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when stopping
            }
        }

        protected override async Task OnStoppingAsync(CancellationToken cancellationToken)
        {
            await StopListeningAsync();
            _cleanupTimer?.Dispose();
            _eventWriter.Complete();
            await base.OnStoppingAsync(cancellationToken);
        }

        protected override Task<bool> OnHealthCheckAsync()
        {
            return Task.FromResult(_isListening && _tcpListener != null);
        }

        public override void Dispose()
        {
            _cleanupTimer?.Dispose();
            _tcpListener?.Stop();
            _eventWriter.Complete();
            
            // Close all sessions
            foreach (var session in _sessions.Values)
            {
                session.Dispose();
            }
            
            base.Dispose();
        }
    }
}
