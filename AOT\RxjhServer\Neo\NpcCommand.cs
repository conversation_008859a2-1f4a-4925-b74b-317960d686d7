using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using HeroYulgang.Utils;
using RxjhServer.AOI;

namespace RxjhServer
{
    /// <summary>
    /// Lớ<PERSON> chứa các command để quản lý và kiểm tra NPC
    /// </summary>
    public partial class Players
    {
        /// <summary>
        /// Xử lý các command liên quan đến NPC
        /// </summary>
        /// <param name="command">Command đầy đủ</param>
        /// <param name="args"><PERSON><PERSON><PERSON> tham số</param>
        /// <returns>True nếu command được xử lý thành công</returns>
        public bool HandleNpcCommand(string command, string[] args)
        {
            try
            {
                switch (args[0].ToLower())
                {
                    case "!npcinfo":
                        return HandleNpcInfo(args);
                    
                    case "!npclist":
                        return HandleNpcList(args);
                    
                    case "!npcnear":
                        return HandleNpcNear(args);
                    
                    case "!npckill":
                        return HandleNpcKill(args);
                    
                    case "!npcremove":
                        return HandleNpcRemove(args);
                    
                    case "!npchide":
                        return HandleNpcHide(args);
                    
                    case "!npcshow":
                        return HandleNpcShow(args);
                    
                    case "!npcrevive":
                        return HandleNpcRevive(args);
                    
                    case "!npcteleport":
                        return HandleNpcTeleport(args);
                    
                    case "!npcstatus":
                        return HandleNpcStatus(args);
                    
                    case "!npcaoi":
                        return HandleNpcAOI(args);

                    case "!npchelp":
                        return HandleNpcHelp();

                    default:
                        HeThongNhacNho("Command NPC không hợp lệ! Sử dụng !npchelp để xem danh sách command.", 7, "NPC");
                        return false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi xử lý NPC command: {ex.Message}");
                HeThongNhacNho("Có lỗi xảy ra khi xử lý command NPC!", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Hiển thị thông tin chi tiết của một NPC
        /// Sử dụng: !npcinfo [sessionID]
        /// </summary>
        private bool HandleNpcInfo(string[] args)
        {
            if (args.Length < 2)
            {
                HeThongNhacNho("Sử dụng: !npcinfo [sessionID]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[1], out int sessionID))
            {
                HeThongNhacNho("Session ID phải là số!", 7, "NPC");
                return false;
            }

            // Tìm NPC trong danh sách NPC của player
            if (NpcList.TryGetValue(sessionID, out NpcClass npc))
            {
                var info = new StringBuilder();
                info.AppendLine($"Thông tin NPC:");
                info.AppendLine($"ID: {npc.FLD_PID}");
                info.AppendLine($"Session ID: {npc.NPC_SessionID}");
                info.AppendLine($"Tên: {npc.Name}");
                info.AppendLine($"Level: {npc.Level}");
                info.AppendLine($"HP: {npc.Rxjh_HP}/{npc.Max_Rxjh_HP}");
                info.AppendLine($"Vị trí: ({npc.Rxjh_X:F1}, {npc.Rxjh_Y:F1}, {npc.Rxjh_Z:F1})");
                info.AppendLine($"Map: {npc.Rxjh_Map}");
                info.AppendLine($"Trạng thái: {(npc.NPCDeath ? "Đã chết" : "Còn sống")}");
                info.AppendLine($"Loại: {(npc.IsNpc == 1 ? "NPC" : "Monster")}");
                info.AppendLine($"Boss: {(npc.FLD_BOSS == 1 ? "Có" : "Không")}");
                
                if (npc.NPCDeath && npc.timeNpcRevival != DateTime.MinValue)
                {
                    var timeLeft = npc.timeNpcRevival - DateTime.Now;
                    if (timeLeft.TotalSeconds > 0)
                    {
                        info.AppendLine($"• Hồi sinh sau: {timeLeft.TotalSeconds:F0} giây");
                    }
                }

                HeThongNhacNho(info.ToString(), 7, "NPC Info");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {sessionID}", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Hiển thị danh sách tất cả NPC gần nhân vật
        /// Sử dụng: !npclist [range] (mặc định 500)
        /// </summary>
        private bool HandleNpcList(string[] args)
        {
            int range = 500;
            if (args.Length >= 2 && int.TryParse(args[1], out int customRange))
            {
                range = customRange;
            }

            var nearbyNpcs = GetNpcsInRangeZone(range);
            
            if (nearbyNpcs.Count == 0)
            {
                HeThongNhacNho($"Không có NPC nào trong phạm vi {range} đơn vị!", 7, "NPC");
                return true;
            }

            var list = new StringBuilder();
            list.AppendLine($"📋 Danh sách NPC trong phạm vi {range}:");
            list.AppendLine($"Tổng cộng: {nearbyNpcs.Count} NPC");
            list.AppendLine("─────────────────────────");

            foreach (var npc in nearbyNpcs.Take(10)) // Giới hạn 10 NPC để tránh spam
            {
                var distance = Math.Sqrt(Math.Pow(npc.Rxjh_X - PosX, 2) + Math.Pow(npc.Rxjh_Y - PosY, 2));
                var status = npc.NPCDeath ? "DEAD" : "ALIVE";
                var type = npc.IsNpc == 1 ? "NPC" : "MON";
                var boss = npc.FLD_BOSS == 1 ? "BOSS" : "";
                
                list.AppendLine($"{status} [{npc.NPC_SessionID}] {npc.Name} ({type}){boss}");
                list.AppendLine($"Khoảng cách: {distance:F1} | HP: {npc.Rxjh_HP}/{npc.Max_Rxjh_HP}");
            }

            if (nearbyNpcs.Count > 10)
            {
                list.AppendLine($"... và {nearbyNpcs.Count - 10} NPC khác");
            }

            HeThongNhacNho(list.ToString(), 7, "NPC List");
            return true;
        }

        /// <summary>
        /// Tìm NPC gần nhất
        /// Sử dụng: !npcnear [range] (mặc định 200)
        /// </summary>
        private bool HandleNpcNear(string[] args)
        {
            int range = 200;
            if (args.Length >= 2 && int.TryParse(args[1], out int customRange))
            {
                range = customRange;
            }

            var nearbyNpcs = GetNpcsInRangeZone(range);
            
            if (nearbyNpcs.Count == 0)
            {
                HeThongNhacNho($"Không có NPC nào trong phạm vi {range} đơn vị!", 7, "NPC");
                return true;
            }

            // Tìm NPC gần nhất
            NpcClass nearestNpc = null;
            double minDistance = double.MaxValue;

            foreach (var npc in nearbyNpcs)
            {
                var distance = Math.Sqrt(Math.Pow(npc.Rxjh_X - PosX, 2) + Math.Pow(npc.Rxjh_Y - PosY, 2));
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestNpc = npc;
                }
            }

            if (nearestNpc != null)
            {
                var status = nearestNpc.NPCDeath ? "Đã chết" : "Còn sống";
                var type = nearestNpc.IsNpc == 1 ? "NPC" : "Monster";
                var boss = nearestNpc.FLD_BOSS == 1 ? "BOSS" : "";
                
                HeThongNhacNho($"NPC gần nhất:\n" +
                              $"[{nearestNpc.NPC_SessionID}] {nearestNpc.Name} ({type}){boss}\n" +
                              $"Khoảng cách: {minDistance:F1}\n" +
                              $"Trạng thái: {status}\n" +
                              $"HP: {nearestNpc.Rxjh_HP}/{nearestNpc.Max_Rxjh_HP}\n" +
                              $"Vị trí: ({nearestNpc.Rxjh_X:F1}, {nearestNpc.Rxjh_Y:F1})", 7, "NPC Near");
                return true;
            }

            return false;
        }

        /// <summary>
        /// Giết một NPC
        /// Sử dụng: !npckill [sessionID]
        /// </summary>
        private bool HandleNpcKill(string[] args)
        {
            if (GMMode < 5)
            {
                HeThongNhacNho("Bạn không có quyền sử dụng lệnh này!", 7, "NPC");
                return false;
            }

            if (args.Length < 2)
            {
                HeThongNhacNho("Sử dụng: !npckill [sessionID]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[1], out int sessionID))
            {
                HeThongNhacNho("Session ID phải là số!", 7, "NPC");
                return false;
            }

            if (NpcList.TryGetValue(sessionID, out NpcClass npc))
            {
                if (npc.NPCDeath)
                {
                    HeThongNhacNho($"NPC {npc.Name} đã chết rồi!", 7, "NPC");
                    return false;
                }

                npc.Rxjh_HP = 0;
                npc.LamMoiTuVongSoLieu();

                HeThongNhacNho($"Đã giết NPC: [{sessionID}] {npc.Name}", 7, "NPC Kill");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {sessionID}", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Xóa một NPC khỏi game
        /// Sử dụng: !npcremove [sessionID]
        /// </summary>
        private bool HandleNpcRemove(string[] args)
        {
            if (GMMode < 8)
            {
                HeThongNhacNho("Bạn không có quyền sử dụng lệnh này!", 7, "NPC");
                return false;
            }

            if (args.Length < 2)
            {
                HeThongNhacNho("Sử dụng: !npcremove [sessionID]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[1], out int sessionID))
            {
                HeThongNhacNho("Session ID phải là số!", 7, "NPC");
                return false;
            }

            if (NpcList.TryGetValue(sessionID, out NpcClass npc))
            {
                // Xóa khỏi AOI system
                AOIManager.Instance.RemoveNPC(npc.NPC_SessionID);

                // Gửi packet despawn cho tất cả player
                var despawnDict = new Dictionary<int, NpcClass> { { npc.NPC_SessionID, npc } };
                NpcClass.UpdateNPC_Despawn(despawnDict, this);

                // Xóa khỏi danh sách
                NpcList.Remove(sessionID);

                // Dispose NPC
                npc.Dispose();

                HeThongNhacNho($"Đã xóa NPC: [{sessionID}] {npc.Name}", 7, "NPC Remove");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {sessionID}", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Ẩn một NPC (không xóa, chỉ ẩn khỏi client)
        /// Sử dụng: !npchide [sessionID]
        /// </summary>
        private bool HandleNpcHide(string[] args)
        {
            if (GMMode < 5)
            {
                HeThongNhacNho("Bạn không có quyền sử dụng lệnh này!", 7, "NPC");
                return false;
            }

            if (args.Length < 2)
            {
                HeThongNhacNho("Sử dụng: !npchide [sessionID]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[1], out int sessionID))
            {
                HeThongNhacNho("Session ID phải là số!", 7, "NPC");
                return false;
            }

            if (NpcList.TryGetValue(sessionID, out NpcClass npc))
            {
                // Gửi packet để ẩn NPC
                var packet = new SendingClass();
                Client.SendPak(packet, 34816, npc.NPC_SessionID);

                HeThongNhacNho($"Đã ẩn NPC: [{sessionID}] {npc.Name}", 7, "NPC Hide");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {sessionID}", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Hiển thị lại một NPC đã bị ẩn
        /// Sử dụng: !npcshow [sessionID]
        /// </summary>
        private bool HandleNpcShow(string[] args)
        {
            if (GMMode < 5)
            {
                HeThongNhacNho("Bạn không có quyền sử dụng lệnh này!", 7, "NPC");
                return false;
            }

            if (args.Length < 2)
            {
                HeThongNhacNho("Sử dụng: !npcshow [sessionID]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[1], out int sessionID))
            {
                HeThongNhacNho("Session ID phải là số!", 7, "NPC");
                return false;
            }

            if (NpcList.TryGetValue(sessionID, out NpcClass npc))
            {
                // Cập nhật thông tin NPC để hiển thị lại
                npc.UpdateNPCSoLieu(this);

                HeThongNhacNho($"Đã hiển thị lại NPC: [{sessionID}] {npc.Name}", 7, "NPC Show");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {sessionID}", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Hồi sinh một NPC đã chết
        /// Sử dụng: !npcrevive [sessionID]
        /// </summary>
        private bool HandleNpcRevive(string[] args)
        {
            if (GMMode < 5)
            {
                HeThongNhacNho("Bạn không có quyền sử dụng lệnh này!", 7, "NPC");
                return false;
            }

            if (args.Length < 2)
            {
                HeThongNhacNho("Sử dụng: !npcrevive [sessionID]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[1], out int sessionID))
            {
                HeThongNhacNho("Session ID phải là số!", 7, "NPC");
                return false;
            }

            if (NpcList.TryGetValue(sessionID, out NpcClass npc))
            {
                if (!npc.NPCDeath)
                {
                    HeThongNhacNho($"NPC {npc.Name} vẫn còn sống!", 7, "NPC");
                    return false;
                }

                // Hồi sinh NPC
                npc.RefreshSpawnData();

                HeThongNhacNho($"Đã hồi sinh NPC: [{sessionID}] {npc.Name}", 7, "NPC Revive");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {sessionID}", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Dịch chuyển đến vị trí của một NPC
        /// Sử dụng: !npcteleport [sessionID]
        /// </summary>
        private bool HandleNpcTeleport(string[] args)
        {
            if (GMMode < 5)
            {
                HeThongNhacNho("Bạn không có quyền sử dụng lệnh này!", 7, "NPC");
                return false;
            }

            if (args.Length < 2)
            {
                HeThongNhacNho("Sử dụng: !npcteleport [sessionID]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[1], out int sessionID))
            {
                HeThongNhacNho("Session ID phải là số!", 7, "NPC");
                return false;
            }

            if (NpcList.TryGetValue(sessionID, out NpcClass npc))
            {
                // Dịch chuyển player đến vị trí NPC
                PosX = npc.Rxjh_X;
                PosY = npc.Rxjh_Y;
                PosZ = npc.Rxjh_Z;
                MapID = npc.Rxjh_Map;

                // Cập nhật vị trí
               // UpdatePosition();

                HeThongNhacNho($"Đã dịch chuyển đến NPC: [{sessionID}] {npc.Name}\n" +
                              $"Vị trí: ({npc.Rxjh_X:F1}, {npc.Rxjh_Y:F1}) - Map {npc.Rxjh_Map}", 7, "NPC Teleport");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {sessionID}", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Hiển thị trạng thái chi tiết của NPC
        /// Sử dụng: !npcstatus [sessionID]
        /// </summary>
        private bool HandleNpcStatus(string[] args)
        {
            if (args.Length < 2)
            {
                HeThongNhacNho("Sử dụng: !npcstatus [sessionID]", 7, "NPC");
                return false;
            }

            if (!int.TryParse(args[1], out int sessionID))
            {
                HeThongNhacNho("Session ID phải là số!", 7, "NPC");
                return false;
            }

            if (NpcList.TryGetValue(sessionID, out NpcClass npc))
            {
                var status = new StringBuilder();
                status.AppendLine($"Trạng thái chi tiết NPC [{sessionID}]:");
                status.AppendLine($"Tên: {npc.Name} (ID: {npc.FLD_PID})");
                status.AppendLine($"Trạng thái: {(npc.NPCDeath ? "Đã chết" : "Còn sống")}");
                status.AppendLine($"HP: {npc.Rxjh_HP}/{npc.Max_Rxjh_HP} ({(double)npc.Rxjh_HP / npc.Max_Rxjh_HP * 100:F1}%)");
                status.AppendLine($"Level: {npc.Level}");
                status.AppendLine($"Vị trí: ({npc.Rxjh_X:F1}, {npc.Rxjh_Y:F1}, {npc.Rxjh_Z:F1})");
                status.AppendLine($"Map: {npc.Rxjh_Map}");
                status.AppendLine($"Hướng: ({npc.FLD_FACE1:F2}, {npc.FLD_FACE2:F2})");
                status.AppendLine($"Loại: {(npc.IsNpc == 1 ? "NPC" : "Monster")}");
                status.AppendLine($"Boss: {(npc.FLD_BOSS == 1 ? "Có" : "Không")}");

                if (npc.NPCDeath)
                {
                    if (npc.timeNpcRevival != DateTime.MinValue)
                    {
                        var timeLeft = npc.timeNpcRevival - DateTime.Now;
                        if (timeLeft.TotalSeconds > 0)
                        {
                            status.AppendLine($"• Hồi sinh sau: {timeLeft.TotalSeconds:F0} giây");
                        }
                        else
                        {
                            status.AppendLine($"• Trạng thái: Sẵn sàng hồi sinh");
                        }
                    }
                }

                // Thông tin về player target
                if (npc.PlayerTargetList?.Count > 0)
                {
                    status.AppendLine($"• Đang target: {npc.PlayerTargetList.Count} player");
                }

                HeThongNhacNho(status.ToString(), 7, "NPC Status");
                return true;
            }
            else
            {
                HeThongNhacNho($"Không tìm thấy NPC với Session ID: {sessionID}", 7, "NPC");
                return false;
            }
        }

        /// <summary>
        /// Hiển thị thông tin AOI của NPC
        /// Sử dụng: !npcaoi [range] (mặc định 500)
        /// </summary>
        private bool HandleNpcAOI(string[] args)
        {
            int range = 500;
            if (args.Length >= 2 && int.TryParse(args[1], out int customRange))
            {
                range = customRange;
            }

            var aoiInfo = new StringBuilder();
            aoiInfo.AppendLine($"Thông tin AOI System:");
            aoiInfo.AppendLine($"Map hiện tại: {MapID}");
            aoiInfo.AppendLine($"Vị trí player: ({PosX:F1}, {PosY:F1})");
            aoiInfo.AppendLine($"Phạm vi quét: {range}");

            // Kiểm tra xem map có sử dụng AOI không
            bool useAOI = AOIConfiguration.Instance.ShouldUseAOI(MapID);
            aoiInfo.AppendLine($"AOI System: {(useAOI ? "Đang sử dụng" : "Không sử dụng")}");

            if (useAOI)
            {
                // Lấy thông tin AOI grids
                var aoiGrids = AOIManager.Instance.GetAOIGrids(PosX, PosY, MapID);
                aoiInfo.AppendLine($"Số grid AOI: {aoiGrids.Count}");

                int totalNpcs = 0;
                int aliveNpcs = 0;
                int deadNpcs = 0;

                // foreach (var grid in aoiGrids)
                // {
                //     totalNpcs = grid.NPCs.Count;
                //     foreach (var npc in grid.NPCs.Values)
                //     {
                //         if (npc.NPCDeath)
                //             deadNpcs++;
                //         else
                //             aliveNpcs++;
                //     }
                // }

                aoiInfo.AppendLine($"• Tổng NPC trong AOI: {totalNpcs}");
                aoiInfo.AppendLine($"  - Còn sống: {aliveNpcs} ");
                aoiInfo.AppendLine($"  - Đã chết: {deadNpcs} ");
            }
            else
            {
                // Sử dụng hệ thống cũ
                var nearbyNpcs = GetNpcsInRangeZone(range);
                int aliveCount = nearbyNpcs.Count(n => !n.NPCDeath);
                int deadCount = nearbyNpcs.Count(n => n.NPCDeath);

                aoiInfo.AppendLine($"Tổng NPC gần: {nearbyNpcs.Count}");
                aoiInfo.AppendLine($"Còn sống: {aliveCount} ");
                aoiInfo.AppendLine($"Đã chết: {deadCount} ");
            }

            HeThongNhacNho(aoiInfo.ToString(), 7, "NPC AOI");
            return true;
        }

        /// <summary>
        /// Hiển thị hướng dẫn sử dụng các command NPC
        /// Sử dụng: !npchelp
        /// </summary>
        private bool HandleNpcHelp()
        {
            var help = new StringBuilder();
            help.AppendLine("Hướng dẫn sử dụng NPC Commands:");
            help.AppendLine("─────────────────────────────────");
            help.AppendLine("Thông tin & Tìm kiếm:");
            help.AppendLine("!npcinfo [sessionID] - Xem thông tin chi tiết NPC");
            help.AppendLine("!npclist [range] - Danh sách NPC gần (mặc định 500)");
            help.AppendLine("!npcnear [range] - Tìm NPC gần nhất (mặc định 200)");
            help.AppendLine("!npcstatus [sessionID] - Trạng thái chi tiết NPC");
            help.AppendLine("!npcaoi [range] - Thông tin AOI system");
            help.AppendLine("");
            help.AppendLine("Quản lý NPC (cần quyền GM):");
            help.AppendLine("!npckill [sessionID] - Giết NPC (GM 5+)");
            help.AppendLine("!npcrevive [sessionID] - Hồi sinh NPC (GM 5+)");
            help.AppendLine("!npchide [sessionID] - Ẩn NPC (GM 5+)");
            help.AppendLine("!npcshow [sessionID] - Hiện NPC (GM 5+)");
            help.AppendLine("!npcremove [sessionID] - Xóa NPC (GM 8+)");
            help.AppendLine("!npcteleport [sessionID] - Dịch chuyển đến NPC (GM 5+)");
            help.AppendLine("");
            help.AppendLine("Mẹo: Sử dụng !npcnear để tìm sessionID của NPC gần nhất");

            HeThongNhacNho(help.ToString(), 7, "NPC Help");
            return true;
        }
    }
}
