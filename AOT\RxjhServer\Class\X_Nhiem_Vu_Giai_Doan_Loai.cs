using System.Collections.Generic;

namespace RxjhServer;

public class X_Nhiem_Vu_Gia<PERSON>_<PERSON><PERSON>_<PERSON>ai
{
	private string string_0 = string.Empty;

	private string string_1 = string.Empty;

	private int int_0 = -1;

	public Dictionary<int, X_Gia<PERSON>_<PERSON>an_Can_Vat_Pham_Loai> GiaiDoanCanVatPham_ = new();

	public Dictionary<int, X_Giai_Doan_<PERSON>_<PERSON>huong_Vat_Pham_Loai> GiaiDoanPhanThuongVatPham_ = new();

	private string string_2 = string.Empty;

	public string NhiemVuDieuKienPhuHopNhacNho2 = string.Empty;

	public string NhiemVuDieuKienPhuHopNhacNho3 = string.Empty;

	public string NhiemVuDieuKienPhuHopNhacNho4 = string.Empty;

	public string NhiemVuDieuKienPhuHopNhacNho5 = string.Empty;

	private string string_3 = string.Empty;

	public string NhiemVuDieuKienKhongPhuHopNhacNho2 = string.Empty;

	public string NhiemVuDieu<PERSON>ien<PERSON>hongPhuHopNhacNho3 = string.Empty;

	public string NhiemVuDieuKienKhongPhuHopNhacNho4 = string.Empty;

	public string NhiemVuDieuKienKhongPhuHopNhacNho5 = string.Empty;

	private int int_1;

	private int int_2;

	private int int_3;

	private int int_4;

	private int int_5;

	private int int_6;

	public int Npc_UnKnow1;

	public string NPCNAME
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	public string NhiemVu_GiaiDoanNoiDung
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public int GiaiDoanID
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public int GiaiDoan_TrangThai
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int MucDo_KhoKhan
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int NpcID
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int NpcMAP_ID
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int ToaDo_NPCX
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int ToaDo_NPCY
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public string NhiemVuDieuKienPhuHopNhacNho1
	{
		get
		{
			return string_2;
		}
		set
		{
			string_2 = value;
		}
	}

	public string NhiemVuDieuKienKhongPhuHopNhacNho1
	{
		get
		{
			return string_3;
		}
		set
		{
			string_3 = value;
		}
	}

	public X_Nhiem_Vu_Giai_Doan_Loai GetRWJD(int int_7, int int_8)
	{
		try
		{
			foreach (var value in World.NhiemVulist.Values)
			{
				if (value.RwID == int_7)
				{
					return value.NhiemVu_GiaiDoan[int_8];
				}
			}
			return null;
		}
		catch
		{
			return null;
		}
	}
}
