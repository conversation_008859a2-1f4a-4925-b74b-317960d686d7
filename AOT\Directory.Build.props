<Project>
  <!-- Global properties for all projects -->
  <PropertyGroup>
    <!-- Hot Reload settings for Debug builds -->
    <EnableHotReload Condition="'$(Configuration)' == 'Debug'">true</EnableHotReload>
    <UseSharedCompilation Condition="'$(Configuration)' == 'Debug'">true</UseSharedCompilation>
    
    <!-- Disable problematic features in Debug for Hot Reload -->
    <PublishAot Condition="'$(Configuration)' == 'Debug'">false</PublishAot>
    <TrimMode Condition="'$(Configuration)' == 'Debug'">none</TrimMode>
    
    <!-- Performance optimizations for Release -->
    <PublishAot Condition="'$(Configuration)' == 'Release'">true</PublishAot>
    <TrimMode Condition="'$(Configuration)' == 'Release'">link</TrimMode>
  </PropertyGroup>
</Project>
