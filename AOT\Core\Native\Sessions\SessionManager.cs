using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Services;
using HeroYulgang.Core.Native.Network;
using HeroYulgang.Core.Native.Packets;
using HeroYulgang.Core.Native.Messaging;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Sessions
{
    /// <summary>
    /// Session manager implementation - thay thế ClientActor management
    /// </summary>
    public class SessionManager : ServiceBase, ISessionManager
    {
        private readonly SessionConfiguration _configuration;
        private readonly IPlayerSessionFactory _sessionFactory;
        private readonly IEventBus _eventBus;
        private readonly ConcurrentDictionary<int, IPlayerSession> _sessions;
        private readonly ConcurrentDictionary<int, int> _accountToSession;
        private readonly Channel<SessionManagerEvent> _eventChannel;
        private readonly ChannelWriter<SessionManagerEvent> _eventWriter;
        private readonly Timer _cleanupTimer;
        private readonly SessionStatistics _statistics;

        public SessionManager(
            SessionConfiguration configuration,
            IPlayerSessionFactory sessionFactory,
            IEventBus eventBus) : base("SessionManager")
        {
            _configuration = configuration;
            _sessionFactory = sessionFactory;
            _eventBus = eventBus;
            _sessions = new ConcurrentDictionary<int, IPlayerSession>();
            _accountToSession = new ConcurrentDictionary<int, int>();
            _statistics = new SessionStatistics { StartTime = DateTime.UtcNow };

            // Create event channel
            var eventOptions = new UnboundedChannelOptions
            {
                SingleReader = false,
                SingleWriter = false,
                AllowSynchronousContinuations = false
            };
            _eventChannel = Channel.CreateUnbounded<SessionManagerEvent>(eventOptions);
            _eventWriter = _eventChannel.Writer;

            // Setup cleanup timer
            _cleanupTimer = new Timer(CleanupSessions, null, _configuration.CleanupInterval, _configuration.CleanupInterval);

            Logger.Instance.Info($"SessionManager created with configuration: MaxSessions={_configuration.MaxSessionsPerAccount}, Timeout={_configuration.SessionTimeout}");
        }

        public int SessionCount => _sessions.Count;

        /// <summary>
        /// Create player session from network session
        /// </summary>
        public async Task<IPlayerSession> CreatePlayerSessionAsync(IClientSession networkSession)
        {
            try
            {
                var packetContext = PacketContextFactory.Create(networkSession);
                var playerSession = _sessionFactory.CreatePlayerSession(networkSession, packetContext);

                if (!_sessions.TryAdd(playerSession.SessionId, playerSession))
                {
                    throw new InvalidOperationException($"Session {playerSession.SessionId} already exists");
                }

                // Update statistics
                _statistics.TotalSessions++;
                _statistics.ActiveSessions = _sessions.Count;

                // Publish event
                var sessionEvent = new PlayerSessionCreatedEvent(playerSession.SessionId, playerSession.RemoteEndPoint);
                await PublishEventAsync(sessionEvent);

                Logger.Instance.Info($"Created player session {playerSession.SessionId} from {playerSession.RemoteEndPoint}");
                return playerSession;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error creating player session: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get player session by ID
        /// </summary>
        public async Task<IPlayerSession?> GetPlayerSessionAsync(int sessionId)
        {
            return await Task.FromResult(_sessions.TryGetValue(sessionId, out var session) ? session : null);
        }

        /// <summary>
        /// Get player session by account ID
        /// </summary>
        public async Task<IPlayerSession?> GetPlayerSessionByAccountAsync(int accountId)
        {
            if (_accountToSession.TryGetValue(accountId, out var sessionId))
            {
                return await GetPlayerSessionAsync(sessionId);
            }
            return null;
        }

        /// <summary>
        /// Get all active player sessions
        /// </summary>
        public async Task<IEnumerable<IPlayerSession>> GetActiveSessionsAsync()
        {
            return await Task.FromResult(_sessions.Values.Where(s => s.IsConnected).ToList());
        }

        /// <summary>
        /// Close player session
        /// </summary>
        public async Task ClosePlayerSessionAsync(int sessionId, string reason = "Session closed")
        {
            try
            {
                if (!_sessions.TryRemove(sessionId, out var session))
                {
                    Logger.Instance.Warning($"Session {sessionId} not found for closing");
                    return;
                }

                var duration = session is PlayerSession ps ? ps.GetDuration() : TimeSpan.Zero;
                var accountId = session.AccountId;
                var characterName = session.CharacterName;

                // Remove from account mapping
                if (accountId.HasValue)
                {
                    _accountToSession.TryRemove(accountId.Value, out _);
                }

                // Close the session
                await session.CloseAsync(reason);

                // Update statistics
                _statistics.ActiveSessions = _sessions.Count;
                if (session.IsAuthenticated && accountId.HasValue && !string.IsNullOrEmpty(characterName))
                {
                    _statistics.AuthenticatedSessions = Math.Max(0, _statistics.AuthenticatedSessions - 1);
                }

                // Publish events
                var sessionEvent = new PlayerSessionClosedEvent(sessionId, reason, duration);
                await PublishEventAsync(sessionEvent);

                if (session.IsAuthenticated && accountId.HasValue && !string.IsNullOrEmpty(characterName))
                {
                    var disconnectEvent = new PlayerDisconnectedEvent(sessionId, accountId.Value, characterName, reason);
                    await PublishEventAsync(disconnectEvent);
                }

                // Dispose if disposable
                if (session is IDisposable disposable)
                {
                    disposable.Dispose();
                }

                Logger.Instance.Info($"Closed player session {sessionId}: {reason} (Duration: {duration:hh\\:mm\\:ss})");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error closing player session {sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Authenticate session
        /// </summary>
        public async Task<bool> AuthenticateSessionAsync(int sessionId, int accountId, string characterName)
        {
            try
            {
                var session = await GetPlayerSessionAsync(sessionId);
                if (session == null)
                {
                    Logger.Instance.Warning($"Session {sessionId} not found for authentication");
                    return false;
                }

                // Check for duplicate login
                if (_configuration.PreventDuplicateLogins && _accountToSession.ContainsKey(accountId))
                {
                    var existingSessionId = _accountToSession[accountId];
                    if (existingSessionId != sessionId)
                    {
                        Logger.Instance.Warning($"Account {accountId} already logged in on session {existingSessionId}");
                        await ClosePlayerSessionAsync(existingSessionId, "Duplicate login detected");
                    }
                }

                // Authenticate the session
                if (session is PlayerSession playerSession)
                {
                    var success = await playerSession.AuthenticateAsync(accountId, characterName);
                    if (success)
                    {
                        _accountToSession[accountId] = sessionId;
                        _statistics.AuthenticatedSessions++;

                        // Publish events
                        var authEvent = new PlayerSessionAuthenticatedEvent(sessionId, accountId, characterName);
                        await PublishEventAsync(authEvent);

                        var connectEvent = new PlayerConnectedEvent(sessionId, accountId, characterName);
                        await PublishEventAsync(connectEvent);

                        Logger.Instance.Info($"Authenticated session {sessionId} for account {accountId}, character {characterName}");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error authenticating session {sessionId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Set player for session
        /// </summary>
        public async Task SetPlayerAsync(int sessionId, object player)
        {
            try
            {
                var session = await GetPlayerSessionAsync(sessionId);
                if (session != null)
                {
                    session.Player = player;
                    Logger.Instance.Debug($"Set player for session {sessionId}");
                }
                else
                {
                    Logger.Instance.Warning($"Session {sessionId} not found for setting player");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error setting player for session {sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Get session events
        /// </summary>
        public async IAsyncEnumerable<SessionManagerEvent> GetEventsAsync()
        {
            var reader = _eventChannel.Reader;
            
            while (await reader.WaitToReadAsync())
            {
                while (reader.TryRead(out var sessionEvent))
                {
                    yield return sessionEvent;
                }
            }
        }

        /// <summary>
        /// Publish session event
        /// </summary>
        private async Task PublishEventAsync(SessionManagerEvent sessionEvent)
        {
            try
            {
                // Send to event channel
                await _eventWriter.WriteAsync(sessionEvent);
                
                // Also publish to event bus
                await _eventBus.PublishAsync(sessionEvent);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error publishing session event: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleanup expired sessions
        /// </summary>
        private void CleanupSessions(object? state)
        {
            try
            {
                var expiredSessions = new List<int>();
                var now = DateTime.UtcNow;

                foreach (var kvp in _sessions)
                {
                    var session = kvp.Value;
                    
                    // Check if session is expired
                    if (session is PlayerSession playerSession)
                    {
                        var isExpired = false;
                        
                        if (!session.IsAuthenticated)
                        {
                            // Unauthenticated sessions expire faster
                            isExpired = playerSession.GetDuration() > _configuration.AuthenticationTimeout;
                        }
                        else
                        {
                            // Authenticated sessions expire based on idle time
                            isExpired = playerSession.IsIdle(_configuration.SessionTimeout);
                        }

                        if (isExpired || !session.IsConnected)
                        {
                            expiredSessions.Add(session.SessionId);
                        }
                    }
                }

                // Close expired sessions
                foreach (var sessionId in expiredSessions)
                {
                    _ = Task.Run(async () => await ClosePlayerSessionAsync(sessionId, "Session expired"));
                }

                if (expiredSessions.Count > 0)
                {
                    Logger.Instance.Info($"Cleaned up {expiredSessions.Count} expired sessions");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error during session cleanup: {ex.Message}");
            }
        }

        /// <summary>
        /// Get session statistics
        /// </summary>
        public SessionStatistics GetStatistics()
        {
            _statistics.ActiveSessions = _sessions.Count;
            _statistics.PendingAuthentication = _sessions.Values.Count(s => !s.IsAuthenticated && s.IsConnected);
            
            // Calculate average session duration
            var activeSessions = _sessions.Values.Where(s => s is PlayerSession).Cast<PlayerSession>().ToList();
            if (activeSessions.Count > 0)
            {
                var totalDuration = activeSessions.Sum(s => s.GetDuration().Ticks);
                _statistics.AverageSessionDuration = new TimeSpan(totalDuration / activeSessions.Count);
            }

            return _statistics;
        }

        protected override async Task OnStoppingAsync(CancellationToken cancellationToken)
        {
            Logger.Instance.Info("Stopping session manager...");

            // Close all sessions
            var closeTasks = _sessions.Values.Select(session => 
                ClosePlayerSessionAsync(session.SessionId, "Server shutdown"));
            
            await Task.WhenAll(closeTasks);

            // Complete event channel
            _eventWriter.Complete();
            
            _cleanupTimer?.Dispose();
            
            await base.OnStoppingAsync(cancellationToken);
        }

        public override void Dispose()
        {
            _cleanupTimer?.Dispose();
            _eventWriter?.Complete();
            
            base.Dispose();
        }
    }
}
