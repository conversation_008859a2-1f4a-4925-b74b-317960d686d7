using System;
using System.Collections.Generic;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer;

public class Event_Duong_Dua_F1
{
	private System.Timers.Timer ThoiGian1;

	private System.Timers.Timer ThoiGian2;

	private DateTime dateTime_0;

	private DateTime iqOqpBruKS;

	public Event_Duong_Dua_F1()
	{
		try
		{
			World.Event_Duong_Dua_F1_Progress = 1;
			World.Npc_DuongDua_F1.Clear();
			dateTime_0 = DateTime.Now.AddHours(12.0);
			ThoiGian1 = new(3000.0);
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			ThoiGian1.Enabled = true;
			ThoiGian1.AutoReset = true;
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				Call_NPC_Add_Duong_Dua_F1();
			}
			else
			{
				ThoiGianKetThucSuKien1(null, null);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "BOSS Event Duong Dua Progress = 1 lỗi !! ----------" + ex.Message);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num <= 0)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
				World.Event_Duong_Dua_F1_Progress = 2;
				iqOqpBruKS = DateTime.Now.AddHours(12.0);
				ThoiGian2 = new(3000.0);
				ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
				ThoiGian2.Enabled = true;
				ThoiGian2.AutoReset = true;
				var num2 = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
				if (num2 <= 0)
				{
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Event Đường Đua F1 222 Phạm sai lầm：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
			if (num <= 0)
			{
				World.Event_Duong_Dua_F1_Progress = 0;
				ThoiGian2.Enabled = false;
				ThoiGian2.Close();
				ThoiGian2.Dispose();
				World.Event_DuongDua_F1_ON_OFF.Dispose();
				Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tính toán Đường Đua Event còn thừa số lượng phạm sai lầm：" + ex);
		}
	}

	public static void GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(Players player, int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void Call_NPC_Add_Duong_Dua_F1()
	{
		try
		{
			AddNpc(1118f, 2138f);
			AddNpc(1118f, 2134f);
			AddNpc(1118f, 2130f);
			AddNpc(1118f, 2126f);
			AddNpc(1118f, 2122f);
			AddNpc(1118f, 2118f);
			AddNpc(1118f, 2114f);
			AddNpc(1118f, 2110f);
			AddNpc(1118f, 2116f);
			AddNpc(1118f, 2112f);
			AddNpc(1118f, 2108f);
			AddNpc(1118f, 2106f);
			AddNpc(1118f, 2102f);
			AddNpc(1118f, 2098f);
			AddNpc(1118f, 2094f);
			AddNpc(1118f, 2090f);
			AddNpc(1118f, 2086f);
			AddNpc(1118f, 2082f);
			AddNpc(1118f, 2078f);
			AddNpc(1118f, 2072f);
			AddNpc(1118f, 2068f);
			AddNpc(1118f, 2064f);
			AddNpc(1118f, 2060f);
			AddNpc(1118f, 2056f);
			AddNpc(1118f, 2052f);
			AddNpc(1118f, 2048f);
			AddNpc(1122f, 2044f);
			AddNpc(1126f, 2040f);
			AddNpc(1130f, 2036f);
			AddNpc(1134f, 2032f);
			AddNpc(1138f, 2032f);
			AddNpc(1142f, 2032f);
			AddNpc(1146f, 2032f);
			AddNpc(1150f, 2032f);
			AddNpc(1152f, 2006f);
			AddNpc(1154f, 2008f);
			AddNpc(1154f, 2012f);
			AddNpc(1154f, 2016f);
			AddNpc(1154f, 2020f);
			AddNpc(1154f, 2024f);
			AddNpc(1154f, 2028f);
			AddNpc(1152f, 2030f);
			AddNpc(1150f, 2004f);
			AddNpc(1146f, 2004f);
			AddNpc(1142f, 2004f);
			AddNpc(1138f, 2004f);
			AddNpc(1134f, 2004f);
			AddNpc(1130f, 2004f);
			AddNpc(1126f, 2004f);
			AddNpc(1122f, 2004f);
			AddNpc(1118f, 2004f);
			AddNpc(1118f, 2000f);
			AddNpc(1118f, 1996f);
			AddNpc(1118f, 1992f);
			AddNpc(1118f, 1988f);
			AddNpc(1118f, 1984f);
			AddNpc(1118f, 1980f);
			AddNpc(1118f, 1976f);
			AddNpc(1118f, 1972f);
			AddNpc(1118f, 1968f);
			AddNpc(1118f, 1964f);
			AddNpc(1118f, 1960f);
			AddNpc(1118f, 1956f);
			AddNpc(1118f, 1952f);
			AddNpc(1118f, 1948f);
			AddNpc(1118f, 1944f);
			AddNpc(1118f, 1940f);
			AddNpc(1118f, 1936f);
			AddNpc(1118f, 1932f);
			AddNpc(1118f, 1928f);
			AddNpc(1118f, 1924f);
			AddNpc(1122f, 1924f);
			AddNpc(1126f, 1924f);
			AddNpc(1130f, 1924f);
			AddNpc(1134f, 1924f);
			AddNpc(1138f, 1924f);
			AddNpc(1142f, 1924f);
			AddNpc(1146f, 1924f);
			AddNpc(1150f, 1924f);
			AddNpc(1154f, 1924f);
			AddNpc(1158f, 1924f);
			AddNpc(1162f, 1924f);
			AddNpc(1166f, 1924f);
			AddNpc(1170f, 1924f);
			AddNpc(1174f, 1924f);
			AddNpc(1178f, 1924f);
			AddNpc(1182f, 1924f);
			AddNpc(1186f, 1924f);
			AddNpc(1190f, 1924f);
			AddNpc(1194f, 1924f);
			AddNpc(1198f, 1924f);
			AddNpc(1202f, 1920f);
			AddNpc(1202f, 1916f);
			AddNpc(1202f, 1912f);
			AddNpc(1202f, 1908f);
			AddNpc(1202f, 1904f);
			AddNpc(1202f, 1900f);
			AddNpc(1198f, 1896f);
			AddNpc(1194f, 1896f);
			AddNpc(1190f, 1896f);
			AddNpc(1186f, 1896f);
			AddNpc(1182f, 1896f);
			AddNpc(1178f, 1896f);
			AddNpc(1174f, 1896f);
			AddNpc(1170f, 1896f);
			AddNpc(1166f, 1896f);
			AddNpc(1162f, 1896f);
			AddNpc(1158f, 1896f);
			AddNpc(1154f, 1896f);
			AddNpc(1150f, 1896f);
			AddNpc(1146f, 1896f);
			AddNpc(1142f, 1896f);
			AddNpc(1138f, 1896f);
			AddNpc(1134f, 1896f);
			AddNpc(1130f, 1896f);
			AddNpc(1126f, 1896f);
			AddNpc(1122f, 1896f);
			AddNpc(1118f, 1896f);
			AddNpc(1114f, 1896f);
			AddNpc(1110f, 1896f);
			AddNpc(1108f, 1896f);
			AddNpc(1104f, 1896f);
			AddNpc(1100f, 1896f);
			AddNpc(1096f, 1896f);
			AddNpc(1092f, 1896f);
			AddNpc(1088f, 1896f);
			AddNpc(1084f, 1896f);
			AddNpc(1080f, 1896f);
			AddNpc(1076f, 1896f);
			AddNpc(1072f, 1896f);
			AddNpc(1068f, 1896f);
			AddNpc(1064f, 1896f);
			AddNpc(1060f, 1896f);
			AddNpc(1056f, 1896f);
			AddNpc(1052f, 1896f);
			AddNpc(1048f, 1896f);
			AddNpc(1044f, 1896f);
			AddNpc(1040f, 1896f);
			AddNpc(1036f, 1896f);
			AddNpc(1032f, 1896f);
			AddNpc(1028f, 1896f);
			AddNpc(1024f, 1896f);
			AddNpc(1020f, 1896f);
			AddNpc(1020f, 1892f);
			AddNpc(1020f, 1888f);
			AddNpc(1020f, 1884f);
			AddNpc(1020f, 1880f);
			AddNpc(1020f, 1876f);
			AddNpc(1020f, 1872f);
			AddNpc(1020f, 1868f);
			AddNpc(1020f, 1864f);
			AddNpc(1020f, 1860f);
			AddNpc(1020f, 1856f);
			AddNpc(1020f, 1852f);
			AddNpc(1020f, 1848f);
			AddNpc(1020f, 1844f);
			AddNpc(1020f, 1840f);
			AddNpc(1020f, 1836f);
			AddNpc(1020f, 1832f);
			AddNpc(1020f, 1828f);
			AddNpc(1020f, 1824f);
			AddNpc(1020f, 1820f);
			AddNpc(1020f, 1816f);
			AddNpc(1020f, 1812f);
			AddNpc(1020f, 1808f);
			AddNpc(1020f, 1804f);
			AddNpc(1020f, 1800f);
			AddNpc(1020f, 1796f);
			AddNpc(1020f, 1792f);
			AddNpc(1020f, 1788f);
			AddNpc(1020f, 1784f);
			AddNpc(1020f, 1780f);
			AddNpc(1020f, 1776f);
			AddNpc(1020f, 1772f);
			AddNpc(1020f, 1768f);
			AddNpc(1020f, 1764f);
			AddNpc(1020f, 1760f);
			AddNpc(1020f, 1756f);
			AddNpc(1020f, 1752f);
			AddNpc(1020f, 1748f);
			AddNpc(1020f, 1744f);
			AddNpc(1020f, 1740f);
			AddNpc(1020f, 1736f);
			AddNpc(1020f, 1732f);
			AddNpc(1016f, 1728f);
			AddNpc(1012f, 1728f);
			AddNpc(1008f, 1728f);
			AddNpc(1004f, 1728f);
			AddNpc(1000f, 1728f);
			AddNpc(996f, 1728f);
			AddNpc(992f, 1728f);
			AddNpc(988f, 1728f);
			AddNpc(984f, 1728f);
			AddNpc(980f, 1728f);
			AddNpc(976f, 1728f);
			AddNpc(972f, 1728f);
			AddNpc(968f, 1728f);
			AddNpc(964f, 1728f);
			AddNpc(960f, 1728f);
			AddNpc(956f, 1728f);
			AddNpc(956f, 1724f);
			AddNpc(956f, 1720f);
			AddNpc(956f, 1716f);
			AddNpc(956f, 1712f);
			AddNpc(956f, 1708f);
			AddNpc(956f, 1704f);
			AddNpc(956f, 1700f);
			AddNpc(956f, 1696f);
			AddNpc(956f, 1692f);
			AddNpc(956f, 1688f);
			AddNpc(956f, 1684f);
			AddNpc(956f, 1680f);
			AddNpc(956f, 1676f);
			AddNpc(956f, 1672f);
			AddNpc(956f, 1668f);
			AddNpc(956f, 1664f);
			AddNpc(956f, 1660f);
			AddNpc(956f, 1656f);
			AddNpc(956f, 1652f);
			AddNpc(956f, 1648f);
			AddNpc(956f, 1644f);
			AddNpc(956f, 1640f);
			AddNpc(956f, 1636f);
			AddNpc(956f, 1632f);
			AddNpc(956f, 1628f);
			AddNpc(956f, 1624f);
			AddNpc(956f, 1620f);
			AddNpc(960f, 1620f);
			AddNpc(964f, 1620f);
			AddNpc(968f, 1620f);
			AddNpc(972f, 1620f);
			AddNpc(976f, 1620f);
			AddNpc(980f, 1620f);
			AddNpc(984f, 1620f);
			AddNpc(988f, 1620f);
			AddNpc(992f, 1620f);
			AddNpc(996f, 1620f);
			AddNpc(1000f, 1620f);
			AddNpc(1004f, 1620f);
			AddNpc(1008f, 1620f);
			AddNpc(1012f, 1620f);
			AddNpc(1016f, 1620f);
			AddNpc(1020f, 1620f);
			AddNpc(1024f, 1620f);
			AddNpc(1028f, 1620f);
			AddNpc(1032f, 1620f);
			AddNpc(1036f, 1620f);
			AddNpc(1040f, 1620f);
			AddNpc(1044f, 1620f);
			AddNpc(1048f, 1620f);
			AddNpc(1052f, 1620f);
			AddNpc(1056f, 1620f);
			AddNpc(1060f, 1620f);
			AddNpc(1060f, 1624f);
			AddNpc(1060f, 1628f);
			AddNpc(1060f, 1632f);
			AddNpc(1060f, 1636f);
			AddNpc(1060f, 1640f);
			AddNpc(1060f, 1644f);
			AddNpc(1060f, 1648f);
			AddNpc(1064f, 1648f);
			AddNpc(1068f, 1648f);
			AddNpc(1072f, 1648f);
			AddNpc(1076f, 1648f);
			AddNpc(1080f, 1648f);
			AddNpc(1084f, 1648f);
			AddNpc(1088f, 1648f);
			AddNpc(1092f, 1648f);
			AddNpc(1096f, 1648f);
			AddNpc(1100f, 1648f);
			AddNpc(1104f, 1648f);
			AddNpc(1108f, 1648f);
			AddNpc(1108f, 1652f);
			AddNpc(1108f, 1656f);
			AddNpc(1108f, 1660f);
			AddNpc(1108f, 1648f);
			AddNpc(1108f, 1652f);
			AddNpc(1108f, 1656f);
			AddNpc(1108f, 1660f);
			AddNpc(1108f, 1664f);
			AddNpc(1108f, 1668f);
			AddNpc(1108f, 1672f);
			AddNpc(1108f, 1676f);
			AddNpc(1108f, 1680f);
			AddNpc(1108f, 1684f);
			AddNpc(1108f, 1686f);
			AddNpc(1108f, 1690f);
			AddNpc(1108f, 1694f);
			AddNpc(1108f, 1698f);
			AddNpc(1108f, 1702f);
			AddNpc(1108f, 1706f);
			AddNpc(1108f, 1710f);
			AddNpc(1108f, 1714f);
			AddNpc(1108f, 1718f);
			AddNpc(1108f, 1722f);
			AddNpc(1108f, 1726f);
			AddNpc(1108f, 1730f);
			AddNpc(1108f, 1734f);
			AddNpc(1108f, 1738f);
			AddNpc(1108f, 1742f);
			AddNpc(1108f, 1746f);
			AddNpc(1108f, 1750f);
			AddNpc(1108f, 1754f);
			AddNpc(1108f, 1758f);
			AddNpc(1108f, 1762f);
			AddNpc(1108f, 1766f);
			AddNpc(1108f, 1770f);
			AddNpc(1108f, 1774f);
			AddNpc(1108f, 1778f);
			AddNpc(1108f, 1782f);
			AddNpc(1108f, 1786f);
			AddNpc(1108f, 1790f);
			AddNpc(1108f, 1794f);
			AddNpc(1108f, 1798f);
			AddNpc(1108f, 1802f);
			AddNpc(1112f, 1806f);
			AddNpc(1116f, 1806f);
			AddNpc(1120f, 1806f);
			AddNpc(1124f, 1806f);
			AddNpc(1128f, 1806f);
			AddNpc(1132f, 1806f);
			AddNpc(1136f, 1806f);
			AddNpc(1140f, 1806f);
			AddNpc(1144f, 1806f);
			AddNpc(1148f, 1806f);
			AddNpc(1152f, 1806f);
			AddNpc(1156f, 1806f);
			AddNpc(1160f, 1806f);
			AddNpc(1164f, 1806f);
			AddNpc(1168f, 1806f);
			AddNpc(1172f, 1806f);
			AddNpc(1176f, 1806f);
			AddNpc(1180f, 1802f);
			AddNpc(1180f, 1798f);
			AddNpc(1180f, 1794f);
			AddNpc(1180f, 1790f);
			AddNpc(1180f, 1786f);
			AddNpc(1180f, 1782f);
			AddNpc(1180f, 1778f);
			AddNpc(1180f, 1774f);
			AddNpc(1180f, 1770f);
			AddNpc(1180f, 1766f);
			AddNpc(1180f, 1762f);
			AddNpc(1180f, 1758f);
			AddNpc(1184f, 1758f);
			AddNpc(1188f, 1758f);
			AddNpc(1192f, 1758f);
			AddNpc(1196f, 1758f);
			AddNpc(1200f, 1758f);
			AddNpc(1204f, 1758f);
			AddNpc(1208f, 1758f);
			AddNpc(1212f, 1758f);
			AddNpc(1216f, 1758f);
			AddNpc(1220f, 1758f);
			AddNpc(1224f, 1758f);
			AddNpc(1228f, 1758f);
			AddNpc(1232f, 1758f);
			AddNpc(1236f, 1758f);
			AddNpc(1240f, 1758f);
			AddNpc(1244f, 1758f);
			AddNpc(1248f, 1758f);
			AddNpc(1252f, 1758f);
			AddNpc(1256f, 1758f);
			AddNpc(1260f, 1754f);
			AddNpc(1260f, 1750f);
			AddNpc(1260f, 1746f);
			AddNpc(1260f, 1742f);
			AddNpc(1260f, 1738f);
			AddNpc(1260f, 1734f);
			AddNpc(1260f, 1730f);
			AddNpc(1260f, 1726f);
			AddNpc(1260f, 1724f);
			AddNpc(1260f, 1720f);
			AddNpc(1260f, 1716f);
			AddNpc(1260f, 1712f);
			AddNpc(1260f, 1708f);
			AddNpc(1260f, 1704f);
			AddNpc(1260f, 1700f);
			AddNpc(1260f, 1694f);
			AddNpc(1260f, 1690f);
			AddNpc(1260f, 1686f);
			AddNpc(1260f, 1682f);
			AddNpc(1260f, 1678f);
			AddNpc(1260f, 1674f);
			AddNpc(1260f, 1670f);
			AddNpc(1260f, 1666f);
			AddNpc(1260f, 1662f);
			AddNpc(1260f, 1658f);
			AddNpc(1256f, 1654f);
			AddNpc(1252f, 1654f);
			AddNpc(1248f, 1654f);
			AddNpc(1244f, 1654f);
			AddNpc(1240f, 1654f);
			AddNpc(1236f, 1654f);
			AddNpc(1232f, 1654f);
			AddNpc(1228f, 1654f);
			AddNpc(1224f, 1658f);
			AddNpc(1224f, 1662f);
			AddNpc(1224f, 1666f);
			AddNpc(1224f, 1670f);
			AddNpc(1224f, 1674f);
			AddNpc(1224f, 1678f);
			AddNpc(1224f, 1682f);
			AddNpc(1224f, 1686f);
			AddNpc(1224f, 1690f);
			AddNpc(1224f, 1694f);
			AddNpc(1224f, 1698f);
			AddNpc(1224f, 1702f);
			AddNpc(1224f, 1706f);
			AddNpc(1224f, 1710f);
			AddNpc(1224f, 1714f);
			AddNpc(1224f, 1718f);
			AddNpc(1224f, 1722f);
			AddNpc(1220f, 1722f);
			AddNpc(1216f, 1722f);
			AddNpc(1212f, 1722f);
			AddNpc(1208f, 1722f);
			AddNpc(1204f, 1722f);
			AddNpc(1200f, 1722f);
			AddNpc(1196f, 1722f);
			AddNpc(1192f, 1722f);
			AddNpc(1188f, 1722f);
			AddNpc(1184f, 1722f);
			AddNpc(1180f, 1722f);
			AddNpc(1176f, 1722f);
			AddNpc(1172f, 1722f);
			AddNpc(1168f, 1722f);
			AddNpc(1164f, 1722f);
			AddNpc(1160f, 1722f);
			AddNpc(1156f, 1722f);
			AddNpc(1152f, 1722f);
			AddNpc(1148f, 1722f);
			AddNpc(1144f, 1722f);
			AddNpc(1140f, 1722f);
			AddNpc(1140f, 1718f);
			AddNpc(1140f, 1714f);
			AddNpc(1140f, 1710f);
			AddNpc(1140f, 1706f);
			AddNpc(1140f, 1702f);
			AddNpc(1140f, 1698f);
			AddNpc(1140f, 1694f);
			AddNpc(1140f, 1690f);
			AddNpc(1140f, 1686f);
			AddNpc(1140f, 1682f);
			AddNpc(1140f, 1678f);
			AddNpc(1140f, 1674f);
			AddNpc(1140f, 1670f);
			AddNpc(1140f, 1666f);
			AddNpc(1140f, 1662f);
			AddNpc(1140f, 1658f);
			AddNpc(1140f, 1654f);
			AddNpc(1140f, 1650f);
			AddNpc(1140f, 1646f);
			AddNpc(1140f, 1642f);
			AddNpc(1140f, 1638f);
			AddNpc(1140f, 1634f);
			AddNpc(1140f, 1630f);
			AddNpc(1140f, 1626f);
			AddNpc(1140f, 1622f);
			AddNpc(1140f, 1618f);
			AddNpc(1140f, 1614f);
			AddNpc(1140f, 1610f);
			AddNpc(1140f, 1606f);
			AddNpc(1140f, 1602f);
			AddNpc(1140f, 1598f);
			AddNpc(1140f, 1594f);
			AddNpc(1140f, 1590f);
			AddNpc(1140f, 1586f);
			AddNpc(1140f, 1582f);
			AddNpc(1140f, 1582f);
			AddNpc(1146f, 1582f);
			AddNpc(1150f, 1582f);
			AddNpc(1154f, 1578f);
			AddNpc(1154f, 1574f);
			AddNpc(1154f, 1570f);
			AddNpc(1154f, 1566f);
			AddNpc(1154f, 1562f);
			AddNpc(1154f, 1558f);
			AddNpc(1154f, 1554f);
			AddNpc(1154f, 1550f);
			AddNpc(1154f, 1546f);
			AddNpc(1154f, 1542f);
			AddNpc(1154f, 1538f);
			AddNpc(1154f, 1534f);
			AddNpc(1154f, 1530f);
			AddNpc(1154f, 1526f);
			AddNpc(1154f, 1522f);
			AddNpc(1154f, 1518f);
			AddNpc(1154f, 1514f);
			AddNpc(1154f, 1510f);
			AddNpc(1154f, 1508f);
			AddNpc(1154f, 1504f);
			AddNpc(1154f, 1500f);
			AddNpc(1154f, 1496f);
			AddNpc(1154f, 1492f);
			AddNpc(1154f, 1488f);
			AddNpc(1154f, 1484f);
			AddNpc(1154f, 1480f);
			AddNpc(1154f, 1480f);
			AddNpc(1158f, 1480f);
			AddNpc(1162f, 1480f);
			AddNpc(1166f, 1480f);
			AddNpc(1122f, 2138f);
			AddNpc(1126f, 2138f);
			AddNpc(1130f, 2138f);
			AddNpc(1134f, 2138f);
			AddNpc(1138f, 2138f);
			AddNpc(1142f, 2138f);
			AddNpc(1146f, 2138f);
			AddNpc(1150f, 2138f);
			AddNpc(1154f, 2138f);
			AddNpc(1158f, 2138f);
			AddNpc(1162f, 2138f);
			AddNpc(1166f, 2138f);
			AddNpc(1170f, 2138f);
			AddNpc(1174f, 2138f);
			AddNpc(1178f, 2138f);
			AddNpc(1182f, 2138f);
			AddNpc(1186f, 2138f);
			AddNpc(1190f, 2138f);
			AddNpc(1194f, 2138f);
			AddNpc(1198f, 2138f);
			AddNpc(1201f, 2138f);
			AddNpc(1201f, 2134f);
			AddNpc(1201f, 2130f);
			AddNpc(1201f, 2126f);
			AddNpc(1201f, 2122f);
			AddNpc(1201f, 2118f);
			AddNpc(1201f, 2114f);
			AddNpc(1201f, 2110f);
			AddNpc(1201f, 2106f);
			AddNpc(1201f, 2104f);
			AddNpc(1201f, 2100f);
			AddNpc(1201f, 2096f);
			AddNpc(1201f, 2092f);
			AddNpc(1201f, 2088f);
			AddNpc(1201f, 2084f);
			AddNpc(1201f, 2080f);
			AddNpc(1201f, 2076f);
			AddNpc(1201f, 2072f);
			AddNpc(1201f, 2068f);
			AddNpc(1201f, 2064f);
			AddNpc(1201f, 2060f);
			AddNpc(1201f, 2056f);
			AddNpc(1201f, 2052f);
			AddNpc(1201f, 2048f);
			AddNpc(1201f, 2044f);
			AddNpc(1201f, 2040f);
			AddNpc(1201f, 2036f);
			AddNpc(1201f, 2032f);
			AddNpc(1201f, 2028f);
			AddNpc(1201f, 2024f);
			AddNpc(1201f, 2020f);
			AddNpc(1201f, 2016f);
			AddNpc(1201f, 2012f);
			AddNpc(1201f, 2008f);
			AddNpc(1201f, 2004f);
			AddNpc(1201f, 2000f);
			AddNpc(1201f, 1996f);
			AddNpc(1201f, 1992f);
			AddNpc(1201f, 1988f);
			AddNpc(1201f, 1984f);
			AddNpc(1201f, 1980f);
			AddNpc(1201f, 1976f);
			AddNpc(1196f, 1976f);
			AddNpc(1192f, 1976f);
			AddNpc(1188f, 1976f);
			AddNpc(1184f, 1976f);
			AddNpc(1180f, 1976f);
			AddNpc(1176f, 1976f);
			AddNpc(1172f, 1976f);
			AddNpc(1168f, 1976f);
			AddNpc(1164f, 1976f);
			AddNpc(1160f, 1976f);
			AddNpc(1156f, 1976f);
			AddNpc(1152f, 1976f);
			AddNpc(1148f, 1976f);
			AddNpc(1146f, 1974f);
			AddNpc(1144f, 1972f);
			AddNpc(1144f, 1968f);
			AddNpc(1144f, 1964f);
			AddNpc(1144f, 1960f);
			AddNpc(1144f, 1956f);
			AddNpc(1144f, 1952f);
			AddNpc(1146f, 1950f);
			AddNpc(1148f, 1948f);
			AddNpc(1152f, 1948f);
			AddNpc(1156f, 1948f);
			AddNpc(1160f, 1948f);
			AddNpc(1164f, 1948f);
			AddNpc(1168f, 1948f);
			AddNpc(1171f, 1945f);
			AddNpc(1174f, 1942f);
			AddNpc(1178f, 1942f);
			AddNpc(1182f, 1942f);
			AddNpc(1186f, 1942f);
			AddNpc(1190f, 1942f);
			AddNpc(1194f, 1942f);
			AddNpc(1198f, 1942f);
			AddNpc(1202f, 1942f);
			AddNpc(1206f, 1942f);
			AddNpc(1210f, 1942f);
			AddNpc(1214f, 1942f);
			AddNpc(1218f, 1942f);
			AddNpc(1222f, 1942f);
			AddNpc(1222f, 1938f);
			AddNpc(1222f, 1934f);
			AddNpc(1222f, 1930f);
			AddNpc(1222f, 1926f);
			AddNpc(1222f, 1922f);
			AddNpc(1222f, 1918f);
			AddNpc(1222f, 1914f);
			AddNpc(1222f, 1910f);
			AddNpc(1222f, 1906f);
			AddNpc(1222f, 1902f);
			AddNpc(1222f, 1898f);
			AddNpc(1222f, 1894f);
			AddNpc(1222f, 1890f);
			AddNpc(1222f, 1886f);
			AddNpc(1222f, 1882f);
			AddNpc(1222f, 1878f);
			AddNpc(1222f, 1874f);
			AddNpc(1222f, 1870f);
			AddNpc(1218f, 1870f);
			AddNpc(1214f, 1870f);
			AddNpc(1210f, 1870f);
			AddNpc(1206f, 1870f);
			AddNpc(1202f, 1870f);
			AddNpc(1198f, 1870f);
			AddNpc(1194f, 1870f);
			AddNpc(1190f, 1870f);
			AddNpc(1186f, 1870f);
			AddNpc(1182f, 1870f);
			AddNpc(1178f, 1870f);
			AddNpc(1174f, 1870f);
			AddNpc(1170f, 1870f);
			AddNpc(1166f, 1870f);
			AddNpc(1162f, 1870f);
			AddNpc(1158f, 1870f);
			AddNpc(1154f, 1870f);
			AddNpc(1150f, 1870f);
			AddNpc(1146f, 1870f);
			AddNpc(1142f, 1870f);
			AddNpc(1138f, 1870f);
			AddNpc(1134f, 1870f);
			AddNpc(1130f, 1870f);
			AddNpc(1126f, 1870f);
			AddNpc(1122f, 1870f);
			AddNpc(1118f, 1870f);
			AddNpc(1114f, 1870f);
			AddNpc(1110f, 1870f);
			AddNpc(1106f, 1870f);
			AddNpc(1102f, 1870f);
			AddNpc(1098f, 1870f);
			AddNpc(1094f, 1870f);
			AddNpc(1090f, 1870f);
			AddNpc(1086f, 1870f);
			AddNpc(1082f, 1870f);
			AddNpc(1078f, 1870f);
			AddNpc(1074f, 1870f);
			AddNpc(1070f, 1870f);
			AddNpc(1066f, 1870f);
			AddNpc(1062f, 1870f);
			AddNpc(1058f, 1870f);
			AddNpc(1054f, 1870f);
			AddNpc(1050f, 1870f);
			AddNpc(1046f, 1866f);
			AddNpc(1046f, 1862f);
			AddNpc(1046f, 1858f);
			AddNpc(1046f, 1854f);
			AddNpc(1046f, 1850f);
			AddNpc(1046f, 1846f);
			AddNpc(1046f, 1842f);
			AddNpc(1046f, 1838f);
			AddNpc(1046f, 1834f);
			AddNpc(1046f, 1830f);
			AddNpc(1046f, 1826f);
			AddNpc(1046f, 1822f);
			AddNpc(1046f, 1818f);
			AddNpc(1046f, 1814f);
			AddNpc(1046f, 1810f);
			AddNpc(1046f, 1806f);
			AddNpc(1046f, 1802f);
			AddNpc(1046f, 1798f);
			AddNpc(1046f, 1794f);
			AddNpc(1046f, 1790f);
			AddNpc(1046f, 1786f);
			AddNpc(1046f, 1782f);
			AddNpc(1046f, 1778f);
			AddNpc(1046f, 1774f);
			AddNpc(1046f, 1770f);
			AddNpc(1046f, 1766f);
			AddNpc(1046f, 1762f);
			AddNpc(1046f, 1758f);
			AddNpc(1046f, 1754f);
			AddNpc(1046f, 1750f);
			AddNpc(1046f, 1746f);
			AddNpc(1046f, 1742f);
			AddNpc(1046f, 1738f);
			AddNpc(1046f, 1734f);
			AddNpc(1046f, 1730f);
			AddNpc(1046f, 1726f);
			AddNpc(1046f, 1722f);
			AddNpc(1046f, 1718f);
			AddNpc(1046f, 1714f);
			AddNpc(1046f, 1710f);
			AddNpc(1046f, 1706f);
			AddNpc(1046f, 1702f);
			AddNpc(1042f, 1702f);
			AddNpc(1038f, 1702f);
			AddNpc(1034f, 1702f);
			AddNpc(1030f, 1702f);
			AddNpc(1028f, 1702f);
			AddNpc(1024f, 1702f);
			AddNpc(1020f, 1702f);
			AddNpc(1016f, 1702f);
			AddNpc(1012f, 1702f);
			AddNpc(1008f, 1702f);
			AddNpc(1004f, 1702f);
			AddNpc(1000f, 1702f);
			AddNpc(996f, 1702f);
			AddNpc(992f, 1702f);
			AddNpc(988f, 1702f);
			AddNpc(984f, 1702f);
			AddNpc(980f, 1698f);
			AddNpc(980f, 1694f);
			AddNpc(980f, 1690f);
			AddNpc(980f, 1686f);
			AddNpc(980f, 1682f);
			AddNpc(980f, 1678f);
			AddNpc(980f, 1674f);
			AddNpc(980f, 1670f);
			AddNpc(980f, 1666f);
			AddNpc(980f, 1662f);
			AddNpc(980f, 1658f);
			AddNpc(980f, 1652f);
			AddNpc(980f, 1648f);
			AddNpc(984f, 1644f);
			AddNpc(988f, 1644f);
			AddNpc(992f, 1644f);
			AddNpc(996f, 1644f);
			AddNpc(1000f, 1644f);
			AddNpc(1004f, 1644f);
			AddNpc(1008f, 1644f);
			AddNpc(1012f, 1644f);
			AddNpc(1016f, 1644f);
			AddNpc(1020f, 1644f);
			AddNpc(1024f, 1644f);
			AddNpc(1028f, 1644f);
			AddNpc(1032f, 1644f);
			AddNpc(1036f, 1648f);
			AddNpc(1036f, 1652f);
			AddNpc(1036f, 1656f);
			AddNpc(1036f, 1660f);
			AddNpc(1036f, 1664f);
			AddNpc(1036f, 1668f);
			AddNpc(1040f, 1672f);
			AddNpc(1044f, 1672f);
			AddNpc(1048f, 1672f);
			AddNpc(1052f, 1672f);
			AddNpc(1056f, 1672f);
			AddNpc(1060f, 1672f);
			AddNpc(1064f, 1672f);
			AddNpc(1068f, 1672f);
			AddNpc(1072f, 1672f);
			AddNpc(1076f, 1672f);
			AddNpc(1080f, 1672f);
			AddNpc(1084f, 1676f);
			AddNpc(1084f, 1680f);
			AddNpc(1084f, 1684f);
			AddNpc(1084f, 1688f);
			AddNpc(1084f, 1692f);
			AddNpc(1084f, 1696f);
			AddNpc(1084f, 1700f);
			AddNpc(1084f, 1704f);
			AddNpc(1084f, 1708f);
			AddNpc(1084f, 1712f);
			AddNpc(1084f, 1716f);
			AddNpc(1084f, 1720f);
			AddNpc(1084f, 1724f);
			AddNpc(1084f, 1728f);
			AddNpc(1084f, 1732f);
			AddNpc(1084f, 1736f);
			AddNpc(1084f, 1740f);
			AddNpc(1084f, 1744f);
			AddNpc(1084f, 1748f);
			AddNpc(1084f, 1752f);
			AddNpc(1084f, 1756f);
			AddNpc(1084f, 1760f);
			AddNpc(1084f, 1764f);
			AddNpc(1084f, 1768f);
			AddNpc(1084f, 1772f);
			AddNpc(1084f, 1776f);
			AddNpc(1084f, 1780f);
			AddNpc(1084f, 1784f);
			AddNpc(1084f, 1788f);
			AddNpc(1084f, 1792f);
			AddNpc(1084f, 1796f);
			AddNpc(1084f, 1800f);
			AddNpc(1084f, 1804f);
			AddNpc(1084f, 1808f);
			AddNpc(1084f, 1812f);
			AddNpc(1084f, 1816f);
			AddNpc(1084f, 1820f);
			AddNpc(1084f, 1824f);
			AddNpc(1084f, 1828f);
			AddNpc(1088f, 1832f);
			AddNpc(1092f, 1832f);
			AddNpc(1096f, 1832f);
			AddNpc(1100f, 1832f);
			AddNpc(1104f, 1832f);
			AddNpc(1108f, 1832f);
			AddNpc(1112f, 1832f);
			AddNpc(1116f, 1832f);
			AddNpc(1120f, 1832f);
			AddNpc(1124f, 1832f);
			AddNpc(1128f, 1832f);
			AddNpc(1132f, 1832f);
			AddNpc(1136f, 1832f);
			AddNpc(1140f, 1832f);
			AddNpc(1144f, 1832f);
			AddNpc(1148f, 1832f);
			AddNpc(1152f, 1832f);
			AddNpc(1156f, 1832f);
			AddNpc(1160f, 1832f);
			AddNpc(1164f, 1832f);
			AddNpc(1168f, 1832f);
			AddNpc(1172f, 1832f);
			AddNpc(1176f, 1832f);
			AddNpc(1180f, 1832f);
			AddNpc(1184f, 1832f);
			AddNpc(1186f, 1832f);
			AddNpc(1190f, 1832f);
			AddNpc(1194f, 1832f);
			AddNpc(1198f, 1832f);
			AddNpc(1201f, 1832f);
			AddNpc(1201f, 1828f);
			AddNpc(1201f, 1824f);
			AddNpc(1201f, 1820f);
			AddNpc(1201f, 1816f);
			AddNpc(1201f, 1812f);
			AddNpc(1201f, 1808f);
			AddNpc(1201f, 1804f);
			AddNpc(1201f, 1800f);
			AddNpc(1201f, 1796f);
			AddNpc(1201f, 1792f);
			AddNpc(1201f, 1788f);
			AddNpc(1201f, 1784f);
			AddNpc(1204f, 1780f);
			AddNpc(1208f, 1780f);
			AddNpc(1212f, 1780f);
			AddNpc(1216f, 1780f);
			AddNpc(1220f, 1780f);
			AddNpc(1224f, 1780f);
			AddNpc(1228f, 1780f);
			AddNpc(1232f, 1780f);
			AddNpc(1236f, 1780f);
			AddNpc(1240f, 1780f);
			AddNpc(1244f, 1780f);
			AddNpc(1248f, 1780f);
			AddNpc(1252f, 1780f);
			AddNpc(1256f, 1780f);
			AddNpc(1260f, 1780f);
			AddNpc(1264f, 1780f);
			AddNpc(1268f, 1780f);
			AddNpc(1272f, 1780f);
			AddNpc(1276f, 1780f);
			AddNpc(1280f, 1780f);
			AddNpc(1280f, 1776f);
			AddNpc(1280f, 1772f);
			AddNpc(1280f, 1768f);
			AddNpc(1280f, 1764f);
			AddNpc(1280f, 1760f);
			AddNpc(1280f, 1756f);
			AddNpc(1280f, 1752f);
			AddNpc(1280f, 1748f);
			AddNpc(1280f, 1744f);
			AddNpc(1280f, 1740f);
			AddNpc(1280f, 1736f);
			AddNpc(1280f, 1732f);
			AddNpc(1280f, 1728f);
			AddNpc(1280f, 1724f);
			AddNpc(1280f, 1720f);
			AddNpc(1280f, 1716f);
			AddNpc(1280f, 1712f);
			AddNpc(1280f, 1708f);
			AddNpc(1280f, 1704f);
			AddNpc(1280f, 1700f);
			AddNpc(1280f, 1696f);
			AddNpc(1280f, 1692f);
			AddNpc(1280f, 1688f);
			AddNpc(1280f, 1684f);
			AddNpc(1280f, 1680f);
			AddNpc(1280f, 1676f);
			AddNpc(1280f, 1672f);
			AddNpc(1280f, 1668f);
			AddNpc(1280f, 1664f);
			AddNpc(1280f, 1660f);
			AddNpc(1280f, 1656f);
			AddNpc(1280f, 1652f);
			AddNpc(1280f, 1648f);
			AddNpc(1280f, 1644f);
			AddNpc(1280f, 1640f);
			AddNpc(1280f, 1636f);
			AddNpc(1276f, 1636f);
			AddNpc(1272f, 1636f);
			AddNpc(1268f, 1636f);
			AddNpc(1268f, 1636f);
			AddNpc(1264f, 1636f);
			AddNpc(1260f, 1636f);
			AddNpc(1256f, 1636f);
			AddNpc(1252f, 1636f);
			AddNpc(1248f, 1636f);
			AddNpc(1244f, 1636f);
			AddNpc(1240f, 1636f);
			AddNpc(1236f, 1636f);
			AddNpc(1232f, 1636f);
			AddNpc(1228f, 1636f);
			AddNpc(1224f, 1636f);
			AddNpc(1220f, 1636f);
			AddNpc(1216f, 1636f);
			AddNpc(1212f, 1636f);
			AddNpc(1208f, 1636f);
			AddNpc(1204f, 1636f);
			AddNpc(1201f, 1636f);
			AddNpc(1201f, 1640f);
			AddNpc(1201f, 1644f);
			AddNpc(1201f, 1648f);
			AddNpc(1201f, 1652f);
			AddNpc(1201f, 1656f);
			AddNpc(1201f, 1660f);
			AddNpc(1201f, 1664f);
			AddNpc(1201f, 1668f);
			AddNpc(1201f, 1672f);
			AddNpc(1201f, 1676f);
			AddNpc(1201f, 1680f);
			AddNpc(1201f, 1684f);
			AddNpc(1201f, 1688f);
			AddNpc(1201f, 1692f);
			AddNpc(1201f, 1696f);
			AddNpc(1201f, 1700f);
			AddNpc(1196f, 1704f);
			AddNpc(1192f, 1704f);
			AddNpc(1188f, 1704f);
			AddNpc(1184f, 1704f);
			AddNpc(1180f, 1704f);
			AddNpc(1176f, 1704f);
			AddNpc(1172f, 1704f);
			AddNpc(1168f, 1704f);
			AddNpc(1164f, 1704f);
			AddNpc(1160f, 1704f);
			AddNpc(1160f, 1700f);
			AddNpc(1160f, 1696f);
			AddNpc(1160f, 1692f);
			AddNpc(1160f, 1688f);
			AddNpc(1160f, 1684f);
			AddNpc(1160f, 1680f);
			AddNpc(1160f, 1676f);
			AddNpc(1160f, 1672f);
			AddNpc(1160f, 1668f);
			AddNpc(1160f, 1664f);
			AddNpc(1160f, 1660f);
			AddNpc(1160f, 1656f);
			AddNpc(1160f, 1652f);
			AddNpc(1160f, 1648f);
			AddNpc(1160f, 1644f);
			AddNpc(1160f, 1640f);
			AddNpc(1160f, 1636f);
			AddNpc(1160f, 1632f);
			AddNpc(1160f, 1628f);
			AddNpc(1160f, 1624f);
			AddNpc(1160f, 1620f);
			AddNpc(1160f, 1616f);
			AddNpc(1160f, 1612f);
			AddNpc(1160f, 1608f);
			AddNpc(1160f, 1604f);
			AddNpc(1160f, 1600f);
			AddNpc(1164f, 1600f);
			AddNpc(1168f, 1600f);
			AddNpc(1172f, 1600f);
			AddNpc(1172f, 1596f);
			AddNpc(1172f, 1592f);
			AddNpc(1172f, 1588f);
			AddNpc(1172f, 1584f);
			AddNpc(1172f, 1580f);
			AddNpc(1172f, 1576f);
			AddNpc(1172f, 1572f);
			AddNpc(1172f, 1568f);
			AddNpc(1172f, 1564f);
			AddNpc(1172f, 1560f);
			AddNpc(1172f, 1556f);
			AddNpc(1172f, 1552f);
			AddNpc(1172f, 1548f);
			AddNpc(1172f, 1544f);
			AddNpc(1172f, 1540f);
			AddNpc(1172f, 1536f);
			AddNpc(1172f, 1532f);
			AddNpc(1172f, 1528f);
			AddNpc(1172f, 1524f);
			AddNpc(1172f, 1520f);
			AddNpc(1172f, 1516f);
			AddNpc(1172f, 1512f);
			AddNpc(1172f, 1508f);
			AddNpc(1172f, 1504f);
			AddNpc(1172f, 1500f);
			AddNpc(1172f, 1496f);
			AddNpc(1172f, 1492f);
			AddNpc(1172f, 1488f);
			AddNpc(1172f, 1484f);
			AddNpc(1172f, 1480f);
			AddNpc(1168f, 1480f);
			AddNpc(1174f, 2076f);
			AddNpc(1170f, 2076f);
			AddNpc(1166f, 2076f);
			AddNpc(1162f, 2076f);
			AddNpc(1158f, 2076f);
			AddNpc(1154f, 2076f);
			AddNpc(1150f, 2076f);
			AddNpc(1146f, 2076f);
			AddNpc(1142f, 2080f);
			AddNpc(1142f, 2084f);
			AddNpc(1142f, 2088f);
			AddNpc(1142f, 2092f);
			AddNpc(1142f, 2096f);
			AddNpc(1142f, 2100f);
			AddNpc(1142f, 2104f);
			AddNpc(1146f, 2108f);
			AddNpc(1150f, 2108f);
			AddNpc(1154f, 2108f);
			AddNpc(1158f, 2108f);
			AddNpc(1162f, 2108f);
			AddNpc(1166f, 2108f);
			AddNpc(1170f, 2108f);
			AddNpc(1174f, 2108f);
			AddNpc(1178f, 2104f);
			AddNpc(1178f, 2100f);
			AddNpc(1178f, 2096f);
			AddNpc(1178f, 2092f);
			AddNpc(1178f, 2088f);
			AddNpc(1178f, 2084f);
			AddNpc(1178f, 2080f);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Event Trường Đua tạo Hàng Rào bị lỗi !! ----------");
		}
	}

	public void AddNpc(float float_0, float float_1)
	{
		try
		{
			if (World.MonsterTemplateList.TryGetValue(15278, out var value))
			{
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.fld_pid;
				npcClass.Name = value.fld_name;
				npcClass.Level = value.fld_level;
				npcClass.Rxjh_Exp = value.fld_exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = 101;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = 0f;
				npcClass.FLD_FACE2 = 0f;
				npcClass.Max_Rxjh_HP = value.fld_hp;
				npcClass.Rxjh_HP = value.fld_hp;
				npcClass.FLD_AT = value.fld_at;
				npcClass.FLD_DF = value.fld_df;
				npcClass.FLD_AUTO = value.fld_auto;
				npcClass.FLD_BOSS = 0;
				npcClass.FLD_NEWTIME = 1;
				npcClass.QuaiXuatHien_DuyNhatMotLan = false;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.AddNpcToMapClass(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.AddNpcToMapClass(npcClass);
					World.MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.ScanNearbyPlayer();
				World.Npc_DuongDua_F1.Add(npcClass.NPC_SessionID, npcClass);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC cây Đường Đua - lỗi [" + 15278 + "]error：" + ex);
		}
	}

	public void Dispose()
	{
		try
		{
			List<NpcClass> list = new();
			foreach (var value in World.Npc_DuongDua_F1.Values)
			{
				list.Add(value);
			}
			if (list != null)
			{
				foreach (var item in list)
				{
					item.GuiDuLieu_TuVong_MotLanCuaQuaiVat();
				}
				list.Clear();
			}
			World.Npc_DuongDua_F1.Clear();
			World.Event_Duong_Dua_F1_Progress = 0;
			if (ThoiGian1 != null)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
			}
			if (ThoiGian2 != null)
			{
				ThoiGian2.Enabled = false;
				ThoiGian2.Close();
				ThoiGian2.Dispose();
			}
			World.Event_DuongDua_F1_ON_OFF = null;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Kết thúc Đường Đua F1 - Lỗi - " + ex.Message);
		}
	}
}
