﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>false</AvaloniaUseCompiledBindingsByDefault>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <!-- Debug Configuration - Disable AOT for Hot Reload support -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <PublishAot>false</PublishAot>
    <TrimMode>none</TrimMode>
    <!-- Enable Hot Reload -->
    <UseSharedCompilation>true</UseSharedCompilation>
    <EnableHotReload>true</EnableHotReload>
  </PropertyGroup>

  <!-- Release Configuration - Enable AOT for performance -->
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <PublishAot>true</PublishAot>
    <TrimMode>link</TrimMode>
    <PublishSingleFile>true</PublishSingleFile>
    <PublishReadyToRun>true</PublishReadyToRun>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.3.0" />
    <PackageReference Include="Avalonia.Desktop" Version="11.3.0" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.0" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.0" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Include="Avalonia.Diagnostics" Version="11.3.0">
      <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
      <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Grpc.Tools" Version="2.72.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2">
      <!-- <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets> -->
    </PackageReference>
    <!-- Configuration -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />

    <!-- Akka.NET -->
    <PackageReference Include="Akka" Version="1.5.42" />
    <PackageReference Include="Akka.Remote" Version="1.5.42" />
    <PackageReference Include="Akka.Streams" Version="1.5.42" />

    <!-- GRPC -->
    <PackageReference Include="Google.Protobuf" Version="3.31.1" />
    <PackageReference Include="Grpc.Net.Client" Version="2.71.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.6" />

    <!-- SeriLog -->

    <!-- FreeSql for PostgreSQL -->
    <PackageReference Include="FreeSql.Provider.PostgreSQL" Version="3.2.833" />
    <PackageReference Include="FreeSql.Repository" Version="3.2.833" />
    <PackageReference Include="FreeSql.DbContext" Version="3.2.833" />

    <!-- Caching and Redis -->
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.4" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />

    <!-- PostgreSQL Driver -->
    <PackageReference Include="Npgsql" Version="8.0.5" />

    <!-- Text Encoding Support -->
    <PackageReference Include="System.Text.Encoding.CodePages" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Protos\*.proto" GrpcServices="Client" />
  </ItemGroup>
</Project>
