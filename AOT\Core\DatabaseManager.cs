using System;
using System.Threading.Tasks;
using HeroYulgang.Services;

namespace HeroYulgang.Core
{
    /// <summary>
    /// Database Manager - Migrated từ EF Core sang ADO.NET thuần túy
    /// Tương thích với AOT compilation
    /// </summary>
    public class DatabaseManager
    {
        private static DatabaseManager? _instance;
        private readonly ConfigManager _configManager;
        private readonly AdoNetConnectionManager _connectionManager;

        // ADO.NET Repositories thay thế EF Core DbContexts

        public static DatabaseManager Instance => _instance ??= new DatabaseManager();


        // Legacy properties removed - Use repositories or AdoNetDBA directly

        private DatabaseManager()
        {
            _configManager = ConfigManager.Instance;
            _connectionManager = AdoNetConnectionManager.Instance;
        }

        public bool Initialize()
        {
            try
            {
                Console.WriteLine("DEBUG: DatabaseManager.Initialize() - Bắt đầu");

                // Test tất cả kết nối database
                Console.WriteLine("DEBUG: DatabaseManager.Initialize() - Bắt đầu test kết nối database");
                var result = InternalInitializeAsync().GetAwaiter().GetResult();
                Console.WriteLine("DEBUG: DatabaseManager.Initialize() - Hoàn thành GetAwaiter().GetResult()");

                if (!result)
                {
                    Console.WriteLine("DEBUG: DatabaseManager.Initialize() - Test kết nối thất bại");
                    Logger.Instance.Error("Không thể kết nối đến một hoặc nhiều database");
                    return false;
                }

                Console.WriteLine("DEBUG: DatabaseManager.Initialize() - Test kết nối thành công");
                Logger.Instance.Info("DatabaseManager (ADO.NET) đã được khởi tạo thành công");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG: DatabaseManager.Initialize() - Exception: {ex.Message}");
                Logger.Instance.Error($"Lỗi khi khởi tạo DatabaseManager: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> InternalInitializeAsync()
        {
            Console.WriteLine("DEBUG: DatabaseManager.InternalInitializeAsync() - Bắt đầu");
            var result = await _connectionManager.TestAllConnectionsAsync();
            Console.WriteLine("DEBUG: DatabaseManager.InternalInitializeAsync() - Hoàn thành");
            return result;
        }

        /// <summary>
        /// Initialize async version
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                // Test tất cả kết nối database
                if (!await _connectionManager.TestAllConnectionsAsync())
                {
                    Logger.Instance.Error("Không thể kết nối đến một hoặc nhiều database");
                    return false;
                }


                Logger.Instance.Info("DatabaseManager (ADO.NET) đã được khởi tạo thành công");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi tạo DatabaseManager: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Dispose resources - ADO.NET version
        /// </summary>
        public void Dispose()
        {
            try
            {

                Logger.Instance.Info("DatabaseManager (ADO.NET) đã được dispose");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dispose DatabaseManager: {ex.Message}");
            }
        }
    }
}
