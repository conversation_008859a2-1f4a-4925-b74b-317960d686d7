using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Services;
using HeroYulgang.Core.Native.Network;

namespace HeroYulgang.Core.Native.Packets
{
    /// <summary>
    /// Packet types - mở rộng từ enum hiện tại
    /// </summary>
    public enum PacketType : ushort
    {
        // Authentication packets
        Login = 1,
        Valid1375 = 1375,
        Heartbeat = 176,
        
        // Game packets
        Movement = 100,
        Chat = 200,
        Combat = 300,
        
        // System packets
        Disconnect = 999
    }

    /// <summary>
    /// Packet priority levels
    /// </summary>
    public enum PacketPriority
    {
        Low = 0,
        Normal = 1,
        High = 2,
        Critical = 3
    }

    /// <summary>
    /// Packet interface
    /// </summary>
    public interface IPacket
    {
        PacketType Type { get; }
        byte[] Data { get; }
        int Length { get; }
        PacketPriority Priority { get; }
        DateTime Timestamp { get; }
        int SessionId { get; }
    }

    /// <summary>
    /// Packet implementation
    /// </summary>
    public class Packet : IPacket
    {
        public PacketType Type { get; }
        public byte[] Data { get; }
        public int Length { get; }
        public PacketPriority Priority { get; }
        public DateTime Timestamp { get; }
        public int SessionId { get; }

        public Packet(PacketType type, byte[] data, int sessionId, PacketPriority priority = PacketPriority.Normal)
        {
            Type = type;
            Data = data;
            Length = data.Length;
            SessionId = sessionId;
            Priority = priority;
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// Parse packet từ raw data
        /// </summary>
        public static Packet Parse(byte[] buffer, int length, int sessionId)
        {
            if (length < 4)
                throw new ArgumentException("Packet too short");

            ushort type = BitConverter.ToUInt16(buffer, 8);
            var packetType = (PacketType)type;
            
            // Determine priority based on packet type
            var priority = GetPacketPriority(packetType);
            
            return new Packet(packetType, buffer, sessionId, priority);
        }

        /// <summary>
        /// Get packet priority based on type
        /// </summary>
        private static PacketPriority GetPacketPriority(PacketType type)
        {
            return type switch
            {
                PacketType.Login => PacketPriority.Critical,
                PacketType.Valid1375 => PacketPriority.Critical,
                PacketType.Heartbeat => PacketPriority.High,
                PacketType.Disconnect => PacketPriority.Critical,
                PacketType.Combat => PacketPriority.High,
                PacketType.Movement => PacketPriority.Normal,
                PacketType.Chat => PacketPriority.Low,
                _ => PacketPriority.Normal
            };
        }
    }

    /// <summary>
    /// Packet handler interface
    /// </summary>
    public interface IPacketHandler<in T> where T : IPacket
    {
        /// <summary>
        /// Handle packet
        /// </summary>
        Task HandleAsync(T packet, IPacketContext context);
        
        /// <summary>
        /// Check if handler can process this packet
        /// </summary>
        bool CanHandle(PacketType packetType);
    }

    /// <summary>
    /// Packet context - thông tin về session và connection
    /// </summary>
    public interface IPacketContext
    {
        /// <summary>
        /// Session ID
        /// </summary>
        int SessionId { get; }
        
        /// <summary>
        /// Client session
        /// </summary>
        IClientSession Session { get; }
        
        /// <summary>
        /// Send response packet
        /// </summary>
        Task SendAsync(byte[] data);
        
        /// <summary>
        /// Close connection
        /// </summary>
        Task CloseAsync();
        
        /// <summary>
        /// Session properties
        /// </summary>
        IDictionary<string, object> Properties { get; }
    }

    /// <summary>
    /// Packet processing pipeline interface - thay thế PacketHandlerActor
    /// </summary>
    public interface IPacketProcessor : IService
    {
        /// <summary>
        /// Process incoming packet
        /// </summary>
        Task ProcessAsync(IPacket packet, IPacketContext context);
        
        /// <summary>
        /// Register packet handler
        /// </summary>
        void RegisterHandler<T>(IPacketHandler<T> handler) where T : IPacket;
        
        /// <summary>
        /// Register packet handler với function
        /// </summary>
        void RegisterHandler<T>(Func<T, IPacketContext, Task> handler) where T : IPacket;
        
        /// <summary>
        /// Unregister packet handler
        /// </summary>
        void UnregisterHandler<T>() where T : IPacket;
        
        /// <summary>
        /// Get processing statistics
        /// </summary>
        PacketProcessingStatistics GetStatistics();
    }

    /// <summary>
    /// Priority packet queue interface
    /// </summary>
    public interface IPriorityPacketQueue
    {
        /// <summary>
        /// Enqueue packet với priority
        /// </summary>
        Task EnqueueAsync(IPacket packet, IPacketContext context);
        
        /// <summary>
        /// Dequeue packet với highest priority
        /// </summary>
        Task<(IPacket packet, IPacketContext context)?> DequeueAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Queue count
        /// </summary>
        int Count { get; }
        
        /// <summary>
        /// Clear queue
        /// </summary>
        void Clear();
    }

    /// <summary>
    /// Packet batch processor interface
    /// </summary>
    public interface IPacketBatchProcessor
    {
        /// <summary>
        /// Add packet to batch
        /// </summary>
        Task AddToBatchAsync(IPacket packet, IPacketContext context);
        
        /// <summary>
        /// Process batch
        /// </summary>
        Task ProcessBatchAsync();
        
        /// <summary>
        /// Batch size
        /// </summary>
        int BatchSize { get; set; }
        
        /// <summary>
        /// Batch timeout
        /// </summary>
        TimeSpan BatchTimeout { get; set; }
    }

    /// <summary>
    /// Packet processing statistics
    /// </summary>
    public class PacketProcessingStatistics
    {
        public long TotalPacketsProcessed { get; set; }
        public long PacketsPerSecond { get; set; }
        public long ErrorCount { get; set; }
        public TimeSpan AverageProcessingTime { get; set; }
        public int QueueSize { get; set; }
        public DateTime StartTime { get; set; }
        public TimeSpan Uptime => DateTime.UtcNow - StartTime;
        
        public Dictionary<PacketType, long> PacketTypeCounters { get; set; } = new();
        public Dictionary<PacketPriority, long> PriorityCounters { get; set; } = new();
    }

    /// <summary>
    /// Packet events
    /// </summary>
    public abstract class PacketEvent
    {
        public DateTime Timestamp { get; } = DateTime.UtcNow;
        public int SessionId { get; }
        public abstract string EventType { get; }
        
        protected PacketEvent(int sessionId)
        {
            SessionId = sessionId;
        }
    }

    public class PacketProcessedEvent : PacketEvent
    {
        public override string EventType => "PacketProcessed";
        public PacketType PacketType { get; }
        public TimeSpan ProcessingTime { get; }
        
        public PacketProcessedEvent(int sessionId, PacketType packetType, TimeSpan processingTime) : base(sessionId)
        {
            PacketType = packetType;
            ProcessingTime = processingTime;
        }
    }

    public class PacketErrorEvent : PacketEvent
    {
        public override string EventType => "PacketError";
        public PacketType PacketType { get; }
        public Exception Exception { get; }
        
        public PacketErrorEvent(int sessionId, PacketType packetType, Exception exception) : base(sessionId)
        {
            PacketType = packetType;
            Exception = exception;
        }
    }

    public class PacketQueueFullEvent : PacketEvent
    {
        public override string EventType => "PacketQueueFull";
        public int QueueSize { get; }
        
        public PacketQueueFullEvent(int sessionId, int queueSize) : base(sessionId)
        {
            QueueSize = queueSize;
        }
    }

    /// <summary>
    /// Packet processor configuration
    /// </summary>
    public class PacketProcessorConfiguration
    {
        /// <summary>
        /// Max queue size
        /// </summary>
        public int MaxQueueSize { get; set; } = 10000;
        
        /// <summary>
        /// Worker thread count
        /// </summary>
        public int WorkerThreadCount { get; set; } = Environment.ProcessorCount;
        
        /// <summary>
        /// Batch processing enabled
        /// </summary>
        public bool BatchProcessingEnabled { get; set; } = true;
        
        /// <summary>
        /// Batch size
        /// </summary>
        public int BatchSize { get; set; } = 100;
        
        /// <summary>
        /// Batch timeout
        /// </summary>
        public TimeSpan BatchTimeout { get; set; } = TimeSpan.FromMilliseconds(10);
        
        /// <summary>
        /// Enable packet logging
        /// </summary>
        public bool EnablePacketLogging { get; set; } = true;
        
        /// <summary>
        /// Processing timeout
        /// </summary>
        public TimeSpan ProcessingTimeout { get; set; } = TimeSpan.FromSeconds(30);
    }
}
