﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class log_deleteitem {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public int? maitem { get; set; }

		[JsonProperty]
		public int? iditem { get; set; }

		[JsonProperty]
		public DateTime? thoigian { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string levelitem { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string username { get; set; }

		[JsonProperty]
		public int? trangthai { get; set; }

	}

}
