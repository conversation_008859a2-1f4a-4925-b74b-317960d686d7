using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using HeroYulgang.Core.Native.Messaging;

namespace HeroYulgang.Tests.Core.Native.Messaging
{
    /// <summary>
    /// Tests for EventBus implementation
    /// </summary>
    [TestFixture]
    public class EventBusTests : TestBase
    {
        private IEventBus _eventBus = null!;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            _eventBus = ServiceProvider.GetRequiredService<IEventBus>();
        }

        [Test]
        public async Task Subscribe_And_Publish_Should_Deliver_Event()
        {
            // Arrange
            var receivedEvents = new List<TestEvent>();
            var eventReceived = new TaskCompletionSource<bool>();

            _eventBus.Subscribe<TestEvent>(evt =>
            {
                receivedEvents.Add(evt);
                eventReceived.TrySetResult(true);
                return Task.CompletedTask;
            });

            var testEvent = new TestEvent { Message = "Test Message", Value = 42 };

            // Act
            await _eventBus.PublishAsync(testEvent);

            // Assert
            await AssertTaskCompletesAsync(eventReceived.Task, TimeSpan.FromSeconds(1));
            Assert.AreEqual(1, receivedEvents.Count);
            Assert.AreEqual("Test Message", receivedEvents[0].Message);
            Assert.AreEqual(42, receivedEvents[0].Value);
        }

        [Test]
        public async Task Multiple_Subscribers_Should_All_Receive_Event()
        {
            // Arrange
            var subscriber1Events = new List<TestEvent>();
            var subscriber2Events = new List<TestEvent>();
            var subscriber3Events = new List<TestEvent>();
            
            var subscriber1Received = new TaskCompletionSource<bool>();
            var subscriber2Received = new TaskCompletionSource<bool>();
            var subscriber3Received = new TaskCompletionSource<bool>();

            _eventBus.Subscribe<TestEvent>(evt =>
            {
                subscriber1Events.Add(evt);
                subscriber1Received.TrySetResult(true);
                return Task.CompletedTask;
            });

            _eventBus.Subscribe<TestEvent>(evt =>
            {
                subscriber2Events.Add(evt);
                subscriber2Received.TrySetResult(true);
                return Task.CompletedTask;
            });

            _eventBus.Subscribe<TestEvent>(evt =>
            {
                subscriber3Events.Add(evt);
                subscriber3Received.TrySetResult(true);
                return Task.CompletedTask;
            });

            var testEvent = new TestEvent { Message = "Broadcast Test", Value = 123 };

            // Act
            await _eventBus.PublishAsync(testEvent);

            // Assert
            await AssertTaskCompletesAsync(subscriber1Received.Task, TimeSpan.FromSeconds(1));
            await AssertTaskCompletesAsync(subscriber2Received.Task, TimeSpan.FromSeconds(1));
            await AssertTaskCompletesAsync(subscriber3Received.Task, TimeSpan.FromSeconds(1));

            Assert.AreEqual(1, subscriber1Events.Count);
            Assert.AreEqual(1, subscriber2Events.Count);
            Assert.AreEqual(1, subscriber3Events.Count);

            Assert.AreEqual("Broadcast Test", subscriber1Events[0].Message);
            Assert.AreEqual("Broadcast Test", subscriber2Events[0].Message);
            Assert.AreEqual("Broadcast Test", subscriber3Events[0].Message);
        }

        [Test]
        public async Task Unsubscribe_Should_Stop_Receiving_Events()
        {
            // Arrange
            var receivedEvents = new List<TestEvent>();
            var eventReceived = new TaskCompletionSource<bool>();

            var subscription = _eventBus.Subscribe<TestEvent>(evt =>
            {
                receivedEvents.Add(evt);
                eventReceived.TrySetResult(true);
                return Task.CompletedTask;
            });

            // Act & Assert - First event should be received
            await _eventBus.PublishAsync(new TestEvent { Message = "First", Value = 1 });
            await AssertTaskCompletesAsync(eventReceived.Task, TimeSpan.FromSeconds(1));
            Assert.AreEqual(1, receivedEvents.Count);

            // Unsubscribe
            subscription.Dispose();

            // Second event should not be received
            await _eventBus.PublishAsync(new TestEvent { Message = "Second", Value = 2 });
            await Task.Delay(100); // Give time for potential delivery

            Assert.AreEqual(1, receivedEvents.Count); // Should still be 1
            Assert.AreEqual("First", receivedEvents[0].Message);
        }

        [Test]
        public async Task Exception_In_Handler_Should_Not_Affect_Other_Handlers()
        {
            // Arrange
            var goodHandlerEvents = new List<TestEvent>();
            var goodHandlerReceived = new TaskCompletionSource<bool>();

            // Handler that throws exception
            _eventBus.Subscribe<TestEvent>(evt =>
            {
                throw new InvalidOperationException("Test exception");
            });

            // Handler that works normally
            _eventBus.Subscribe<TestEvent>(evt =>
            {
                goodHandlerEvents.Add(evt);
                goodHandlerReceived.TrySetResult(true);
                return Task.CompletedTask;
            });

            var testEvent = new TestEvent { Message = "Exception Test", Value = 999 };

            // Act
            await _eventBus.PublishAsync(testEvent);

            // Assert - Good handler should still receive the event
            await AssertTaskCompletesAsync(goodHandlerReceived.Task, TimeSpan.FromSeconds(1));
            Assert.AreEqual(1, goodHandlerEvents.Count);
            Assert.AreEqual("Exception Test", goodHandlerEvents[0].Message);
        }

        [Test]
        public async Task Concurrent_Publishing_Should_Work_Correctly()
        {
            // Arrange
            var receivedEvents = new List<TestEvent>();
            var receivedCount = 0;
            var allEventsReceived = new TaskCompletionSource<bool>();
            var lockObject = new object();

            _eventBus.Subscribe<TestEvent>(evt =>
            {
                lock (lockObject)
                {
                    receivedEvents.Add(evt);
                    receivedCount++;
                    if (receivedCount >= 10)
                    {
                        allEventsReceived.TrySetResult(true);
                    }
                }
                return Task.CompletedTask;
            });

            // Act - Publish 10 events concurrently
            var publishTasks = new List<Task>();
            for (int i = 0; i < 10; i++)
            {
                var eventValue = i;
                publishTasks.Add(Task.Run(async () =>
                {
                    await _eventBus.PublishAsync(new TestEvent { Message = $"Event {eventValue}", Value = eventValue });
                }));
            }

            await Task.WhenAll(publishTasks);

            // Assert
            await AssertTaskCompletesAsync(allEventsReceived.Task, TimeSpan.FromSeconds(2));
            Assert.AreEqual(10, receivedEvents.Count);

            // Verify all events were received (order may vary due to concurrency)
            var receivedValues = receivedEvents.Select(e => e.Value).OrderBy(v => v).ToArray();
            var expectedValues = Enumerable.Range(0, 10).ToArray();
            CollectionAssert.AreEqual(expectedValues, receivedValues);
        }

        [Test]
        public async Task Different_Event_Types_Should_Be_Handled_Separately()
        {
            // Arrange
            var testEvents = new List<TestEvent>();
            var stringEvents = new List<string>();
            
            var testEventReceived = new TaskCompletionSource<bool>();
            var stringEventReceived = new TaskCompletionSource<bool>();

            _eventBus.Subscribe<TestEvent>(evt =>
            {
                testEvents.Add(evt);
                testEventReceived.TrySetResult(true);
                return Task.CompletedTask;
            });

            _eventBus.Subscribe<string>(evt =>
            {
                stringEvents.Add(evt);
                stringEventReceived.TrySetResult(true);
                return Task.CompletedTask;
            });

            // Act
            await _eventBus.PublishAsync(new TestEvent { Message = "Test Event", Value = 1 });
            await _eventBus.PublishAsync("String Event");

            // Assert
            await AssertTaskCompletesAsync(testEventReceived.Task, TimeSpan.FromSeconds(1));
            await AssertTaskCompletesAsync(stringEventReceived.Task, TimeSpan.FromSeconds(1));

            Assert.AreEqual(1, testEvents.Count);
            Assert.AreEqual(1, stringEvents.Count);
            Assert.AreEqual("Test Event", testEvents[0].Message);
            Assert.AreEqual("String Event", stringEvents[0]);
        }

        [Test]
        public async Task High_Volume_Publishing_Should_Maintain_Performance()
        {
            // Arrange
            const int eventCount = 1000;
            var receivedCount = 0;
            var allEventsReceived = new TaskCompletionSource<bool>();
            var lockObject = new object();

            _eventBus.Subscribe<TestEvent>(evt =>
            {
                lock (lockObject)
                {
                    receivedCount++;
                    if (receivedCount >= eventCount)
                    {
                        allEventsReceived.TrySetResult(true);
                    }
                }
                return Task.CompletedTask;
            });

            var startTime = DateTime.UtcNow;

            // Act - Publish many events
            var publishTasks = new List<Task>();
            for (int i = 0; i < eventCount; i++)
            {
                var eventValue = i;
                publishTasks.Add(_eventBus.PublishAsync(new TestEvent { Message = $"Event {eventValue}", Value = eventValue }));
            }

            await Task.WhenAll(publishTasks);

            // Assert
            await AssertTaskCompletesAsync(allEventsReceived.Task, TimeSpan.FromSeconds(10));
            
            var duration = DateTime.UtcNow - startTime;
            var eventsPerSecond = eventCount / duration.TotalSeconds;
            
            Assert.AreEqual(eventCount, receivedCount);
            Assert.Greater(eventsPerSecond, 100, "Should process at least 100 events per second");
            
            Console.WriteLine($"Processed {eventCount} events in {duration.TotalMilliseconds:F2}ms ({eventsPerSecond:F0} events/sec)");
        }

        [Test]
        public async Task Dispose_Should_Stop_Event_Processing()
        {
            // Arrange
            var receivedEvents = new List<TestEvent>();

            _eventBus.Subscribe<TestEvent>(evt =>
            {
                receivedEvents.Add(evt);
                return Task.CompletedTask;
            });

            // Act - Publish before dispose
            await _eventBus.PublishAsync(new TestEvent { Message = "Before Dispose", Value = 1 });
            await Task.Delay(50); // Allow processing

            // Dispose the event bus
            if (_eventBus is IDisposable disposable)
            {
                disposable.Dispose();
            }

            // Try to publish after dispose
            try
            {
                await _eventBus.PublishAsync(new TestEvent { Message = "After Dispose", Value = 2 });
            }
            catch
            {
                // Expected - event bus may throw after disposal
            }

            await Task.Delay(50); // Allow any potential processing

            // Assert
            Assert.AreEqual(1, receivedEvents.Count);
            Assert.AreEqual("Before Dispose", receivedEvents[0].Message);
        }
    }
}
