
using System;
using System.Text;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.Game;
using HeroYulgang.Helpers;
using HeroYulgang.Services;

namespace RxjhServer;

public partial class PlayersBes
{
       public int SetBaseStat(tbl_xwwl_char dbRow)
	{
		try
		{
			int num;
			GMMode = (int)dbRow.fld_j9;
			CharacterPosition = (int)dbRow.fld_index;
			int_16 = (int)dbRow.fld_fight_exp;
			long_0 = long.Parse(dbRow.fld_exp.ToString());
			try
			{
				long_2 = long.Parse(dbRow.fld_money.ToString());
				Old_Gold_One_Sec = long_2;
				int_108 = (int)dbRow.fld_whtb;
			}
			catch
			{
				long_2 = 0L;
				int_108 = 0;
			}
			int_40 = (int)dbRow.fld_point;
			int_41 = (int)dbRow.fld_zx;
			int_42 = (int)dbRow.fld_level;
			int_46 = (int)dbRow.fld_job;
			int_47 = (int)dbRow.fld_job_level;
			NewCharacterTemplate = new((byte[])dbRow.fld_face);
			_CharacterNameTemplate = (byte[])dbRow.fld_nametype;
			int_48 = NewCharacterTemplate.GioiTinh;
			int_20 = (int)dbRow.fld_hp;
			int_32 = (int)dbRow.fld_mp;
			int_38 = (int)dbRow.fld_sp;
			FLD_PVP_Piont = (int)dbRow.fld_pvp_piont;
			FLD_LoaiSanXuat = (int)dbRow.fld_zztype;
			FLD_TrinhDoSanXuat = (int)dbRow.fld_zzsl;
			float_0 = float.Parse(dbRow.fld_x.ToString());
			float_1 = float.Parse(dbRow.fld_y.ToString());
			float_2 = float.Parse(dbRow.fld_z.ToString());
			EquipmentDataVersion = (int)dbRow.fld_zbver;
			int_49 = (int)dbRow.fld_menow;
			if ((int_48 < 1 || int_48 > 2) && Client != null)
			{
				Client.Dispose();
				LogHelper.WriteLine(LogLevel.Error, "Disconnected![Mã dis 63]");
			}
			ToaDoNew = new(PosX, PosY, PosZ, MapID);
			Player_WuXun = (int)dbRow.fld_wx;
			NhanVatThienVaAc = (int)dbRow.fld_se;
			Character_KhinhCong = (int)dbRow.fld_jq;
			FLD_Couple = dbRow.fld_qlname.ToString();
			FLD_CoupleRing = dbRow.fld_qljzname.ToString();
			FLD_Couple_Love = (int)dbRow.fld_qldu;
			NhanCuoiKhacChu = dbRow.fld_love_word.ToString();
			GiaiTruQuanHe_Countdown = (int)dbRow.fld_marital_status;
			WhetherMarried = (int)dbRow.fld_married;
			ThangThienVoCong_DiemSo = (int)dbRow.fld_thangthienvocongdiemso;
			ThangThienLichLuyen_KinhNghiem = (int)dbRow.fld_thangthienlichluyen;
			if (World.Event_MoHop_SoLuong != 0)
			{
				FLD_NUMBER_OPEN = (int)dbRow.fld_mlz;
			}
			else
			{
				FLD_NUMBER_OPEN = 0;
			}
			TitlePoints = (int)dbRow.fld_titlepoints;
			dateTime_2 = (DateTime)dbRow.fld_jh_date;
			RemainingTimeOfTrainingMap = (int)dbRow.fld_fb_time;
			Player_Extra_Money_Level = (int)dbRow.fld_moneyextralevel;
			num = 39;
			ActivityMapRemainingTime = (int)dbRow.fld_hd_time;
			num = 40;
			MatDi_VoHuan = (int)dbRow.fld_lost_wx;
			num = 41;
			NhanVoHuan_MoiNgay = (int)dbRow.fld_get_wx;
			num = 42;
			_CharacterPartitionID = dbRow.fld_fqid;
			num = 43;
			RoseTitlePoints = (int)dbRow.fld_rosetitlepoints;
			num = 44;
			_BangPhai_DoCongHien = (int)dbRow.bangphai_doconghien;
			num = 45;
			ClientSettings = dbRow.fld_config.ToString();
			num = 46;
			ThanNuVoCongDiemSo = (int)dbRow.fld_thannuvocongdiemso;
			num = 47;
			TheLucChien_PhePhai = dbRow.tlc_random_phe.ToString();
			num = 48;
            if (World.TheLucChien_Progress < 1 && World.TheLucChien_Random_MoRa != 0)
            {
                TheLucChien_PhePhai = string.Empty;
                GameDb.UpdateFactionWar(CharacterName, AccountID, string.Empty).GetAwaiter().GetResult();
                //DBA.ExeSqlCommand($"UPDATE    [TBL_XWWL_Char]  SET  TLC_RANDOM_PHE='{string.Empty}'  WHERE  FLD_ID='{AccountID}'", "gamedb").GetAwaiter().GetResult();
            }
			int_109 = (int)dbRow.fld_add_hp;
			int_110 = Convert.ToInt32(dbRow.fld_add_at.ToString());
			int_111 = Convert.ToInt32(dbRow.fld_add_df.ToString());
			int_112 = (int)dbRow.fld_add_hb;
			int_113 = (int)dbRow.fld_add_mp;
			int_114 = (int)dbRow.fld_add_mz;
			int_115 = (int)dbRow.fld_add_clvc;
			int_116 = (int)dbRow.fld_add_ptvc;
			int_117 = (int)dbRow.fld_add_kc;
			_numberofrebirths = (int)dbRow.fld_zs;
            if (CharacterPartitionID != World.PartitionNumber)
            {
                _CharacterPartitionID = World.PartitionNumber;
                GameDb.UpdateFQID(CharacterName, AccountID, World.PartitionNumber).GetAwaiter().GetResult();
                //DBA.ExeSqlCommand($"UPDATE    [TBL_XWWL_Char]  SET  FLD_FQID='{World.PartitionNumber}'  WHERE  FLD_ID='{AccountID}'", "gamedb").GetAwaiter().GetResult();
            }
			if (Player_ExpErience < 0)
			{
				Player_ExpErience = 0;
			}
			if (Player_Qigong_point < 0)
			{
				Player_Qigong_point = 0;
			}

			return num;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "SetBaseStat Error: " + ex.Message);
			return 0;
		}
	}
      private void LoadPinkBag(tbl_xwwl_char dbRow)
    {
        var array60 = (byte[])dbRow.fld_pinkbag_item;
        for (var num37 = 0; num37 < 24; num37++)
        {
            var array61 = new byte[World.Item_Db_Byte_Length];
            if (array60.Length >= num37 * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
            {
                try
                {
                    System.Buffer.BlockCopy(array60, num37 * World.Item_Db_Byte_Length, array61, 0, World.Item_Db_Byte_Length);
                }
                catch (Exception ex15)
                {
                    LogHelper.WriteLine(LogLevel.Error, " --- " + ex15);
                }
            }
            PinkBag_24Slot[num37] = new(array61, num37);
            var array62 = new byte[4];
            System.Buffer.BlockCopy(PinkBag_24Slot[num37].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array62, 0, 4);
            var num38 = BitConverter.ToInt32(array62, 0);
            if (num38 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num38)).TotalSeconds >= 0.0)
            {
                HeThongNhacNho("Áo choàng hành lý có bảo vật quá hạn [" + PinkBag_24Slot[num37].GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Thiên cơ các");
                PinkBag_24Slot[num37].VatPham_byte = new byte[World.Item_Db_Byte_Length];
            }
        }
    }


    private byte[] LoadThoLinhPhu(tbl_xwwl_char dbRow)
    {
		byte[] array54 = null;
        if (dbRow.fld_doors != null)
		{
			array54 = (byte[])dbRow.fld_doors;
		}
        ThoLinhPhu_ToaDo.Clear();
        if (array54.Length >= 128)
        {
            for (var num36 = 0; num36 < 30; num36++)
            {
                try
                {
                    var array55 = new byte[4];
                    var array56 = new byte[4];
                    var array57 = new byte[4];
                    var array58 = new byte[4];
                    var array59 = new byte[14];
                    System.Buffer.BlockCopy(array54, num36 * 32 + 15, array58, 0, 4);
                    System.Buffer.BlockCopy(array54, num36 * 32 + 19, array55, 0, 4);
                    System.Buffer.BlockCopy(array54, num36 * 32 + 23, array56, 0, 4);
                    System.Buffer.BlockCopy(array54, num36 * 32 + 27, array57, 0, 4);
                    System.Buffer.BlockCopy(array54, num36 * 32, array59, 0, 14);
                    var rxjh_name = Encoding.Default.GetString(array59).Trim();
                    if ((double)BitConverter.ToSingle(array55, 0) != 0.0 || (double)BitConverter.ToSingle(array56, 0) != 0.0 || BitConverter.ToInt32(array58, 0) != 0)
                    {
                        X_Toa_Do_Class x_Toa_Do_Class = new(BitConverter.ToSingle(array55, 0), BitConverter.ToSingle(array56, 0), BitConverter.ToSingle(array57, 0), BitConverter.ToInt32(array58, 0));
                        x_Toa_Do_Class.Rxjh_name = rxjh_name;
                        if (ThoLinhPhu_ToaDo.ContainsKey(10 + num36))
                        {
                            ThoLinhPhu_ToaDo.Remove(10 + num36);
                        }
                        ThoLinhPhu_ToaDo.Add(10 + num36, x_Toa_Do_Class);
                    }
                }
                catch
                {
                    LogHelper.WriteLine(LogLevel.Error, " --- lỗi TLP tọa độ --- !!!");
                }
            }
        }

        return array54;
    }

    private void LoadCTime(tbl_xwwl_char dbRow, byte[] src)
    {
		PersonalMedicine = null;
        if (!dbRow.fld_ctime.Equals(null))
        {
            PersonalMedicine = (byte[])dbRow.fld_ctime;
        }
        if (!dbRow.fld_ctimenew.Equals(null))
        {
            AdditionalStatusItemsNew = (byte[])dbRow.fld_ctimenew;
        }
        var array51 = new byte[16];
        System.Buffer.BlockCopy(src, 0, array51, 0, 16);
        for (var num35 = 0; num35 < 2; num35++)
        {
            try
            {
                var array52 = new byte[4];
                var array53 = new byte[4];
                System.Buffer.BlockCopy(array51, num35 * 8, array52, 0, 4);
                System.Buffer.BlockCopy(array51, num35 * 8 + 4, array53, 0, 4);
                var dateTime = new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(BitConverter.ToInt32(array53, 0));
                if (!(dateTime < DateTime.Now) && BitConverter.ToInt32(array52, 0) > 0 && !PublicDrugs.ContainsKey(BitConverter.ToInt32(array52, 0)))
                {
                    X_Cong_Huu_Duoc_Pham_Loai x_Cong_Huu_Duoc_Pham_Loai = new();
                    x_Cong_Huu_Duoc_Pham_Loai.DuocPhamID = BitConverter.ToInt32(array52, 0);
                    x_Cong_Huu_Duoc_Pham_Loai.ThoiGian = BitConverter.ToUInt32(array53, 0);
                    PublicDrugs.Add(x_Cong_Huu_Duoc_Pham_Loai.DuocPhamID, x_Cong_Huu_Duoc_Pham_Loai);
                }
            }
            catch (Exception value7)
            {
                Console.WriteLine(value7);
            }
        }
    }

    private void LoadChTime(tbl_xwwl_char dbRow)
    {
        var array47 = (byte[])dbRow.fld_chtime;
        if (array47 != null && array47.Length > 1)
        {
            var array48 = new byte[320];
            System.Buffer.BlockCopy(array47, 0, array48, 0, 320);
            for (var num34 = 0; num34 < 80; num34++)
            {
                try
                {
                    var array49 = new byte[4];
                    var array50 = new byte[4];
                    System.Buffer.BlockCopy(array48, num34 * 8, array49, 0, 4);
                    System.Buffer.BlockCopy(array48, num34 * 8 + 4, array50, 0, 4);
                    if (BitConverter.ToInt32(array49, 0) > 0)
                    {
                        if (!TitleDrug.ContainsKey(BitConverter.ToInt32(array49, 0)))
                        {
                            X_Xung_Hao_Duoc_Pham_Loai x_Xung_Hao_Duoc_Pham_Loai = new();
                            x_Xung_Hao_Duoc_Pham_Loai.DuocPhamID = BitConverter.ToInt32(array49, 0);
                            x_Xung_Hao_Duoc_Pham_Loai.ThoiGian = BitConverter.ToUInt32(array50, 0);
                            TitleDrug.Add(x_Xung_Hao_Duoc_Pham_Loai.DuocPhamID, x_Xung_Hao_Duoc_Pham_Loai);
                        }
                        continue;
                    }
                }
                catch (Exception value6)
                {
                    Console.WriteLine(value6);
                    continue;
                }
                break;
            }
        }
    }

    private void LoadStime(tbl_xwwl_char dbRow)
    {
        var array43 = (byte[])dbRow.fld_stime;
        if (array43 != null && array43.Length > 1)
        {
            var array44 = new byte[16];
            System.Buffer.BlockCopy(array43, 0, array44, 0, 16);
            for (var num33 = 0; num33 < 40; num33++)
            {
                try
                {
                    var array45 = new byte[4];
                    var array46 = new byte[4];
                    System.Buffer.BlockCopy(array44, num33 * 8, array45, 0, 4);
                    System.Buffer.BlockCopy(array44, num33 * 8 + 4, array46, 0, 4);
                    if (BitConverter.ToInt32(array45, 0) > 0)
                    {
                        if (!TimeMedicine.ContainsKey(BitConverter.ToInt32(array45, 0)))
                        {
                            X_Thoi_Gian_Duoc_Pham_Loai x_Thoi_Gian_Duoc_Pham_Loai = new();
                            x_Thoi_Gian_Duoc_Pham_Loai.DuocPhamID = BitConverter.ToInt32(array45, 0);
                            x_Thoi_Gian_Duoc_Pham_Loai.ThoiGian = BitConverter.ToUInt32(array46, 0);
                            TimeMedicine.Add(x_Thoi_Gian_Duoc_Pham_Loai.DuocPhamID, x_Thoi_Gian_Duoc_Pham_Loai);
                        }
                        continue;
                    }
                }
                catch (Exception value5)
                {
                    Console.WriteLine(value5);
                    continue;
                }
                break;
            }
        }
    }

    private void LoadPublicWareHouse(out int num, out byte[] src)
    {
        var userPublicWarehouse = GameDb.GetAndCreatePublicWarehouse(AccountID).Result;
        if (userPublicWarehouse == null && Client != null)
        {
            Client.Dispose();
        }
        var array39 = (byte[])userPublicWarehouse.fld_item;
        ComprehensiveWarehouseEquipmentDataVersion = (int)userPublicWarehouse.fld_zbver;
        src = (byte[])userPublicWarehouse.fld_itime;
        try
        {
            ComprehensiveWarehouseMoney = long.Parse(userPublicWarehouse.fld_money.ToString());
        }
        catch
        {
            ComprehensiveWarehouseMoney = 0L;
        }
        for (var num31 = 0; num31 < 60; num31++)
        {
            var array40 = new byte[World.Item_Db_Byte_Length];
            if (array39.Length >= num31 * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
            {
                try
                {
                    System.Buffer.BlockCopy(array39, num31 * World.Item_Db_Byte_Length, array40, 0, World.Item_Db_Byte_Length);
                }
                catch (Exception value4)
                {
                    Console.WriteLine(value4);
                }
            }
            PublicWarehouse[num31] = new(array40, num31);
            if (PublicWarehouse[num31].GetVatPham_ID == 1008001507)
            {
                PhaiChangMangTheoAoChang_HanhLy = true;
            }
            var array41 = new byte[4];
            System.Buffer.BlockCopy(PublicWarehouse[num31].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array41, 0, 4);
            var num32 = BitConverter.ToInt32(array41, 0);
            if (num32 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num32)).TotalSeconds >= 0.0)
            {
                HeThongNhacNho("Kho công cộng có bảo vật quá hạn [" + PublicWarehouse[num31].GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Thiên cơ các");
                PublicWarehouse[num31].VatPham_byte = new byte[World.Item_Db_Byte_Length];
            }
            if (World.AllItmelog != 1)
            {
                continue;
            }
            try
            {
                if (PublicWarehouse[num31].DatDuocVatPhamViTriLoaiHinh() != 1 && PublicWarehouse[num31].DatDuocVatPhamViTriLoaiHinh() != 2 && PublicWarehouse[num31].DatDuocVatPhamViTriLoaiHinh() != 5 && PublicWarehouse[num31].DatDuocVatPhamViTriLoaiHinh() != 6)
                {
                    if (PublicWarehouse[num31].DatDuocVatPhamViTriLoaiHinh() != 4 && PublicWarehouse[num31].DatDuocVatPhamViTriLoaiHinh() == 12 && (PublicWarehouse[num31].ThuocTinh1.ThuocTinhLoaiHinh == 7 || PublicWarehouse[num31].ThuocTinh2.ThuocTinhLoaiHinh == 7 || PublicWarehouse[num31].ThuocTinh3.ThuocTinhLoaiHinh == 7 || PublicWarehouse[num31].ThuocTinh4.ThuocTinhLoaiHinh == 7))
                    {
                        LogHelper.WriteLine(LogLevel.Debug, "Đã tìm thấy bảo vệ WG VatPham 999 PublicWarehouse [" + AccountID + "]-[" + CharacterName + "]  Position[" + num31 + "]  编号[" + BitConverter.ToInt32(PublicWarehouse[num31].ItemGlobal_ID, 0) + "]  VatPhamTen称[" + PublicWarehouse[num31].GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(PublicWarehouse[num31].VatPhamSoLuong, 0) + "]  ThuocTinh:[" + PublicWarehouse[num31].FLD_MAGIC0 + "," + PublicWarehouse[num31].FLD_MAGIC1 + "," + PublicWarehouse[num31].FLD_MAGIC2 + "," + PublicWarehouse[num31].FLD_MAGIC3 + "," + PublicWarehouse[num31].FLD_MAGIC4 + "]");
                        PublicWarehouse[num31].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                    }
                }
                else if (PublicWarehouse[num31].ThuocTinh1.ThuocTinhLoaiHinh == 7 || PublicWarehouse[num31].ThuocTinh2.ThuocTinhLoaiHinh == 7 || PublicWarehouse[num31].ThuocTinh3.ThuocTinhLoaiHinh == 7 || PublicWarehouse[num31].ThuocTinh4.ThuocTinhLoaiHinh == 7)
                {
                    LogHelper.WriteLine(LogLevel.Debug, "Đã tìm thấy bảo vệ WG VatPham 000 PublicWarehouse [" + AccountID + "]-[" + CharacterName + "]  Position[" + num31 + "]  编号[" + BitConverter.ToInt32(PublicWarehouse[num31].ItemGlobal_ID, 0) + "]  VatPhamTen称[" + PublicWarehouse[num31].GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(PublicWarehouse[num31].VatPhamSoLuong, 0) + "]  ThuocTinh:[" + PublicWarehouse[num31].FLD_MAGIC0 + "," + PublicWarehouse[num31].FLD_MAGIC1 + "," + PublicWarehouse[num31].FLD_MAGIC2 + "," + PublicWarehouse[num31].FLD_MAGIC3 + "," + PublicWarehouse[num31].FLD_MAGIC4 + "]");
                    PublicWarehouse[num31].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                }
            }
            catch (Exception ex13)
            {
                var array42 = new string[24]
                {
                        "查VatPham错误                PersonalWarehouse  错误  [",
                        AccountID,
                        "]-[",
                        CharacterName,
                        "]  Position[",
                        num31.ToString(),
                        "]  编号[",
                        BitConverter.ToInt32(PublicWarehouse[num31].ItemGlobal_ID, 0).ToString(),
                        "]  VatPhamTen称[",
                        PublicWarehouse[num31].GetItemName(),
                        "]  VatPhamSoLuong[",
                        BitConverter.ToInt32(PublicWarehouse[num31].VatPhamSoLuong, 0).ToString(),
                        "]  ThuocTinh:[",
                        PublicWarehouse[num31].FLD_MAGIC0.ToString(),
                        ",",
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null
                };
                array42[15] = PublicWarehouse[num31].FLD_MAGIC1.ToString();
                array42[16] = ",";
                array42[17] = PublicWarehouse[num31].FLD_MAGIC2.ToString();
                array42[18] = ",";
                array42[19] = PublicWarehouse[num31].FLD_MAGIC3.ToString();
                array42[20] = ",";
                array42[21] = PublicWarehouse[num31].FLD_MAGIC4.ToString();
                array42[22] = "]";
                array42[23] = ex13?.ToString();
                LogHelper.WriteLine(LogLevel.Error, string.Concat(array42));
            }
        }
        num = 61;
    }

    private async Task LoadCharacterWareHouse()
    {
		//var userWarehouse = RxjhClass.GetUserWarehouse(AccountID, CharacterName);
		var userWarehouse = await GameDb.GetAndCreateWarehouse(AccountID, CharacterName);
		if (userWarehouse == null && Client != null)
		{
			Client.Dispose();
			LogHelper.WriteLine(LogLevel.Error, "Disconnected![Lấy dữ liệu kho online phạm sai lầm]");
		}
        if (userWarehouse == null && Client != null)
        {
            Client.Dispose();
            LogHelper.WriteLine(LogLevel.Error, "Disconnected![Mã dis 66]");
        }
        var array35 = (byte[])userWarehouse.fld_item;
        try
        {
            PersonalWarehouseMoney = long.Parse(userWarehouse.fld_money.ToString());
        }
        catch
        {
            PersonalWarehouseMoney = 0L;
        }
        for (var num29 = 0; num29 < 60; num29++)
        {
            var array36 = new byte[World.Item_Db_Byte_Length];
            if (array35.Length >= num29 * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
            {
                try
                {
                    System.Buffer.BlockCopy(array35, num29 * World.Item_Db_Byte_Length, array36, 0, World.Item_Db_Byte_Length);
                }
                catch (Exception value3)
                {
                    Console.WriteLine(value3);
                }
            }
            PersonalWarehouse[num29] = new(array36, num29);
            if (PersonalWarehouse[num29].GetVatPham_ID == 1008001507)
            {
                PhaiChangMangTheoAoChang_HanhLy = true;
            }
            var array37 = new byte[4];
            System.Buffer.BlockCopy(PersonalWarehouse[num29].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array37, 0, 4);
            var num30 = BitConverter.ToInt32(array37, 0);
            if (num30 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num30)).TotalSeconds >= 0.0)
            {
                HeThongNhacNho("Kho cá nhân có bảo vật quá hạn [" + PersonalWarehouse[num29].GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Thiên cơ các");
                PersonalWarehouse[num29].VatPham_byte = new byte[World.Item_Db_Byte_Length];
            }
        }
    }

 	private async void LoadBirdMail()
	{
		if (DanhSach_TruyenThu != null)
		{
			DanhSach_TruyenThu.Clear();
		}
		var birdMails = await GameDb.LoadBirdMailList(CharacterName);
		if (birdMails != null)
		{
			foreach (var mail in birdMails)
			{
                X_Nguoi_Truyen_Thu_Loai x_Nguoi_Truyen_Thu_Loai = new()
                {
                    TruyenThuID = mail.id ?? 0,
                    CoPhaiLaNPC = mail.guithu_npc ?? 0,
                    TruyenThuNguoiGui = mail.nguoiguithu_ten ?? string.Empty,
                    TruyenThuNoiDung = mail.truyenthunoidung ?? string.Empty,
                    TruyenThuThoiGian = mail.truyenthuthoigian ?? DateTime.Now,
                    DaXemHayChua = mail.danhdaudaxem ?? 0
                };
                DanhSach_TruyenThu.Add(x_Nguoi_Truyen_Thu_Loai.TruyenThuID, x_Nguoi_Truyen_Thu_Loai);
			}
		}
		// var dataTable3 = RxjhClass.DatDuocDanhSach_TruyenThu(CharacterName);
		// if (dataTable3 != null)
		// {
		// 	for (var num28 = 0; num28 < dataTable3.Rows.Count; num28++)
		// 	{
		// 		X_Nguoi_Truyen_Thu_Loai x_Nguoi_Truyen_Thu_Loai = new();
		// 		x_Nguoi_Truyen_Thu_Loai.TruyenThuID = (int)dataTable3.Rows[num28]["ID"];
		// 		x_Nguoi_Truyen_Thu_Loai.CoPhaiLaNPC = (int)dataTable3.Rows[num28]["GuiThu_NPC"];
		// 		x_Nguoi_Truyen_Thu_Loai.TruyenThuNguoiGui = dataTable3.Rows[num28]["NguoiGuiThu_Ten"].ToString();
		// 		x_Nguoi_Truyen_Thu_Loai.TruyenThuNoiDung = dataTable3.Rows[num28]["TruyenThuNoiDung"].ToString();
		// 		x_Nguoi_Truyen_Thu_Loai.TruyenThuThoiGian = DateTime.Parse(dataTable3.Rows[num28]["TruyenThuThoiGian"].ToString());
		// 		x_Nguoi_Truyen_Thu_Loai.DaXemHayChua = (int)dataTable3.Rows[num28]["DanhDauDaXem"];
		// 		DanhSach_TruyenThu.Add(x_Nguoi_Truyen_Thu_Loai.TruyenThuID, x_Nguoi_Truyen_Thu_Loai);
		// 	}
		// 	dataTable3.Dispose();
		// }
	}

	private void LoadPlayerCompletedQuest(tbl_xwwl_char dbRow)
	{
		var array32 = (byte[])dbRow.fld_quest_finish;
		for (var num25 = 0; num25 < 500; num25++)
		{
			var array33 = new byte[2];
			var array34 = new byte[4];
			try
			{
				if (array32.Length >= num25 * 2 + 6)
				{
					System.Buffer.BlockCopy(array32, num25 * 2, array33, 0, 2);
					System.Buffer.BlockCopy(array32, num25 * 2 + 2, array34, 0, 4);
					int num26 = BitConverter.ToInt16(array33, 0);
					var num27 = BitConverter.ToInt32(array34, 0);
					var text = "";
					var text2 = "";
					var text3 = "";
					if (num27.ToString().Length == 8)
					{
						text2 = num27.ToString().Substring(0, 2);
						text = num27.ToString().Substring(2, 2);
						text3 = num27.ToString().Substring(4, 4);
					}
					if (num27.ToString().Length == 7)
					{
						text2 = int.Parse(num27.ToString().Substring(0, 1)).ToString("00");
						text = num27.ToString().Substring(1, 2);
						text3 = num27.ToString().Substring(3, 4);
					}
					var nhiemvudate = DateTime.ParseExact(text + "/" + text2 + "/" + text3, "dd/MM/yyyy", null);
					if (num26 != 0)
					{
						X_Nhiem_Vu_Theo_Ngay x_Nhiem_Vu_Theo_Ngay = new();
						x_Nhiem_Vu_Theo_Ngay.nhiemvuid = num26;
						x_Nhiem_Vu_Theo_Ngay.nhiemvudate = nhiemvudate;
						CompletedQuestList.Add(num26, x_Nhiem_Vu_Theo_Ngay);
					}
					continue;
				}
			}
			catch
			{
				continue;
			}
			break;
		}
	}

	private void LoadPlayerQuest(tbl_xwwl_char dbRow)
	{
		var array30 = (byte[])dbRow.fld_quest;
		for (var num24 = 0; num24 < 100; num24++)
		{
			var array31 = new byte[4];
			try
			{
				if (array30.Length < num24 * 4 + 4)
				{
					break;
				}
				System.Buffer.BlockCopy(array30, num24 * 4, array31, 0, 4);
				X_Nhiem_Vu_Loai x_Nhiem_Vu_Loai = new(array31);
				if (x_Nhiem_Vu_Loai.NhiemVuID == 0 || QuestList.ContainsKey(x_Nhiem_Vu_Loai.NhiemVuID))
				{
					continue;
				}
				if (x_Nhiem_Vu_Loai.NhiemVuID == 1200 || x_Nhiem_Vu_Loai.NhiemVuID == 1201 || x_Nhiem_Vu_Loai.NhiemVuID == 1203)
				{
					var rW = new X_Nhiem_Vu_Loai().GetRW(x_Nhiem_Vu_Loai.NhiemVuID);
					rW.NhiemVuID = x_Nhiem_Vu_Loai.NhiemVuID;
					rW.RwID = x_Nhiem_Vu_Loai.NhiemVuID;
					rW.NhiemVuGiaiDoanID = x_Nhiem_Vu_Loai.NhiemVuGiaiDoanID;
					if (x_Nhiem_Vu_Loai.NhiemVuGiaiDoanID < rW.NhiemVu_GiaiDoan.Count)
					{
						rW.NhiemVu_GiaiDoan[x_Nhiem_Vu_Loai.NhiemVuGiaiDoanID].GiaiDoan_TrangThai = 1;
					}
					else
					{
						rW.NhiemVu_GiaiDoan[x_Nhiem_Vu_Loai.NhiemVuGiaiDoanID - 1].GiaiDoan_TrangThai = 1;
					}
					QuestList.Add(x_Nhiem_Vu_Loai.NhiemVuID, rW);
				}
				else
				{
					QuestList.Add(x_Nhiem_Vu_Loai.NhiemVuID, x_Nhiem_Vu_Loai);
				}
				continue;
			}
			catch (Exception value2)
			{
				Console.WriteLine(value2);
				continue;
			}
		}
	}

	private void LoadWearItem(tbl_xwwl_char dbRow)
	{
		var array21 = (byte[])dbRow.fld_wearitem;
		for (var num19 = 0; num19 < 47; num19++)
		{
			var array22 = new byte[World.Item_Db_Byte_Length];
			if (num19 >= 16 && num19 < 31)
			{
				if (array21.Length >= num19 * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
				{
					try
					{
						System.Buffer.BlockCopy(array21, num19 * World.Item_Db_Byte_Length, array22, 0, World.Item_Db_Byte_Length);
					}
					catch (Exception ex5)
					{
						LogHelper.WriteLine(LogLevel.Error, " --- " + ex5.ToString());
					}
				}
				Sub_Wear[num19 - 16] = new(array22, num19 - 16);
				var array23 = new byte[4];
				System.Buffer.BlockCopy(Sub_Wear[num19 - 16].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array23, 0, 4);
				var num20 = BitConverter.ToInt32(array23, 0);
				if (num20 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num20)).TotalSeconds >= 0.0)
				{
					HeThongNhacNho("Trang bị xuyên thanh có bảo vật quá hạn [" + Sub_Wear[num19 - 16].GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Truyền Âm Các");
					Sub_Wear[num19 - 16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
				if (World.AllItmelog != 1)
				{
					continue;
				}
				try
				{
					if (Sub_Wear[num19 - 16].DatDuocVatPhamViTriLoaiHinh() != 1 && Sub_Wear[num19 - 16].DatDuocVatPhamViTriLoaiHinh() != 2 && Sub_Wear[num19 - 16].DatDuocVatPhamViTriLoaiHinh() != 5 && Sub_Wear[num19 - 16].DatDuocVatPhamViTriLoaiHinh() != 6)
					{
						if (Sub_Wear[num19 - 16].DatDuocVatPhamViTriLoaiHinh() != 4 && Sub_Wear[num19 - 16].DatDuocVatPhamViTriLoaiHinh() == 12 && (Sub_Wear[num19 - 16].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Sub_Wear[num19 - 16].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Sub_Wear[num19 - 16].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Sub_Wear[num19 - 16].ThuocTinh4.ThuocTinhLoaiHinh == 7))
						{
							LogHelper.WriteLine(LogLevel.Debug, "发现WG防VatPham  ThietBiPhuTro  [" + AccountID + "]-[" + CharacterName + "]  Position[" + (num19 - 16) + "]  编号[" + BitConverter.ToInt32(Sub_Wear[num19 - 16].ItemGlobal_ID, 0) + "]  VatPhamTen称[" + Sub_Wear[num19 - 16].GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(Sub_Wear[num19 - 16].VatPhamSoLuong, 0) + "]  ThuocTinh:[" + Sub_Wear[num19 - 16].FLD_MAGIC0 + "," + Sub_Wear[num19 - 16].FLD_MAGIC1 + "," + Sub_Wear[num19 - 16].FLD_MAGIC2 + "," + Sub_Wear[num19 - 16].FLD_MAGIC3 + "," + Sub_Wear[num19 - 16].FLD_MAGIC4 + "]");
							Sub_Wear[num19 - 16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						}
					}
					else if (Sub_Wear[num19 - 16].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Sub_Wear[num19 - 16].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Sub_Wear[num19 - 16].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Sub_Wear[num19 - 16].ThuocTinh4.ThuocTinhLoaiHinh == 7)
					{
						LogHelper.WriteLine(LogLevel.Debug, "发现WG防VatPham  ThietBiPhuTro  [" + AccountID + "]-[" + CharacterName + "]  Position[" + (num19 - 16) + "]  编号[" + BitConverter.ToInt32(Sub_Wear[num19 - 16].ItemGlobal_ID, 0) + "]  VatPhamTen称[" + Sub_Wear[num19 - 16].GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(Sub_Wear[num19 - 16].VatPhamSoLuong, 0) + "]  ThuocTinh:[" + Sub_Wear[num19 - 16].FLD_MAGIC0 + "," + Sub_Wear[num19 - 16].FLD_MAGIC1 + "," + Sub_Wear[num19 - 16].FLD_MAGIC2 + "," + Sub_Wear[num19 - 16].FLD_MAGIC3 + "," + Sub_Wear[num19 - 16].FLD_MAGIC4 + "]");
						Sub_Wear[num19 - 16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
				}
				catch (Exception ex6)
				{
					var array24 = new string[24]
					{
							"查VatPham错误        EquipmentBarPackage  错误  [",
							AccountID,
							"]-[",
							CharacterName,
							"]  Position[",
							(num19 - 16).ToString(),
							"]  编号[",
							BitConverter.ToInt32(Sub_Wear[num19 - 16].ItemGlobal_ID, 0).ToString(),
							"]  VatPhamTen称[",
							Sub_Wear[num19 - 16].GetItemName(),
							"]  VatPhamSoLuong[",
							BitConverter.ToInt32(Sub_Wear[num19 - 16].VatPhamSoLuong, 0).ToString(),
							"]  ThuocTinh:[",
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null
					};
					array24[13] = Sub_Wear[num19 - 16].FLD_MAGIC0.ToString();
					array24[14] = ",";
					array24[15] = Sub_Wear[num19 - 16].FLD_MAGIC1.ToString();
					array24[16] = ",";
					array24[17] = Sub_Wear[num19 - 16].FLD_MAGIC2.ToString();
					array24[18] = ",";
					array24[19] = Sub_Wear[num19 - 16].FLD_MAGIC3.ToString();
					array24[20] = ",";
					array24[21] = Sub_Wear[num19 - 16].FLD_MAGIC4.ToString();
					array24[22] = "]";
					array24[23] = ex6?.ToString();
					LogHelper.WriteLine(LogLevel.Error, string.Concat(array24));
				}
			}
			else if (num19 >= 31 && num19 < 37)
			{
				if (array21.Length >= num19 * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
				{
					try
					{
						System.Buffer.BlockCopy(array21, num19 * World.Item_Db_Byte_Length, array22, 0, World.Item_Db_Byte_Length);
					}
					catch (Exception ex7)
					{
						LogHelper.WriteLine(LogLevel.Error, "  " + ex7.ToString());
					}
				}
				ThietBiTab3[num19 - 31] = new(array22, num19 - 31);
				var array25 = new byte[4];
				System.Buffer.BlockCopy(ThietBiTab3[num19 - 31].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array25, 0, 4);
				var num21 = BitConverter.ToInt32(array25, 0);
				if (num21 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num21)).TotalSeconds >= 0.0)
				{
					HeThongNhacNho("Trang bị xuyên thanh có bảo vật quá hạn [" + ThietBiTab3[num19 - 31].GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Truyền Âm Các");
					ThietBiTab3[num19 - 31].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
			}
			else if (num19 == 46)
			{
				if (array21.Length >= num19 * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
				{
					try
					{
						System.Buffer.BlockCopy(array21, num19 * World.Item_Db_Byte_Length, array22, 0, World.Item_Db_Byte_Length);
					}
					catch (Exception ex8)
					{
						LogHelper.WriteLine(LogLevel.Error, "  " + ex8);
					}
				}
				Item_Wear[16] = new(array22, 16);
				var array26 = new byte[4];
				System.Buffer.BlockCopy(Item_Wear[16].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array26, 0, 4);
				var num22 = BitConverter.ToInt32(array26, 0);
				if (num22 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num22)).TotalSeconds >= 0.0)
				{
					HeThongNhacNho("Trang bị trong ô trang bị có bảo vật quá hạn [" + Item_Wear[16].GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Truyền Âm Các");
					Item_Wear[16].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}

			}
			else
			{
				if (num19 >= 16)
				{
					continue;
				}
				if (array21.Length >= num19 * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
				{
					try
					{
						System.Buffer.BlockCopy(array21, num19 * World.Item_Db_Byte_Length, array22, 0, World.Item_Db_Byte_Length);
					}
					catch (Exception ex10)
					{
						LogHelper.WriteLine(LogLevel.Error, "  " + ex10);
					}
				}
				Item_Wear[num19] = new(array22, num19);
				var array28 = new byte[4];
				System.Buffer.BlockCopy(Item_Wear[num19].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array28, 0, 4);
				var num23 = BitConverter.ToInt32(array28, 0);
				if (num23 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num23)).TotalSeconds >= 0.0)
				{
					HeThongNhacNho("Trang bị trong ô trang bị có bảo vật quá hạn [" + Item_Wear[num19].GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Truyền Âm Các");
					Item_Wear[num19].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
			}
		}
	}

	private void LoadCostumeBag(tbl_xwwl_char dbRow)
	{
		var array18 = (byte[])dbRow.fld_fashion_item;
		for (var num17 = 0; num17 < 66; num17++)
		{
			var array19 = new byte[World.Item_Db_Byte_Length];
			if (array18.Length >= num17 * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
			{
				try
				{
					System.Buffer.BlockCopy(array18, num17 * World.Item_Db_Byte_Length, array19, 0, World.Item_Db_Byte_Length);
				}
				catch (Exception ex4)
				{
					LogHelper.WriteLine(LogLevel.Error, " --- " + ex4);
				}
			}
			AoChang_HanhLy[num17] = new(array19, num17);
			var array20 = new byte[4];
			System.Buffer.BlockCopy(AoChang_HanhLy[num17].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array20, 0, 4);
			var num18 = BitConverter.ToInt32(array20, 0);
			if (num18 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num18)).TotalSeconds >= 0.0)
			{
				HeThongNhacNho("Áo choàng hành lý có bảo vật quá hạn [" + AoChang_HanhLy[num17].GetItemName() + "], đã bị Thiên cơ các tiêu hủy!!", 20, "Thiên cơ các");
				AoChang_HanhLy[num17].VatPham_byte = new byte[World.Item_Db_Byte_Length];
			}
		}
	}

	private int LoadItemInBag(tbl_xwwl_char dbRow)
	{
		int num;
		var array14 = (byte[])dbRow.fld_item;
		num = 531;
		for (var num15 = 0; num15 < 96; num15++)
		{
			num = 532;
			var array15 = new byte[World.Item_Db_Byte_Length];
			if (array14.Length >= num15 * World.Item_Db_Byte_Length + World.Item_Db_Byte_Length)
			{
				try
				{
					System.Buffer.BlockCopy(array14, num15 * World.Item_Db_Byte_Length, array15, 0, World.Item_Db_Byte_Length);
				}
				catch (Exception ex2)
				{
					LogHelper.WriteLine(LogLevel.Error, "Lỗi nè !! - " + ex2.ToString());
				}
			}
			Item_In_Bag[num15] = new(array15, num15);
			if (Item_In_Bag[num15].GetVatPham_ID == 1008001507)
			{
				PhaiChangMangTheoAoChang_HanhLy = true;
			}
			if (num15 < 36)
			{
				int_50 += Item_In_Bag[num15].VatPham_TongTrongLuong;
			}
			var array16 = new byte[4];
			System.Buffer.BlockCopy(Item_In_Bag[num15].VatPham_byte, World.VatPham_ThuocTinh_KichThuoc, array16, 0, 4);
			var num16 = BitConverter.ToInt32(array16, 0);
			if (num16 > 0 && DateTime.Now.Subtract(new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(num16)).TotalSeconds >= 0.0)
			{
				HeThongNhacNho("Ba lô có vật phẩm quá thời hạn [" + Item_In_Bag[num15].GetItemName() + "], Thiên cơ các đã xóa bỏ !!", 20, "Thiên cơ các");
				Item_In_Bag[num15].VatPham_byte = new byte[World.Item_Db_Byte_Length];
			}
			if (World.AllItmelog != 1)
			{
				continue;
			}
			try
			{
				if (Item_In_Bag[num15].DatDuocVatPhamViTriLoaiHinh() != 1 && Item_In_Bag[num15].DatDuocVatPhamViTriLoaiHinh() != 2 && Item_In_Bag[num15].DatDuocVatPhamViTriLoaiHinh() != 5 && Item_In_Bag[num15].DatDuocVatPhamViTriLoaiHinh() != 6)
				{
					if (Item_In_Bag[num15].DatDuocVatPhamViTriLoaiHinh() != 4 && Item_In_Bag[num15].DatDuocVatPhamViTriLoaiHinh() == 12 && (Item_In_Bag[num15].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Item_In_Bag[num15].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Item_In_Bag[num15].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Item_In_Bag[num15].ThuocTinh4.ThuocTinhLoaiHinh == 7))
					{
						LogHelper.WriteLine(LogLevel.Debug, "Đã tìm thấy bảo vệ WG VatPham 111 Equipment Bar Package [" + AccountID + "]-[" + CharacterName + "] Position[" + num15 + "]  编号[" + BitConverter.ToInt32(Item_In_Bag[num15].ItemGlobal_ID, 0) + "]  VatPhamTen称[" + Item_In_Bag[num15].GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(Item_In_Bag[num15].VatPhamSoLuong, 0) + "]  ThuocTinh:[" + Item_In_Bag[num15].FLD_MAGIC0 + "," + Item_In_Bag[num15].FLD_MAGIC1 + "," + Item_In_Bag[num15].FLD_MAGIC2 + "," + Item_In_Bag[num15].FLD_MAGIC3 + "," + Item_In_Bag[num15].FLD_MAGIC4 + "]");
						Item_In_Bag[num15].VatPham_byte = new byte[World.Item_Db_Byte_Length];
					}
				}
				else if (Item_In_Bag[num15].ThuocTinh1.ThuocTinhLoaiHinh == 7 || Item_In_Bag[num15].ThuocTinh2.ThuocTinhLoaiHinh == 7 || Item_In_Bag[num15].ThuocTinh3.ThuocTinhLoaiHinh == 7 || Item_In_Bag[num15].ThuocTinh4.ThuocTinhLoaiHinh == 7)
				{
					LogHelper.WriteLine(LogLevel.Debug, "Đã tìm thấy bảo vệ WG VatPham 222 Equipment Bar Package [" + AccountID + "]-[" + CharacterName + "] Position[" + num15 + "]  编号[" + BitConverter.ToInt32(Item_In_Bag[num15].ItemGlobal_ID, 0) + "]  VatPhamTen称[" + Item_In_Bag[num15].GetItemName() + "]  VatPhamSoLuong[" + BitConverter.ToInt32(Item_In_Bag[num15].VatPhamSoLuong, 0) + "]  ThuocTinh:[" + Item_In_Bag[num15].FLD_MAGIC0 + "," + Item_In_Bag[num15].FLD_MAGIC1 + "," + Item_In_Bag[num15].FLD_MAGIC2 + "," + Item_In_Bag[num15].FLD_MAGIC3 + "," + Item_In_Bag[num15].FLD_MAGIC4 + "]");
					Item_In_Bag[num15].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				}
			}
			catch (Exception ex3)
			{
				var array17 = new string[24]
				{
						"查VatPham错误        EquipmentBarPackage  错误[",
						AccountID,
						"]-[",
						CharacterName,
						"]  Position[",
						num15.ToString(),
						"]  编号[",
						BitConverter.ToInt32(Item_In_Bag[num15].ItemGlobal_ID, 0).ToString(),
						"]  VatPhamTen称[",
						Item_In_Bag[num15].GetItemName(),
						"]  VatPhamSoLuong[",
						BitConverter.ToInt32(Item_In_Bag[num15].VatPhamSoLuong, 0).ToString(),
						"]  ThuocTinh:[",
						Item_In_Bag[num15].FLD_MAGIC0.ToString(),
						",",
						null,
						null,
						null,
						null,
						null,
						null,
						null,
						null,
						null
				};
				array17[15] = Item_In_Bag[num15].FLD_MAGIC1.ToString();
				array17[16] = ",";
				array17[17] = Item_In_Bag[num15].FLD_MAGIC2.ToString();
				array17[18] = ",";
				array17[19] = Item_In_Bag[num15].FLD_MAGIC3.ToString();
				array17[20] = ",";
				array17[21] = Item_In_Bag[num15].FLD_MAGIC4.ToString();
				array17[22] = "]";
				array17[23] = ex3.ToString();
				LogHelper.WriteLine(LogLevel.Error, string.Concat(array17));
			}
		}

		return num;
	}

	private void LoadQuestBag(tbl_xwwl_char dbRow)
	{
		var array12 = (byte[])dbRow.fld_qitem;
		for (var num14 = 0; num14 < 36; num14++)
		{
			var array13 = new byte[8];
			if (array12.Length >= num14 * 8 + 8)
			{
				Buffer.BlockCopy(array12, num14 * 8, array13, 0, 8);

			}
			NhiemVu_VatPham[num14] = new(array13);
		}
	}

	public void LoadAscentionMagic(tbl_xwwl_char dbRow)
	{
		try
		{
			if (dbRow.fld_thangthienvocong != null)
			{
				var array9 = (byte[])dbRow.fld_thangthienvocong;
				for (var num10 = 0; num10 < 32 && array9.Length >= num10 * 8 + 8; num10++)
				{
					var fLD_PID_ = BitConverter.ToInt32(array9, num10 * 8);
					var num11 = BitConverter.ToInt32(array9, num10 * 8 + 4);
					X_Vo_Cong_Loai x_Vo_Cong_Loai2 = new(fLD_PID_);
					x_Vo_Cong_Loai2.VoCong_DangCap = ((num11 < x_Vo_Cong_Loai2.FLD_VoCongToiCaoDangCap) ? num11 : x_Vo_Cong_Loai2.FLD_VoCongToiCaoDangCap);
					if ((x_Vo_Cong_Loai2.FLD_JOB == 0 || Player_Job == x_Vo_Cong_Loai2.FLD_JOB) && (x_Vo_Cong_Loai2.FLD_ZX == 0 || Player_Zx == x_Vo_Cong_Loai2.FLD_ZX) && Player_Job_level >= x_Vo_Cong_Loai2.FLD_JOBLEVEL && Player_Level >= x_Vo_Cong_Loai2.FLD_LEVEL)
					{
						VoCongMoi[x_Vo_Cong_Loai2.FLD_VoCongLoaiHinh, x_Vo_Cong_Loai2.FLD_INDEX] = x_Vo_Cong_Loai2;
					}
				}
			}
			if (Player_Job_level >= 6 && Player_Job == 8)
			{
				if (VoCongMoi == null) return;

				// Lấy target player sử dụng helper method
				Players targetPlayer = GetTargetPlayer();
				if (targetPlayer == null)
				{
					LogHelper.WriteLine(LogLevel.Error, $"LoadAscentionMagic Error: Không thể lấy target player cho {AccountID}-{CharacterName}");
					return;
				}

				if (VoCongMoi[3, 9] == null)
				{
					X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 3, 9);
				}
				if (VoCongMoi[3, 10] == null)
				{
					X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 3, 10);
				}
				if (VoCongMoi[3, 11] == null)
				{
					X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 3, 11);
				}
			}
			if (Player_Job_level >= 12)
			{
				var num12 = 0;
				var array10 = (byte[])dbRow.fld_asc7_anti_qigong;
				Player_Anti_Qigong_point = (Player_Level - 169) * 2;
				for (var num13 = 0; num13 < 27; num13++)
				{
					var array11 = new byte[4];
					try
					{
						if (array10.Length < num13 * 4 + 4)
						{
							System.Buffer.BlockCopy(BitConverter.GetBytes(num13 + 2001), 0, array11, 0, 4);
							X_Thang_Thien_Khi_Cong_Loai x_Thang_Thien_Khi_Cong_Loai2 = new(array11);
							PhanKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai2.KhiCongID, x_Thang_Thien_Khi_Cong_Loai2);
						}
						else
						{
							System.Buffer.BlockCopy(array10, num13 * 4, array11, 0, 4);
							X_Thang_Thien_Khi_Cong_Loai x_Thang_Thien_Khi_Cong_Loai3 = new(array11);
							PhanKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai3.KhiCongID, x_Thang_Thien_Khi_Cong_Loai3);
							num12 += x_Thang_Thien_Khi_Cong_Loai3.KhiCong_SoLuong;
						}
					}
					catch
					{
					}
				}
				Player_Anti_Qigong_point -= num12;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải Ascention Magic: " + ex.Message);
			LogHelper.WriteLine(LogLevel.Error, "Stack trace: " + ex.StackTrace);
		}
	}

	private void LoadMagic(tbl_xwwl_char dbRow)
	{
		var array6 = (byte[])dbRow.fld_kongfu;
		for (var num7 = 0; num7 < 78; num7++)
		{
			var array7 = new byte[4];
			var array8 = new byte[4];
			try
			{
				if (array6.Length < num7 * 8 + 8)
				{
					break;
				}
				System.Buffer.BlockCopy(array6, num7 * 8, array7, 0, 4);
				System.Buffer.BlockCopy(array6, num7 * 8 + 4, array8, 0, 4);
				var num8 = BitConverter.ToInt32(array7, 0);
				if (num8 == 0)
				{
					continue;
				}
				var num9 = BitConverter.ToInt32(array8, 0);
				if (num8 == 1000701)
				{
					num8 = ((Player_Zx != 1) ? 1020701 : 1010701);
				}
				if (num8 != 0)
				{
					X_Vo_Cong_Loai x_Vo_Cong_Loai = new(num8);
					if ((x_Vo_Cong_Loai.FLD_JOB == 0 || Player_Job == x_Vo_Cong_Loai.FLD_JOB) && (x_Vo_Cong_Loai.FLD_ZX == 0 || Player_Zx == x_Vo_Cong_Loai.FLD_ZX) && Player_Job_level >= x_Vo_Cong_Loai.FLD_JOBLEVEL && Player_Level >= x_Vo_Cong_Loai.FLD_LEVEL)
					{
						x_Vo_Cong_Loai.VoCong_DangCap = ((num9 < x_Vo_Cong_Loai.FLD_VoCongToiCaoDangCap) ? num9 : x_Vo_Cong_Loai.FLD_VoCongToiCaoDangCap);
						VoCongMoi[x_Vo_Cong_Loai.FLD_VoCongLoaiHinh, x_Vo_Cong_Loai.FLD_INDEX] = x_Vo_Cong_Loai;
					}
				}
				continue;
			}
			catch
			{
				continue;
			}
		}
		if (Player_Job_level >= 5 && ((Player_Job != 8) & (Player_Job != 13)))
		{
			// Lấy target player sử dụng helper method
			Players targetPlayer = GetTargetPlayer();
			if (targetPlayer == null)
			{
				LogHelper.WriteLine(LogLevel.Error, $"LoadMagic Error: Không thể lấy target player cho {AccountID}-{CharacterName}");
				return;
			}

			if (VoCongMoi[0, 25] == null)
			{
				X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 0, 25);
			}
			if (VoCongMoi[0, 26] == null)
			{
				X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 0, 26);
			}
			if (VoCongMoi[0, 27] == null)
			{
				X_Vo_Cong_Loai.LearnMartialArtsBook(targetPlayer, 0, 27);
			}
		}
		if (VoCongMoi[1, 2] != null)
		{
			Character_KhinhCong = 2;
		}
		else if (VoCongMoi[1, 1] != null)
		{
			Character_KhinhCong = 1;
		}
	}

	private void LoadAbility(tbl_xwwl_char dbRow, int num3)
	{
		 var array3 = new byte[100];
		var array4 = (byte[])dbRow.fld_skills;
		System.Buffer.BlockCopy(array4, 0, array3, 0, array4.Length);
		for (var l = 0; l < 15; l++)
		{
			var array5 = new byte[2];
			try
			{
				if (array3.Length > l + 1)
				{
					System.Buffer.BlockCopy(array3, l, array5, 0, 1);
				}
			}
			catch
			{
			}
			KhiCong[l] = new(array5);
			switch (l)
			{
				default:
					if (l < 12)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
					}
					break;
				case 0:
					if (Player_Level >= 1)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 1:
					if (Player_Level >= 1)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 2:
					if (Player_Level >= 1)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 3:
					if (Player_Level >= 1)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 4:
					if (Player_Level >= 1)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 5:
					if (Player_Level >= 10 && Player_Job_level >= 1)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 6:
					if (Player_Level >= 10 && Player_Job_level >= 1)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 7:
					if (Player_Level >= 35 && Player_Job_level >= 2)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 8:
					if (Player_Level >= 60 && Player_Job_level >= 3)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 9:
					if (Player_Level >= 80 && Player_Job_level >= 4)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 10:
					if (Player_Level >= 90 && Player_Job_level >= 4)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
				case 11:
					if (Player_Level >= 100 && Player_Job_level >= 5)
					{
						KhiCong[l].KhiCongID = GetKhiCong_ID(l, Player_Job);
						if (KhiCong[l].KhiCong_SoLuong == 255)
						{
							KhiCong[l].KhiCong_SoLuong = 0;
						}
					}
					break;
			}
		}
		var num4 = 0;
		for (var m = 0; m < 12; m++)
		{
			var khiCong_byte = KhiCong[m].KhiCong_byte;
			int num5 = BitConverter.ToInt16(khiCong_byte, 0);
			if (khiCong_byte[0] != byte.MaxValue)
			{
				num4 += num5;
			}
		}
		var num6 = ((Player_Level <= 34) ? (Player_Level - 1) : (34 + (Player_Level - 35) * 2));
		if (num4 + Player_Qigong_point + num3 != num6)
		{
			if (num4 + num3 > num6 - Player_Qigong_point)
			{
				Player_Qigong_point = num6;
				for (var n = 0; n < 12; n++)
				{
					if (BitConverter.ToInt16(KhiCong[n].KhiCong_byte, 0) != 255)
					{
						KhiCong[n].KhiCong_byte = new byte[2];
					}
				}
				foreach (var value8 in ThangThienKhiCong.Values)
				{
					value8.KhiCong_SoLuong = 0;
				}
			}
			else
			{
				Player_Qigong_point = num6 - num4 - num3;
			}
		}
	}

	private int LoadAscentionAbility(tbl_xwwl_char dbRow, int num3)
	{
		if (dbRow.fld_thangthienkhicong != null)
		{
			var array = (byte[])dbRow.fld_thangthienkhicong;
			if (Player_Job_level >= 6)
			{
				for (var k = 0; k < 15; k++)
				{
					var array2 = new byte[4];
					try
					{
						if (array.Length < k * 4 + 4)
						{
							break;
						}
						System.Buffer.BlockCopy(array, k * 4, array2, 0, 4);
						X_Thang_Thien_Khi_Cong_Loai x_Thang_Thien_Khi_Cong_Loai = new(array2);
						if (!World.ThangThienKhiCongList.TryGetValue(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, out var value))
						{
							continue;
						}
						switch (Player_Job)
						{
							case 1:
								if (value.NhanVatNgheNghiep1 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 2:
								if (value.NhanVatNgheNghiep2 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 3:
								if (value.NhanVatNgheNghiep3 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 4:
								if (value.NhanVatNgheNghiep4 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 5:
								if (value.NhanVatNgheNghiep5 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 6:
								if (value.NhanVatNgheNghiep6 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 7:
								if (value.NhanVatNgheNghiep7 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 8:
								if (value.NhanVatNgheNghiep8 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 9:
								if (value.NhanVatNgheNghiep9 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 10:
								if (value.NhanVatNgheNghiep10 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 11:
								if (value.NhanVatNgheNghiep11 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 12:
								if (value.NhanVatNgheNghiep12 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
							case 13:
								if (value.NhanVatNgheNghiep13 == 1 && !GetSTQG(x_Thang_Thien_Khi_Cong_Loai.KhiCongID))
								{
									ThangThienKhiCong.Add(x_Thang_Thien_Khi_Cong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Loai);
									num3 += x_Thang_Thien_Khi_Cong_Loai.KhiCong_SoLuong;
								}
								break;
						}
						continue;
					}
					catch
					{
						continue;
					}
				}
			}
		}

		return num3;
	}


}
