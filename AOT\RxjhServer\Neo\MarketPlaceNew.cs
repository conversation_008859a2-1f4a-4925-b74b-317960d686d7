using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Database.FreeSql.Entities.BBG;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using Microsoft.Data.SqlClient;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer
{
    /// <summary>
    /// Hệ thống quản lý chợ giao dịch vật phẩm
    /// </summary>
    public class MarketPlace
    {
        private readonly Players _player;

        private readonly long[] _buaChu =
        {
            800000003, 800000004, 800000005, 800000007, 800000008, 800000009, 800000010, 800000029, 800000050, 800000051,
            800000052, 800000053, 800000054, 800000055, 800000055, 800000057, 1000000330, 1000000365, 1000000367,
            1000000654, 1000000652
        };

        private readonly long[] _petEns =
        {
            800000030, 800000032, 800000031, 800000033, 800000034, 800000035, 800000036, 800000037, 800000080, 800000081,
            800000082, 800000083
        };

        private readonly long[] _petIds =
            { 1008000557, 1008000558, 1008000559, 1008000560, 1008000563, 1008000564, 1008000565 };

        // private readonly long[] _petEggBears = { 1008002690};
        private readonly Dictionary<long, int[]> _stoneDict = new()
        {
            { 800000001, new[] { 4, 1 } }, // Kim cương thạch
            { 800000023, new[] { 4, 2 } }, // Kim cương thạch cao cấp
            { 800000061, new[] { 4, 3 } }, // Kim cương thạch siêu cấp
            { 1000001650, new[] { 4, 4 } }, // Kim cương thạch hỗn nguyên
            { 800000002, new[] { 5, 1 } }, // Hàn ngọc thạch
            { 800000024, new[] { 5, 2 } }, // Hàn ngọc thạch cao cấp
            { 800000062, new[] { 5, 3 } }, // Hàn ngọc thạch siêu cấp
            { 1000001651, new[] { 5, 4 } }, // Hàn ngọc thạch cao cấp
            { 800000028, new[] { 11, 0 } } // Nguyên liệu thường
        };

        public MarketPlace(Players player)
        {
            _player = player;
        }

        /// <summary>
        /// Xử lý các yêu cầu từ chợ
        /// </summary>
        public void MarketPlaceRequest(byte[] data)
        {
            try
            {
                var offset = 2;
                int type = BitConverter.ToInt16(data, 0xC - offset);
                switch (type)
                {
                    case 1:
                        // Register New Item
                        MarketPlace_Register_Item(data, offset);
                        break;
                    case 2:
                        // Buy Item
                        MarKetPlace_Buy_Item(data, offset);
                        break;
                    case 3:
                        // Get List Item
                        MarketPlace_List_Item(data, offset);
                        break;
                    case 4:
                        // Log
                        MarketPlace_History(_player, data, offset);
                        break;
                    case 5:
                        // Retrieve Item from market
                        MarketPlace_Take_Profit(data, offset);
                        break;
                    case 6:
                        MarketPlace_Cancel_Selling(data, offset);
                        break;
                    case 7:
                        // My Selling Item
                        MarketPlace_List_Items_Sold(_player, offset);
                        break;
                }
            }
            catch (Exception ex)
            {
                _player.HeThongNhacNho("MarketPlace Error ", 10, "Market");
                LogHelper.WriteLine(LogLevel.Error, "MarketPlace Error " + ex.Message);
            }
        }

       public async void MarketPlace_Take_Profit(byte[] data, int offset)
    {
        try
        {
            // Parse packet data
            var productId = BitConverter.ToInt32(data, 0x12 - offset);

            // Complete marketplace transaction using BBGDb
            var profitData = await BBGDb.CompleteMarketplaceTransaction(productId, _player.CharacterName);

            if (profitData != null)
            {
                // Add profit to player's currency
                MarketPlace_Currency_Update(profitData.ProfitAmount, CurrencyOperation.Increase);

                // Update player data
                _player.Init_Item_In_Bag();
                _player.UpdateCharacterData(_player);
                _player.SaveCharacterData(); // Save character data to prevent item loss

                // Send success notification to client
                var array = Converter.HexStringToByte(
                    "aa5500000f27460154000500010000000000000000000000000000000000000000000600000001000000b9823f0200000000189354000000004700000000000000000000000000000000000000000000000000bcadb1000000000000000055aa");

                var buyerBytes = Encoding.GetEncoding(1252).GetBytes(profitData.BuyerName);
                System.Buffer.BlockCopy(buyerBytes, 0, array, 0x12, Math.Min(buyerBytes.Length, 32));

                _player.Client?.Send_Map_Data(array, array.Length);

                LogHelper.WriteLine(LogLevel.Info, $"Player {_player.CharacterName} took profit {profitData.ProfitAmount} from marketplace item {productId}");
            }
            else
            {
                _player.HeThongNhacNho("Không tìm thấy vật phẩm!!! Vui lòng liên hệ admin", 10, "Market");
                LogHelper.WriteLine(LogLevel.Debug, $"No sold marketplace item found with ID: {productId} for player: {_player.CharacterName}");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"MarketPlace_Take_Profit Error: {ex.Message}");
            _player.HeThongNhacNho("Có lỗi xảy ra với Thiên cơ các! Vui lòng thử lại sau!!!", 10, "Market");
        }
    }

    public async void MarketPlace_Cancel_Selling(byte[] data, int offset)
    {
        try
        {
            // Parse packet data
            int type = BitConverter.ToInt16(data, 0xC - offset);
            int amount = BitConverter.ToInt16(data, 0x1A - offset);
            var productId = BitConverter.ToInt32(data, 0x12 - offset);
            var itemId = BitConverter.ToInt64(data, 0x1c - offset);
            var option = BitConverter.ToInt32(data, 0x24 - offset);
            var magic1 = BitConverter.ToInt32(data, 0x28 - offset);
            var magic2 = BitConverter.ToInt32(data, 0x2C - offset);
            var magic3 = BitConverter.ToInt32(data, 0x30 - offset);
            var magic4 = BitConverter.ToInt32(data, 0x34 - offset);

            // Create item validation data
            var itemValidation = (itemId, option, magic1, magic2, magic3, magic4);

            // Cancel marketplace selling using BBGDb
            var cancelResult = await BBGDb.CancelMarketplaceSelling(productId, _player.CharacterName, amount, itemValidation);

            if (cancelResult.Success)
            {
                // Check inventory space
                var emptySlot = _player.GetParcelVacancy(_player);
                if (emptySlot == -1)
                {
                    _player.HeThongNhacNho("Thùng đồ không đủ chỗ trống", 10, "Market");
                    return;
                }

                // Create item from marketplace data
                var marketItem = new Item { VatPham_byte = cancelResult.Item.item };

                // Generate new item ID if stackable
                if (marketItem.GetVatPhamSoLuong > 1)
                {
                    var newId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
                    System.Buffer.BlockCopy(newId, 0, marketItem.VatPham_byte, 0, 8);
                }

                // Set correct amount and give item back to player
                marketItem.VatPhamSoLuong = BitConverter.GetBytes(amount);
                _player.Item_In_Bag[emptySlot].VatPham_byte = marketItem.VatPham_byte;

                // Update beast equipment if needed
                if (_player.CharacterBeast != null)
                {
                    _player.UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory();
                    _player.UpdateTheWeightOfTheBeast();
                }

                // Update player data
                _player.Init_Item_In_Bag();
                _player.UpdateCharacterData(_player);
                _player.SaveCharacterData(); // Save character data to prevent item loss

                // Send success response
                MarketPlace_Response(type, 1);

                LogHelper.WriteLine(LogLevel.Info, $"Player {_player.CharacterName} cancelled marketplace item {productId}");
            }
            else
            {
                _player.HeThongNhacNho(cancelResult.ErrorMessage.Contains("validation") ?
                    "Có lỗi xảy ra khi kiểm tra vật phẩm" :
                    "Item này đã được bán hoặc hết hạn!!!", 10, "Market");

                LogHelper.WriteLine(LogLevel.Debug, $"Cancel failed: {cancelResult.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"MarketPlace_Cancel_Selling Error: {ex.Message}");
            _player.HeThongNhacNho("Có lỗi xảy ra với Thiên cơ các! Vui lòng thử lại sau!!!", 10, "Market");
        }
    }

    public async void MarKetPlace_Buy_Item(byte[] data, int offset)
    {
        try
        {
            // Parse packet data
            int type = BitConverter.ToInt16(data, 0xC - offset);
            int amount = BitConverter.ToInt16(data, 0x1A - offset);
            var productId = BitConverter.ToInt32(data, 0x12 - offset);
            var itemId = BitConverter.ToInt64(data, 0x1c - offset);
            var option = BitConverter.ToInt32(data, 0x24 - offset);
            var magic1 = BitConverter.ToInt32(data, 0x28 - offset);
            var magic2 = BitConverter.ToInt32(data, 0x2C - offset);
            var magic3 = BitConverter.ToInt32(data, 0x30 - offset);
            var magic4 = BitConverter.ToInt32(data, 0x34 - offset);
            var price = BitConverter.ToInt64(data, 0x38 - offset);

            // Validate input
            if (_player.Player_Money < price)
            {
                _player.HeThongNhacNho("Bạn không đủ tiền mua vật phẩm này ", 10, "Market");
                return;
            }

            if (amount <= 0)
            {
                _player.HeThongNhacNho("Số lượng không hợp lệ", 10, "Market");
                return;
            }

            // Check inventory space
            var emptySlot = _player.GetParcelVacancy(_player);
            if (emptySlot == -1)
            {
                _player.HeThongNhacNho("Thùng đồ không đủ chỗ trống", 10, "Market");
                return;
            }

            // Create item validation data
            var itemValidation = (itemId, option, magic1, magic2, magic3, magic4);

            // Purchase marketplace item using BBGDb with race condition protection
            var purchaseResult = await BBGDb.PurchaseMarketplaceItem(productId, _player.CharacterName, amount, price, itemValidation);

            if (purchaseResult.Success)
            {
                // Deduct money from player
                MarketPlace_Currency_Update(price, CurrencyOperation.Decrease);

                // Create item from marketplace data
                var marketItem = new Item { VatPham_byte = purchaseResult.Item.item };

                // Generate new item ID if stackable
                if (marketItem.GetVatPhamSoLuong > 1)
                {
                    var newId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
                    System.Buffer.BlockCopy(newId, 0, marketItem.VatPham_byte, 0, 8);
                }

                // Set correct amount and give item to player
                marketItem.VatPhamSoLuong = BitConverter.GetBytes(amount);
                _player.Item_In_Bag[emptySlot].VatPham_byte = marketItem.VatPham_byte;

                // Update beast equipment if needed
                if (_player.CharacterBeast != null)
                {
                    _player.UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory();
                    _player.UpdateTheWeightOfTheBeast();
                }

                // Update player data
                _player.Init_Item_In_Bag();
                _player.UpdateCharacterData(_player);
                _player.SaveCharacterData(); // Save character data to prevent item loss

                // Send success response
                MarketPlace_Response(type, 1);

                LogHelper.WriteLine(LogLevel.Info, $"Player {_player.CharacterName} purchased marketplace item {productId} for {price}");
            }
            else
            {
                // Handle purchase failure
                string errorMessage = purchaseResult.ErrorMessage.Contains("already sold") ?
                    "Item này đã được bán hoặc hết hạn!!!" :
                    purchaseResult.ErrorMessage.Contains("validation") ?
                    "Có lỗi xảy ra khi kiểm tra vật phẩm" :
                    "Có lỗi xảy ra với Thiên cơ các! Vui lòng thử lại sau!!!";

                _player.HeThongNhacNho(errorMessage, 10, "Market");
                LogHelper.WriteLine(LogLevel.Debug, $"Purchase failed: {purchaseResult.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"MarKetPlace_Buy_Item Error: {ex.Message}");
            _player.HeThongNhacNho("Có lỗi xảy ra với Thiên cơ các! Vui lòng thử lại sau!!!", 10, "Market");
        }
    }

    public int MarketPlace_CreateOrderDetail(Players player, int id, int amount, long price)
    {
            try
            {
                return BBGDb.CreateOrderDetail(player.CharacterName, id, amount, price).Result;
        //     const string insertQuery = @"
                // INSERT INTO ORDER_DETAIL (STATUS, MESSAGE, BUYYER, AMOUNT, PRICE, MARKETPLACE_ID, CREATED_AT, UPDATED_AT)
                // OUTPUT INSERTED.ID
                // VALUES (@Status, @Message, @Buyer, @Amount, @Price,@MarketID, @CreatedAt, @UpdatedAt)";

                //     var sqlParameters = new List<SqlParameter>
                //     {
                //         new("@Status", "PENDING"),
                //         new("@Message", DBNull.Value),
                //         new("@Buyer", player.CharacterName),
                //         new("@Amount", amount),
                //         new("@Price", price),
                //         new("@marketID", id),
                //         new("@CreatedAt", DateTime.Now),
                //         new("@UpdatedAt", DateTime.Now)
                //     };

                //     var orderDetailId = DBA.GetDBValue_3(insertQuery, sqlParameters.ToArray(), "BBG");
                //     if (orderDetailId is not int detailId) return -1;

                //     return detailId;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Debug, "MarketPlace_CreateOrderDetail Error " + ex.Message);
                return -1;
            }
    }

        public bool MarketPlace_Update_Order(int orderId, string status)
        {
            return BBGDb.UpdateOrderStatus(orderId, status).Result;
        // const string updateQuery = @"UPDATE ORDER_DETAIL SET STATUS=@Status WHERE ID = @OrderID";
            // var sqlParameter = new List<SqlParameter>
            // {
            //     new("@Status", status),
            //     new("@OrderID", orderId)
            // };
            // var rowsAffected = DBA.ExeSqlCommand(updateQuery, sqlParameter.ToArray(), "BBG").GetAwaiter().GetResult();
            // return rowsAffected > 0;
        }

    public void MarketPlace_Response(int type, int success)
    {
        var array = Converter.HexStringToByte("aa5500000f274601060001000100000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 0xC, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(success), 0, array, 0XE, 2);
        _player.Client?.Send_Map_Data(array, array.Length);
    }

    public void MarketPlace_List_Items_Sold(Players player, int offset)
    {
        SendingClass packetDataClass = new();
        packetDataClass.Write2(7); //
        packetDataClass.Write4(1);
        packetDataClass.Write4(int.MaxValue);
            //packetDataClass.Write4(0);
       // var totals = BBGDb.CountTotalItemSelling(player.CharacterName, "SOLD").Result;
        var items = BBGDb.GetMarketplaceItems(player.CharacterName, "SOLD", offset, 30).Result;
        foreach (var row in items)
        try
        {
            MarketPlace_Write_One_Item(packetDataClass, row);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Debug, "Error " + ex.Message);
        }

        _player.Client?.SendPak(packetDataClass, 17921, 0, true);
    }
    
    public void MarketPlace_Write_One_Item_BasePrice(SendingClass packetDataClass, marketplace row, int currentPage = 0)
    {
        packetDataClass.Write4(currentPage);
        packetDataClass.Write4((int)row.id);
        packetDataClass.Write4(0);
        packetDataClass.Write4(_player.SessionID);
        packetDataClass.WriteString(_player.AccountID.Trim(), 20);
        packetDataClass.Write4(_player.SessionID);
        packetDataClass.Write4(0);
        packetDataClass.Write(0);
        packetDataClass.WriteName(row.seller_name.Trim());
        packetDataClass.Write4(0);
        packetDataClass.Write4(0);
        var originalItem = (byte[])row.item;
        Item item = new()
        {
            VatPham_byte = originalItem,
            VatPhamSoLuong = BitConverter.GetBytes((int)row.current_amount)
        };
        packetDataClass.Write(item.GetByte());

        packetDataClass.Write8((long)row.base_price);
        packetDataClass.Write2((int)row.current_amount);
        packetDataClass.Write4(MarketPlace_Get_Expired_Seconds(Convert.ToDateTime(row.expired_at)));
       // packetDataClass.Write4(0);
    }

    public void MarketPlace_Write_One_Item(SendingClass packetDataClass, marketplace row, int currentPage = 0)
        {
            packetDataClass.Write4(currentPage);
            packetDataClass.Write4((int)row.id);
            packetDataClass.Write4(0);
            packetDataClass.Write4(_player.SessionID);
            packetDataClass.WriteString(_player.AccountID.Trim(), 20);
            packetDataClass.Write4(_player.SessionID);
            packetDataClass.Write4(0);
            packetDataClass.Write(0);
            packetDataClass.WriteName(row.seller_name.Trim());
            packetDataClass.Write4(0);
            packetDataClass.Write4(0);
            var originalItem = (byte[])row.item;
            Item item = new()
            {
                VatPham_byte = originalItem,
                VatPhamSoLuong = BitConverter.GetBytes((int)row.current_amount)
            };
            packetDataClass.Write(item.GetByte());

            packetDataClass.Write8((long)row.fld_price);
            packetDataClass.Write2((int)row.current_amount);
            packetDataClass.Write4(MarketPlace_Get_Expired_Seconds(Convert.ToDateTime(row.expired_at)));
            // packetDataClass.Write4(0);
        }

    public async void MarketPlace_List_Item(byte[] data, int offset)
    {
        try
        {
            // Parse packet data
            int page = BitConverter.ToInt16(data, 0x1c - offset);
            int filter0 = BitConverter.ToInt16(data, 0xe - offset);
            int filter1 = BitConverter.ToInt16(data, 0x12 - offset);
            int filter2 = BitConverter.ToInt16(data, 0x14 - offset);
            int filter3 = BitConverter.ToInt16(data, 0x16 - offset);
            int filter4 = BitConverter.ToInt16(data, 0x18 - offset);
            int filter5 = BitConverter.ToInt16(data, 0x1A - offset);

            var searchByte = new byte[0x1A];
            Array.Copy(data, 0x30 - offset, searchByte, 0, 0x1A);
            var searchWord = Encoding.Default.GetString(searchByte).Replace("\0", "").ToLowerInvariant();

            // Create filter object
            var filter = new BBGDb.MarketplaceFilter
            {
                Filter1 = filter1,
                Filter2 = filter2,
                Filter3 = filter3,
                Filter4 = filter4,
                Filter5 = filter5,
                SearchWord = searchWord,
                SortOrder = filter0
            };

            // Search marketplace items using BBGDb
            var searchResult = await BBGDb.SearchMarketplaceItems(filter, page, 10);

            // Create response packet
            var packetData = new SendingClass();
            packetData.Write2(3); // 3 - Item List
            packetData.Write4(searchResult.TotalCount != 0 ? 1 : 0);
            packetData.Write4(searchResult.TotalPages);

            LogHelper.WriteLine(LogLevel.Error, $"Total {searchResult.TotalCount} page {searchResult.TotalPages}");

            // Write items to packet
            foreach (var item in searchResult.Items)
            {
                try
                {
                    MarketPlace_Write_One_Item(packetData, item);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Error writing item {item.id}: {ex.Message}");
                }
            }

            // Send packet
            _player.Client?.SendPak(packetData, 17921, _player.SessionID, true);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Error, $"MarketPlace_List_Item Error: {ex.Message}");
        }
    }

    public async Task MarketPlace_Register_Item(byte[] data, int offset)
    {
        int type = BitConverter.ToInt16(data, 0xC - offset);
        int itemPos = BitConverter.ToInt16(data, 0xE - offset);
        var price = BitConverter.ToInt64(data, 0x14 - offset);
        int amount = BitConverter.ToInt16(data, 0x1c - offset);
        _player.Item_In_Bag[itemPos].Lock_Move = true;
        if (amount > _player.Item_In_Bag[itemPos].GetVatPhamSoLuong)
            throw new($"{_player.CharacterName} - Số lượng vật phẩm đăng bán nhiều hơn số lượng đang có ");

        var itemByte = new byte[World.Item_Db_Byte_Length]; //
        System.Buffer.BlockCopy(data, 0x1E - offset, itemByte, 8, World.Item_Db_Byte_Length - 8); //  - Series
        //LogHelper.WriteLine(LogLevel.Debug, $"ItemPost {itemPos} itemPrice {price} amount {amount}");
        if (_player.Player_Money <= 0 || _player.Player_Money < price * 1 / 1000)
        {
            _player.HeThongNhacNho($"Cần {price * 1 / 1000} Lượng để có thể bán vật phẩm", 10, "Market");
            throw new("Nhân vật không đủ tiền để đăng ký bán vật phẩm");
        }

        if (price < 1)
        {
            _player.HeThongNhacNho("Giá của vật phẩm không thể nhỏ hơn 1", 10, "Market");
            throw new("Giá của vật phẩm không thể nhỏ hơn 1");
        }

        // Check 20 item sell cung luc
        MarketPlace_ValidateMaxItemsOnSale(type);
        var itemId = _player.Item_In_Bag[itemPos].GetVatPham_ID;
        var newProductCode = "MP" + DateTime.Now.ToString("yyMMddHHmmss");
        var rowsAffected = await MarketPlace_RegisterItemInMarket(itemPos, itemId, newProductCode, amount, price);
        if (rowsAffected > 0)
        {
            //Success
            Item item = new()
            {
                VatPham_byte = itemByte
            };
            item = _player.Item_In_Bag[itemPos];
            _player.Item_In_Bag[itemPos].Lock_Move = false;
            //LogHelper.WriteLine(LogLevel.Error, $"Success id {rowsAffected} amount {amount} old {Item_In_Bag[itemPos].VatPhamSoLuong}");
            var amountLeft = _player.Item_In_Bag[itemPos].GetVatPhamSoLuong - amount;

            if (amountLeft <= 0)
                _player.Item_In_Bag[itemPos].VatPham_byte = new byte[World.Item_Db_Byte_Length];
            else
                _player.Item_In_Bag[itemPos].VatPhamSoLuong = BitConverter.GetBytes(amountLeft);

            MarketPlace_Currency_Update(price * 1 / 1000, CurrencyOperation.Decrease);
            _player.SaveCharacterData();
            if (!MarketPlace_Update_Status(newProductCode, "SELLING"))
            {
                _player.Item_In_Bag[itemPos].VatPham_byte = item.VatPham_byte;
                _player.SaveCharacterData();
                throw new("Không thể cập nhật vật phẩm. Vui lòng thử lại sau");
            }

            if (_player.CharacterBeast != null)
            {
                _player.UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory();
                _player.UpdateTheWeightOfTheBeast();
            }

            _player.Init_Item_In_Bag();
            MarketPlace_Response(type, 1);
        }
        else
        {
            _player.HeThongNhacNho("Có lỗi xảy ra với máy chủ! Vui lòng thử lại sau!", 10, "Market");
            MarketPlace_Update_Status(newProductCode, "FAILED");
            // Failed, Revert the change, add back item to Bag
            MarketPlace_Response(type, int.MaxValue - 4); // true = success
        }
    }

    public void MarketPlace_Currency_Update(long price, CurrencyOperation operation)
    {
        switch (operation)
        {
            case CurrencyOperation.Decrease:
                _player.KiemSoatGold_SoLuong(price, 0);
                break;
            case CurrencyOperation.Increase:
                _player.KiemSoatGold_SoLuong(price, 1);
                break;
            default:
                LogHelper.WriteLine(LogLevel.Error,"Default");
                break;
        }

        _player.UpdateMoneyAndWeight();
        // var type = 0;
        // // position 0 = -gold 1 = +gold
        // switch (type)
        // {
        //     case 1:
        //         CheckTheNumberOfIngotsInBaibaoge();
        //         if (operation == CurrencyOperation.Decrease)
        //         {
        //             KiemSoatCash_SoLuong((int)price, 0);
        //             HeThongNhacNho($"Đã trừ {price} Cash. Còn lại {FLD_RXPIONT} CASH", 10, "Market");
        //         }
        //         else if (operation == CurrencyOperation.Increase)
        //         {
        //             KiemSoatCash_SoLuong((int)price, 1);
        //             HeThongNhacNho($"Đã cộng {price} Cash. Còn lại {FLD_RXPIONT} CASH", 10, "Market");
        //         }
        //
        //         Save_CashData();
        //         break;
        //     default:
        //         if (operation == CurrencyOperation.Decrease)
        //             KiemSoatGold_SoLuong(price, 0);
        //         else if (operation == CurrencyOperation.Increase) KiemSoatGold_SoLuong(price, 1);
        //
        //         UpdateMoneyAndWeight();
        //         break;
        // }
    }

        private async Task<int> MarketPlace_RegisterItemInMarket(int itemPos, long itemId, string newProductCode, int amount, long price)
        {
            var itemInfo = World.ItemList.TryGetValue((int)itemId, out var item) ? item : null;
            if (itemInfo == null) return 0;
            var reside1 = itemInfo.FLD_RESIDE1; // Job
            var reside2 = itemInfo.FLD_RESIDE2; // Loai Item
            var level = itemInfo.FLD_LEVEL;
            var enhanced = _player.Item_In_Bag[itemPos].FLD_CuongHoaSoLuong;
            var attribute = _player.Item_In_Bag[itemPos].FLDThuocTinhSoLuong;
            var option = _player.Item_In_Bag[itemPos].FLD_MAGIC0;
            int filter1, filter2 = 0, filter3 = 0, filter4 = 0, filter5 = 0;
            switch (reside2)
            {
                case 4:
                    filter1 = 1;
                    filter2 = reside1;
                    filter3 = level % 10 + 1;
                    filter4 = enhanced;
                    filter5 = attribute;
                    break;
                case 1:
                    filter1 = 2;
                    filter2 = reside2;
                    filter3 = reside1;
                    filter4 = level % 10 + 1;
                    filter5 = enhanced;
                    break;
                case 2:
                case 5:
                case 6:
                    filter1 = 2;
                    filter2 = reside2;
                    filter3 = level % 10 + 1;
                    filter4 = enhanced;
                    filter5 = 0;
                    break;
                case 7:
                case 8:
                case 10:
                    filter1 = 3;
                    filter2 = level % 10 + 1;
                    filter3 = 0;
                    filter4 = 0;
                    filter5 = 0;
                    break;
                case 16:
                    filter3 = option / 100000;
                    filter4 = 0;
                    filter5 = 0;
                    if (_stoneDict.TryGetValue(itemId, out var val))
                    {
                        //Các loại cường hóa hợp thành thạch
                        filter1 = val[0];
                        filter2 = val[1];
                    }
                    else if (_petIds.Contains(itemId))
                    {
                        filter1 = 7;
                        filter2 = 1;
                    }
                    else if (_petEns.Contains(itemId))
                    {
                        filter1 = 7;
                        filter2 = 5;
                    }
                    else if (_buaChu.Contains(itemId))
                    {
                        filter1 = 8;
                        filter2 = 3;
                    }
                    else if (itemId == 800000013)
                    {
                        // Handle Khi cong
                        filter1 = 6;
                    }
                    else
                    {
                        filter1 = 10;
                    }

                    break;
                case 18:
                    filter1 = 8;
                    filter2 = 3;
                    break;
                case 19:
                    filter1 = 8;
                    filter2 = 4;
                    break;
                case 23:
                    // Trang bị pet
                    filter1 = 7;
                    filter2 = 5;
                    break;
                case 25:
                    // Trang sức pet
                    filter1 = 7;
                    filter2 = 3;
                    break;
                default:
                    filter1 = 10;
                    filter2 = 0;
                    filter3 = 0;
                    filter4 = 0;
                    filter5 = 0;
                    break;
            }

            var totalSelling = await BBGDb.TotalSelling(_player.CharacterName);
            if (totalSelling >= 20)
            {
                _player.HeThongNhacNho("Không thể đăng ký !! Mỗi nhân vật chỉ được bán tối đa 20 vật phẩm cùng lúc", 10,
                    "Market");
                return 0;
            }
            var itemName = itemInfo.ItmeNAME;
            var rowsAffected = await BBGDb.RegisterItemInMarket(_player.CharacterName, _player.AccountID, newProductCode, itemName, amount, price, price * 97 / 100, amount, filter1, filter2, filter3, filter4, filter5, _player.Item_In_Bag[itemPos].VatPham_byte, DateTime.Now, DateTime.Now.AddDays(7));


            return rowsAffected;
        }

    private void MarketPlace_ValidateMaxItemsOnSale(int type)
    {
        var totalSelling = BBGDb.TotalSelling(_player.CharacterName).Result;
        if (totalSelling < 20) return;
        _player.HeThongNhacNho("Không thể đăng ký !! Mỗi nhân vật chỉ được bán tối đa 20 vật phẩm cùng lúc", 10,
            "Market");
        MarketPlace_Response(type, int.MaxValue - 4);
        throw new("Cannot register item: Max 20 items can be sold at once.");
    }

    public bool MarketPlace_Update_Status(int productId, string status)
    {
        return BBGDb.UpdateMarketItemStatus(productId, status).Result;
    // const string updateQuery = "UPDATE MARKETPLACE SET STATUS = @Status WHERE ID = @productId";

        // var updateParameters = new List<SqlParameter>
        // {
        //     new("@Status", status),
        //     new("@productId", productId)
        // };

        // var rowsAffected = DBA.ExeSqlCommand(updateQuery, updateParameters.ToArray(), "BBG").GetAwaiter().GetResult();
        // return rowsAffected > 0;
    }

    public bool MarketPlace_Update_Status(string productCode, string status)
    {
        return BBGDb.UpdateMarketItemStatus(productCode, status).Result;
    // const string updateQuery = "UPDATE MARKETPLACE SET STATUS = @Status WHERE PRODUCT_CODE = @ProductCode";

        // var updateParameters = new List<SqlParameter>
        // {
        //     new("@Status", status),
        //     new("@ProductCode", productCode)
        // };
        // var rowsAffected = DBA.ExeSqlCommand(updateQuery, updateParameters.ToArray(), "BBG").GetAwaiter().GetResult();
        // return rowsAffected > 0;
    }

    public void MarketPlace_History(Players player, byte[] data, int offset)
    {
        int type = BitConverter.ToInt16(data, 0xC - offset);
        var totalItems = BBGDb.TotalSelling(player.CharacterName).Result;
        var totalPages = (totalItems + 9) / 10;
        // start 
        var itemsTable = BBGDb.GetMarketplaceItems(player.CharacterName, "SELLING", 0, 30).Result; // 30 items per page for history
        const int packetSize = 10; // Define max items per packet

        LogHelper.WriteLine(LogLevel.Debug, $"Total {totalItems} page {totalPages} {itemsTable.Count}");
        var currentPage = 0;
        for (var i = 0; i < totalItems; i += packetSize)
        {
            var packetData = new SendingClass();
            packetData.Write2(type);
            packetData.Write4(1);
            packetData.Write4(int.MaxValue);
            //packetData.Write4(currentPage); // Set current page for each packet

            var itemsInThisPacket = Math.Min(packetSize, totalItems - i);
            for (var j = i; j < i + itemsInThisPacket; j++)
            {
                var row = itemsTable[j];
                try
                {
                    MarketPlace_Write_One_Item_BasePrice(packetData, row, currentPage);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Error {ex.Message}");
                }
            }

            _player.Client?.SendPak(packetData, 17921, 0,true);
            currentPage++; // Increment page counter after sending a packet
        }

    }

        public int MarketPlace_Get_Expired_Seconds(DateTime expiredAt)
        {
            var minutesLeft = (int)(expiredAt - DateTime.Now).TotalSeconds;
            return minutesLeft;
        }
    }

    /// <summary>
    /// Loại thao tác tiền tệ
    /// </summary>
    public enum CurrencyOperation
    {
        Decrease,
        Increase
    }
}