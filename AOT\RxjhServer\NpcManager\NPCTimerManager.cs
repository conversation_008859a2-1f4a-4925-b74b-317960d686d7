using System;
using System.Collections.Concurrent;
using System.Timers;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
namespace RxjhServer.NpcManager
{
    /// <summary>
    /// Centralized timer management for all NPCs to reduce memory usage and improve performance
    /// Replaces individual timers per NPC with a single shared timer system
    /// </summary>
    public static class NPCTimerManager
    {
        /// <summary>
        /// Global timer that processes all NPC updates
        /// </summary>
        private static readonly Timer _globalUpdateTimer = new(100); // 100ms interval
        
        /// <summary>
        /// Dictionary storing update information for each NPC
        /// </summary>
        private static readonly ConcurrentDictionary<int, NPCUpdateInfo> _npcUpdates = new();
        
        /// <summary>
        /// Flag to track if the timer manager is initialized
        /// </summary>
        private static bool _isInitialized = false;
        
        /// <summary>
        /// Lock object for initialization
        /// </summary>
        private static readonly object _initLock = new object();
        
        /// <summary>
        /// Initialize the timer manager (called automatically when first NPC is registered)
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;
            
            lock (_initLock)
            {
                if (_isInitialized) return;
                
                _globalUpdateTimer.Elapsed += ProcessAllNPCUpdates;
                _globalUpdateTimer.AutoReset = true;
                _globalUpdateTimer.Enabled = true;
                _isInitialized = true;
                
                LogHelper.WriteLine(LogLevel.Info, "NPCTimerManager initialized successfully");
            }
        }
        
        /// <summary>
        /// Register an NPC for timer-based updates
        /// </summary>
        /// <param name="npc">NPC to register</param>
        /// <param name="moveInterval">Movement update interval in milliseconds</param>
        public static void RegisterNPC(NpcClass npc, int moveInterval = 5000)
        {
            if (!_isInitialized)
            {
                Initialize();
            }
            
            var now = DateTime.Now;
            var updateInfo = new NPCUpdateInfo
            {
                NPC = npc,
                MoveInterval = moveInterval,
                NextMoveTime = now.AddMilliseconds(RNG.Next(1000, moveInterval)), // Randomize initial timing
                NextAttackTime = now.AddMilliseconds(RNG.Next(500, 1500)),
                NextRespawnTime = DateTime.MaxValue, // Disabled by default
                RespawnInterval = npc.FLD_NEWTIME * 1000, // Use FLD_NEWTIME in milliseconds
                LastUpdateTime = now
            };
            
            _npcUpdates.AddOrUpdate(npc.NPC_SessionID, updateInfo, (key, existing) => updateInfo);
           // LogHelper.WriteLine(LogLevel.Debug, $"NPC {npc.Name} (ID: {npc.FLD_PID}, SessionID: {npc.NPC_SessionID}) registered with NPCTimerManager");
        }
        
        /// <summary>
        /// Unregister an NPC from timer updates
        /// </summary>
        /// <param name="npcSessionID">NPC session ID to unregister</param>
        public static void UnregisterNPC(int npcSessionID)
        {
            _npcUpdates.TryRemove(npcSessionID, out _);
        }
        
        /// <summary>
        /// Update move interval for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="newInterval">New move interval in milliseconds</param>
        public static void UpdateMoveInterval(int npcSessionID, int newInterval)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                updateInfo.MoveInterval = newInterval;
            }
        }

        /// <summary>
        /// Enable/disable movement for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="enabled">True to enable movement, false to disable</param>
        public static void SetMovementEnabled(int npcSessionID, bool enabled)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                if (enabled)
                {
                    // Reset next move time to enable movement
                    updateInfo.NextMoveTime = DateTime.Now.AddMilliseconds(RNG.Next(1000, updateInfo.MoveInterval));
                }
                else
                {
                    // Set next move time to far future to disable movement
                    updateInfo.NextMoveTime = DateTime.MaxValue;
                }
            }
        }

        /// <summary>
        /// Enable/disable attack for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="enabled">True to enable attack, false to disable</param>
        public static void SetAttackEnabled(int npcSessionID, bool enabled)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                if (enabled)
                {
                    // Reset next attack time to enable attack
                    updateInfo.NextAttackTime = DateTime.Now.AddMilliseconds(1000);
                }
                else
                {
                    // Set next attack time to far future to disable attack
                    updateInfo.NextAttackTime = DateTime.MaxValue;
                }
            }
        }

        /// <summary>
        /// Check if movement is enabled for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if movement is enabled</returns>
        public static bool IsMovementEnabled(int npcSessionID)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                return updateInfo.NextMoveTime != DateTime.MaxValue;
            }
            return false;
        }

        /// <summary>
        /// Check if attack is enabled for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if attack is enabled</returns>
        public static bool IsAttackEnabled(int npcSessionID)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                return updateInfo.NextAttackTime != DateTime.MaxValue;
            }
            return false;
        }

        /// <summary>
        /// Enable/disable respawn for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="enabled">True to enable respawn, false to disable</param>
        /// <param name="respawnInterval">Respawn interval in milliseconds (default: 30000)</param>
        public static void SetRespawnEnabled(int npcSessionID, bool enabled, int respawnInterval = 30000)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                if (enabled)
                {
                    updateInfo.RespawnInterval = respawnInterval;
                    updateInfo.NextRespawnTime = DateTime.Now.AddMilliseconds(respawnInterval);
                    LogHelper.WriteLine(LogLevel.Debug, $"NPC {updateInfo.NPC.Name} (ID: {updateInfo.NPC.FLD_PID}) respawn enabled, will respawn in {respawnInterval}ms");
                }
                else
                {
                    updateInfo.NextRespawnTime = DateTime.MaxValue;
                    LogHelper.WriteLine(LogLevel.Debug, $"NPC {updateInfo.NPC.Name} (ID: {updateInfo.NPC.FLD_PID}) respawn disabled");
                }
            }
            else
            {
                LogHelper.WriteLine(LogLevel.Warning, $"Attempted to set respawn for unregistered NPC with SessionID: {npcSessionID}");
            }
        }

        /// <summary>
        /// Check if respawn is enabled for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if respawn is enabled</returns>
        public static bool IsRespawnEnabled(int npcSessionID)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                return updateInfo.NextRespawnTime != DateTime.MaxValue;
            }
            return false;
        }

        /// <summary>
        /// Update respawn interval for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="newInterval">New respawn interval in milliseconds</param>
        public static void UpdateRespawnInterval(int npcSessionID, int newInterval)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                updateInfo.RespawnInterval = newInterval;
                // Update next respawn time if respawn is enabled
                if (updateInfo.NextRespawnTime != DateTime.MaxValue)
                {
                    updateInfo.NextRespawnTime = DateTime.Now.AddMilliseconds(newInterval);
                }
            }
        }
        
        /// <summary>
        /// Main timer event that processes all NPC updates
        /// </summary>
        private static void ProcessAllNPCUpdates(object sender, ElapsedEventArgs e)
        {
            try
            {
                var now = DateTime.Now;
                var processedCount = 0;
                var errorCount = 0;
                
                foreach (var kvp in _npcUpdates)
                {
                    var npcInfo = kvp.Value;
                    var npc = npcInfo.NPC;
                    
                    try
                    {
                        // Skip if NPC is disposed (but not if just dead - dead NPCs need respawn processing)
                        if (npc.NPC_Removed)
                        {
                            // Only log occasionally to avoid spam
                            if (now.Second % 60 == 0 && now.Millisecond < 200)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Skipping NPC {npc.Name} (ID: {npc.FLD_PID}) - Removed: {npc.NPC_Removed}");
                            }
                            continue;
                        }

                        // Process respawn first (for dead NPCs)
                        if (now >= npcInfo.NextRespawnTime)
                        {
                            ProcessNPCRespawn(npc, npcInfo, now);
                        }

                        // Only process movement and attack for alive NPCs
                        if (!npc.NPCDeath)
                        {
                            // Process movement
                            if (now >= npcInfo.NextMoveTime)
                            {
                                ProcessNPCMove(npc, npcInfo, now);
                                processedCount++;
                            }

                            // Process attack
                            if (now >= npcInfo.NextAttackTime)
                            {
                                ProcessNPCAttack(npc, npcInfo, now);
                            }
                        }
                        else
                        {
                            // Log dead NPCs occasionally
                            if (now.Second % 60 == 0 && now.Millisecond < 200)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Skipping NPC {npc.Name} (ID: {npc.FLD_PID}) - Dead: {npc.NPCDeath}, Removed: {npc.NPC_Removed}");
                            }
                        }

                        npcInfo.LastUpdateTime = now;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        LogHelper.WriteLine(LogLevel.Error, $"Error processing NPC {npc.NPC_SessionID}: {ex.Message}");
                        
                        // Remove problematic NPCs to prevent repeated errors
                        if (errorCount > 10)
                        {
                            _npcUpdates.TryRemove(kvp.Key, out _);
                        }
                    }
                }
                
                // Log statistics periodically
                if (now.Second % 30 == 0 && now.Millisecond < 200)
                {
                    var aliveCount = 0;
                    var deadCount = 0;
                    var respawnEnabledCount = 0;

                    foreach (var kvp in _npcUpdates)
                    {
                        var npcInfo = kvp.Value;
                        if (npcInfo.NPC.NPCDeath)
                            deadCount++;
                        else
                            aliveCount++;

                        if (npcInfo.NextRespawnTime != DateTime.MaxValue)
                            respawnEnabledCount++;
                    }

                    LogHelper.WriteLine(LogLevel.Debug, $"NPCTimerManager: Total {_npcUpdates.Count} NPCs (Alive: {aliveCount}, Dead: {deadCount}, Respawn enabled: {respawnEnabledCount}), {processedCount} moves this cycle");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Critical error in NPCTimerManager: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Process movement for a specific NPC
        /// </summary>
        private static void ProcessNPCMove(NpcClass npc, NPCUpdateInfo npcInfo, DateTime now)
        {
            try
            {
                // Call the NPC's movement logic
                npc.ProcessAutomaticMove();
                
                // Schedule next movement with some randomization
                var baseInterval = npcInfo.MoveInterval;
                var randomOffset = RNG.Next(-1000, 1000); // ±1 second variation
                var nextInterval = Math.Max(1000, baseInterval + randomOffset);
                
                npcInfo.NextMoveTime = now.AddMilliseconds(nextInterval);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in ProcessNPCMove for NPC {npc.NPC_SessionID}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Process attack for a specific NPC
        /// </summary>
        private static void ProcessNPCAttack(NpcClass npc, NPCUpdateInfo npcInfo, DateTime now)
        {
            try
            {
                // Call the NPC's attack logic
                npc.ProcessAutomaticAttack();

                // Schedule next attack (1 second interval)
                npcInfo.NextAttackTime = now.AddMilliseconds(1000);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in ProcessNPCAttack for NPC {npc.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Process respawn for a specific NPC
        /// </summary>
        private static void ProcessNPCRespawn(NpcClass npc, NPCUpdateInfo npcInfo, DateTime now)
        {
            try
            {
                // Call the NPC's respawn logic
                npc.ProcessAutomaticRespawn();

                // Disable respawn after processing (one-time event)
                // The NPC will re-enable respawn when it dies again
                npcInfo.NextRespawnTime = DateTime.MaxValue;

                LogHelper.WriteLine(LogLevel.Debug, $"NPC {npc.Name} (ID: {npc.FLD_PID}) respawned successfully");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in ProcessNPCRespawn for NPC {npc.NPC_SessionID}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Get statistics about the timer manager
        /// </summary>
        public static (int RegisteredNPCs, bool IsRunning) GetStatistics()
        {
            return (_npcUpdates.Count, _globalUpdateTimer.Enabled);
        }
        
        /// <summary>
        /// Shutdown the timer manager
        /// </summary>
        public static void Shutdown()
        {
            try
            {
                _globalUpdateTimer.Stop();
                _globalUpdateTimer.Dispose();
                _npcUpdates.Clear();
                _isInitialized = false;
                
                LogHelper.WriteLine(LogLevel.Info, "NPCTimerManager shutdown completed");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error during NPCTimerManager shutdown: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// Information about NPC update timing
    /// </summary>
    public class NPCUpdateInfo
    {
        public NpcClass NPC { get; set; }
        public DateTime NextMoveTime { get; set; }
        public DateTime NextAttackTime { get; set; }
        public DateTime NextRespawnTime { get; set; }
        public DateTime LastUpdateTime { get; set; }
        public int MoveInterval { get; set; }
        public int RespawnInterval { get; set; }
    }
}
