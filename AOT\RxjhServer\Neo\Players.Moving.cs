

using System;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer.HelperTools;
using RxjhServer.Systems;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer
{
    /// <summary>
    /// Movement data structure for safe packet parsing and validation
    /// </summary>
    public struct MovementData
    {
        public int Dst;
        public float CurrentX, CurrentY, CurrentZ;
        public float TargetX, TargetY, TargetZ;
        public float DistanceLeft;
        public byte MoveType;
        public DateTime Timestamp;
        public float CalculatedSpeed;
        public int SessionId;

        public byte[] BasePacketLength;

        public MovementData(int dst,float currentX, float currentY, float currentZ,
                           float targetX, float targetY, float targetZ,
                           float distanceLeft, byte moveType, int sessionId, byte[] basePacketLength)
        {
            Dst = dst;
            CurrentX = currentX;
            CurrentY = currentY;
            CurrentZ = currentZ;
            TargetX = targetX;
            TargetY = targetY;
            TargetZ = targetZ;
            DistanceLeft = distanceLeft;
            MoveType = moveType;
            SessionId = sessionId;
            Timestamp = DateTime.Now;
            CalculatedSpeed = 0f;
            BasePacketLength = basePacketLength; // Adjust as needed
        }
    }

    /// <summary>
    /// Configuration for movement validation system
    /// </summary>
    public static class MovementConfig
    {
        public static float SpeedTolerance = 1.15f;        // 15% tolerance for network lag
        public static float PositionTolerance = 1.25f;     // 25% tolerance for position validation
        public static int MaxViolationsPerMinute = 8;      // Max violations before action
        public static int ViolationResetTime = 30000;      // 30 seconds reset time
        public static bool EnablePositionCorrection = true;
        public static bool EnableSpeedLogging = true;
        public static float MinValidDistance = 0.1f;       // Minimum distance to consider as movement
        public static float MaxTeleportDistance = 250f;    // Max distance before considering teleport
    }

    public partial class Players
    {
        // Movement validation tracking
        private DateTime _lastMovementTime = DateTime.Now;
        private float _lastValidX, _lastValidY, _lastValidZ;
        private int _speedViolationCount = 0;
        private DateTime _lastViolationReset = DateTime.Now;

        public void MovingNeo(byte[] packetData, int length)
        {
            try
            {
                // Parse movement data safely
                var movementData = ParseMovementPacket(packetData, length);
                if (!movementData.HasValue)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Invalid movement packet from player {CharacterName}");
                    return;
                }

                // Process movement with new validation system
                ProcessMovementSecure(movementData.Value);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"MovingNeo error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Safely parse movement packet data
        /// </summary>
        private MovementData? ParseMovementPacket(byte[] packetData, int length)
        {
            try
            {
                if (packetData == null || length < 46)
                {
                    return null;
                }

                var sessionId = BitConverter.ToInt32(packetData, 4);
                if (sessionId != SessionID)
                {
                    return null;
                }

                var offset = 10;
                var dst = BitConverter.ToInt32(packetData, offset);
                var targetX = BitConverter.ToSingle(packetData, offset + 4);
                var targetZ = BitConverter.ToSingle(packetData, offset + 8);
                var targetY = BitConverter.ToSingle(packetData, offset + 12);
                var currentX = BitConverter.ToSingle(packetData, offset + 16);
                var currentZ = BitConverter.ToSingle(packetData, offset + 20);
                var currentY = BitConverter.ToSingle(packetData, offset + 24);
                var moveType = packetData[offset + 29];
                var distanceLeft = BitConverter.ToSingle(packetData, offset + 32);

                return new MovementData(dst,currentX, currentY, currentZ,
                                      targetX, targetY, targetZ,
                                      distanceLeft, moveType, sessionId,packetData);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ParseMovementPacket error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Secure movement processing with validation and anti-cheat
        /// </summary>
        private void ProcessMovementSecure(MovementData movement)
        {
            try
            {
                // Basic state validation
                if (!ValidatePlayerState())
                {
                    return;
                }

                // Reset violation counter if enough time has passed
                ResetViolationCounterIfNeeded();

                // Validate movement speed
                if (!ValidateMovementSpeed(movement))
                {
                    HandleSpeedViolation(movement);
                    return;
                }

                // Validate position
                if (!ValidatePosition(movement))
                {
                    HandlePositionViolation(movement);
                    return;
                }
                
                // Apply movement if all validations pass
                ApplyValidMovement(movement);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ProcessMovementSecure error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate basic player state for movement
        /// </summary>
        private bool ValidatePlayerState()
        {
            if (PlayerTuVong || TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(27))
            {
                return false;
            }

            if (TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(8) ||
                TrangThai_BatThuong.ContainsKey(24) || TrangThai_BatThuong.ContainsKey(23))
            {
                HeThongNhacNho("Trạng thái hiện tại không thể dịch chuyển!");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Reset violation counter if reset time has passed
        /// </summary>
        private void ResetViolationCounterIfNeeded()
        {
            var timeSinceLastReset = (int)DateTime.Now.Subtract(_lastViolationReset).TotalMilliseconds;
            if (timeSinceLastReset >= MovementSystem.Config.ViolationResetTime)
            {
                _speedViolationCount = 0;
                _lastViolationReset = DateTime.Now;
            }
        }

        /// <summary>
        /// Validate movement speed against maximum allowed speed
        /// </summary>
        private bool ValidateMovementSpeed(MovementData movement)
        {
            try
            {
                var timeDelta = (float)DateTime.Now.Subtract(_lastMovementTime).TotalMilliseconds;
                if (timeDelta <= 0) return true; // Skip validation for same-time packets

                var distance = CalculateDistance(PosX, PosY, movement.CurrentX, movement.CurrentY);
                if (distance < MovementSystem.Config.MinValidDistance) return true; // Skip micro-movements

                var maxAllowedSpeed = MovementSystem.CalculateMaxSpeed(this);

                if (!MovementSystem.IsMovementReasonable(distance, timeDelta, maxAllowedSpeed))
                {
                    MovementSystem.Statistics.RecordSpeedViolation();

                    if (MovementSystem.Config.EnableSpeedLogging)
                    {
                        var actualSpeed = (distance * 1000f) / timeDelta;
                        LogHelper.WriteLine(LogLevel.Warning,
                            $"Speed violation: Player {CharacterName} - Actual: {actualSpeed:F2}, Max: {maxAllowedSpeed:F2}, Distance: {distance:F2}, Time: {timeDelta:F2}ms");
                    }
                    return false;
                }

                MovementSystem.Statistics.RecordMovement();
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ValidateMovementSpeed error: {ex.Message}");
                return true; // Allow movement on validation error to prevent stuck players
            }
        }

        /// <summary>
        /// Validate position for teleport detection and terrain collision
        /// </summary>
        private bool ValidatePosition(MovementData movement)
        {
            try
            {
                if (!MovementSystem.Config.EnableTeleportDetection)
                {
                    return true;
                }

                // Check for teleportation (sudden large distance movement)
                var distance = CalculateDistance(PosX, PosY, movement.CurrentX, movement.CurrentY);
                if (MovementSystem.IsTeleportDistance(distance))
                {
                    MovementSystem.Statistics.RecordTeleportDetection();

                    LogHelper.WriteLine(LogLevel.Warning,
                        $"Teleport detected: Player {CharacterName} moved {distance:F2} pixels instantly");
                    return false;
                }

                // Additional position validations can be added here
                // - Terrain collision checking
                // - Map boundary validation
                // - No-clip detection

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ValidatePosition error: {ex.Message}");
                return true; // Allow movement on validation error
            }
        }

        /// <summary>
        /// Calculate distance between two points
        /// </summary>
        private float CalculateDistance(float x1, float y1, float x2, float y2)
        {
            var dx = x2 - x1;
            var dy = y2 - y1;
            return (float)Math.Sqrt(dx * dx + dy * dy);
        }

        /// <summary>
        /// Handle speed violation with progressive penalties
        /// </summary>
        private void HandleSpeedViolation(MovementData movement)
        {
            _speedViolationCount++;

            if (_speedViolationCount >= MovementSystem.Config.MaxViolationsPerMinute)
            {
                // Severe violation - disconnect player
                LogHelper.WriteLine(LogLevel.Error,
                    $"Player {CharacterName} disconnected for excessive speed violations ({_speedViolationCount})");

                if (Client != null)
                {
                    Client.Dispose();
                }
                return;
            }

            // Apply position correction
            if (MovementSystem.Config.EnablePositionCorrection)
            {
                CorrectPlayerPosition();
            }

            // Log violation for monitoring
            LogHelper.WriteLine(LogLevel.Warning,
                $"Speed violation #{_speedViolationCount} for player {CharacterName}");
        }

        /// <summary>
        /// Handle position violation (teleport detection)
        /// </summary>
        private void HandlePositionViolation(MovementData movement)
        {
            _speedViolationCount++; // Count as speed violation for simplicity

            LogHelper.WriteLine(LogLevel.Warning,
                $"Position violation for player {CharacterName} - possible teleport hack");

            // Always correct position for teleport violations
            CorrectPlayerPosition();
        }

        /// <summary>
        /// Correct player position to last valid position
        /// </summary>
        private void CorrectPlayerPosition()
        {
            try
            {
                MovementSystem.Statistics.RecordPositionCorrection();

                // Rollback to last valid position
                PosX = _lastValidX;
                PosY = _lastValidY;
                PosZ = _lastValidZ;
                TargetPositionX = _lastValidX;
                TargetPositionY = _lastValidY;

                // Send position correction packet to client
                SendPositionCorrection();

                LogHelper.WriteLine(LogLevel.Info,
                    $"Position corrected for player {CharacterName} to ({_lastValidX:F2}, {_lastValidY:F2})");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"CorrectPlayerPosition error: {ex.Message}");
            }
        }

        /// <summary>
        /// Send position correction packet to client
        /// </summary>
        private void SendPositionCorrection()
        {
            try
            {
                var array = Converter.HexStringToByte("AA5522005C0079001C000000000000000000000070410000C64465000000000000000000000055AA");
                Buffer.BlockCopy(BitConverter.GetBytes(PosX), 0, array, 14, 4);
                Buffer.BlockCopy(BitConverter.GetBytes(PosY), 0, array, 22, 4);
                Buffer.BlockCopy(BitConverter.GetBytes(PosZ), 0, array, 18, 4);
                Buffer.BlockCopy(BitConverter.GetBytes(MapID), 0, array, 26, 4);
                Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
                Client?.Send_Map_Data(array, array.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"SendPositionCorrection error: {ex.Message}");
            }
        }

        /// <summary>
        /// Apply valid movement after all validations pass
        /// </summary>
        private void ApplyValidMovement(MovementData movement)
        {
            try
            {
                // Store last valid position for fallback
                _lastValidX = PosX;
                _lastValidY = PosY;
                _lastValidZ = PosZ;
                _lastMovementTime = DateTime.Now;

                // Update player position
                PosX = movement.CurrentX;
                PosY = movement.CurrentY;
                PosZ = movement.CurrentZ;
                TargetPositionX = movement.TargetX;
                TargetPositionY = movement.TargetY;

                // Reset movement tracking variables
                if (TriggerMapMovementEvent)
                {
                    _yxsl = 0;
                }
                tracking_status = false;

                // Update movement calculation for legacy compatibility
                TocDo_TinhToan(movement.CurrentX, movement.CurrentY);
                _toaDoCuoiCungX = movement.CurrentX;
                _toaDoCuoiCungY = movement.CurrentY;

                // Handle offline mode positioning
                if (Offline_TreoMay_Mode_ON_OFF == 1)
                {
                    PosX = movement.TargetX;
                    PosY = movement.TargetY;
                }

                // Reset combat states
                PKTuVong = false;
                Player_VoDich = false;

                // Stop automatic systems
                StopAutomaticSystems();

                // Broadcast movement to other players
                BroadcastMovement(movement);

                // Update AOI and visibility
                UpdateVisibilityAndAOI();

                // Handle special map events
                HandleSpecialMapEvents();

                // Call movement hooks for zone management
                CallMovementHooks();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ApplyValidMovement error for player {CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Stop automatic systems when player moves
        /// </summary>
        private void StopAutomaticSystems()
        {
            if (AutomaticAttack != null)
            {
                AutomaticAttack.Enabled = false;
                AutomaticAttack.Close();
                AutomaticAttack.Dispose();
                AutomaticAttack = null;
            }

            if (AutomaticRecovery != null)
            {
                AutomaticRecovery.Enabled = false;
                AutomaticRecovery.Close();
                AutomaticRecovery.Dispose();
                AutomaticRecovery = null;
            }

            if (Automatic_Coordinates != null)
            {
                Automatic_Coordinates.Enabled = false;
                Automatic_Coordinates.Close();
                Automatic_Coordinates.Dispose();
                Automatic_Coordinates = null;
            }
        }

        /// <summary>
        /// Broadcast movement to nearby players
        /// </summary>
        private void BroadcastMovement(MovementData movement)
        {
            try
            {
                byte[] response = new byte[movement.BasePacketLength.Length];
                System.Buffer.BlockCopy(movement.BasePacketLength, 0, response, 0, movement.BasePacketLength.Length);
                System.Buffer.BlockCopy(BitConverter.GetBytes(base.SessionID), 0, response, 4, 2);
                System.Buffer.BlockCopy(BitConverter.GetBytes(movement.DistanceLeft), 0, response, 46, 4);
                response[6] = 101;
                System.Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, response, 18, 4);
                SendCurrentRangeBroadcastData(response, response.Length);
                // SendingClass w = new();
                // w.Write4(movement.Dst);
                // w.Write(movement.TargetX);
                // w.Write(15f);
                // w.Write(movement.TargetY);
                // w.Write(movement.CurrentX);
                // w.Write(movement.CurrentZ);
                // w.Write(movement.CurrentY);
                // w.Write1(1); 
                // w.Write1(movement.MoveType);
                // w.Write2(0);
                // w.Write(movement.DistanceLeft);
                // w.Write4(65535);
                // w.Write(movement.CurrentX);
                // w.Write(movement.CurrentY);
                // w.Write(movement.TargetX);
                // w.Write(movement.TargetY);
                // Create movement packet for broadcasting
                //var array = new byte[movement.BasePacketLength];

                // Build movement packet (simplified version)
                // Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array, 4, 2);
                // Buffer.BlockCopy(BitConverter.GetBytes(movement.Dst), 0, array, 10, 4);
                // Buffer.BlockCopy(BitConverter.GetBytes(movement.TargetX), 0, array, 14, 4);
                // Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, array, 18, 4);
                // Buffer.BlockCopy(BitConverter.GetBytes(movement.TargetY), 0, array, 22, 4);
                // Buffer.BlockCopy(BitConverter.GetBytes(movement.CurrentX), 0, array, 26, 4);
                // Buffer.BlockCopy(BitConverter.GetBytes(movement.CurrentZ), 0, array, 30, 4);
                // Buffer.BlockCopy(BitConverter.GetBytes(movement.CurrentY), 0, array, 34, 4);
                // array[6] = 101; // Movement packet type
                // Buffer.BlockCopy(BitConverter.GetBytes(movement.BasePacketLength - 12), 0, array, 8, 2);

                // Broadcast to nearby players
                // SendCurrentRangeBroadcastData(array, array.Length);
                //SendCurrentRangeBroadcastData(w, 25856, SessionID);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"BroadcastMovement error: {ex.Message}");
            }
        }

        /// <summary>
        /// Update visibility and AOI system
        /// </summary>
        private void UpdateVisibilityAndAOI()
        {
            try
            {
                // Check if position changed significantly
                if (TargetPositionX != PosX || TargetPositionY != PosY)
                {
                    TINH_TOAN_NHANVAT_TOADO();
                }

                // Update AOI and visibility
                if (!MoveAllto(50, ToaDoNew))
                {
                    GetTheReviewRangePlayers();
                    GetReviewScopeNpc();
                    ScanGroundItems();
                    TriggerAutomaticAttack();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"UpdateVisibilityAndAOI error: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle special map events and triggers
        /// </summary>
        private void HandleSpecialMapEvents()
        {
            try
            {
                // Handle special map 43001 event
                if (MapID == 43001)
                {
                    触发人物靠近();
                }

                // Handle racing event lock
                if (Event_TrangThai_Lock_DuongDua_F1 == 1 && World.Event_DuongDua_F1_ON_OFF != null)
                {
                    var timeSinceFreeze = (int)DateTime.Now.Subtract(Delay_DongBang).TotalMilliseconds;
                    if (GMMode == 0 && timeSinceFreeze > 4000)
                    {
                        Delay_DongBang = DateTime.Now;
                        foreach (var player in World.allConnectedChars.Values)
                        {
                            player.HeThongNhacNho($"[{CharacterName}] bị loại vì dẫm lên hoa !!", 7, "Thiên cơ các");
                        }
                        Send_IceBlock(SessionID);
                        UpdateMartialArtsAndStatus();
                        Event_TrangThai_Lock_DuongDua_F1 = 0;
                    }
                }

                // Handle racing event coordinate lock
                Lock_ToaDo_Event_DuongDua_HuyenBotPhai();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"HandleSpecialMapEvents error: {ex.Message}");
            }
        }

        /// <summary>
        /// Call movement hooks for zone management and cross-server functionality
        /// </summary>
        private void CallMovementHooks()
        {
            try
            {
                // Hook for cross-server zone handling
                //HookMove(PosX, PosY, PosZ);

                //// Hook for zone management
                //Hooks_PlayerMove.AfterPlayerMove(this);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"CallMovementHooks error: {ex.Message}");
            }
        }

        /// <summary>
        /// Parse legacy movement packet format used by CharacterMove
        /// </summary>
        private MovementData? ParseLegacyMovementPacket(byte[] packetData, int packetSize)
        {
            try
            {
                if (packetData == null || packetSize < 50)
                {
                    return null;
                }

                // Parse legacy packet format
                var array = new byte[4];
                var array2 = new byte[4];
                var dst = new byte[4];
                var array3 = new byte[4];
                var array4 = new byte[4];
                var dst2 = new byte[4];
                var array5 = new byte[4];
                var dst3 = new byte[4];
                var dst4 = new byte[4];
                var array6 = new byte[4];

                Buffer.BlockCopy(packetData, 4, array6, 0, 2);
                Buffer.BlockCopy(packetData, 10, dst3, 0, 4);
                Buffer.BlockCopy(packetData, 14, array, 0, 4);
                Buffer.BlockCopy(packetData, 18, dst, 0, 4);
                Buffer.BlockCopy(packetData, 22, array2, 0, 4);
                Buffer.BlockCopy(packetData, 26, array3, 0, 4);
                Buffer.BlockCopy(packetData, 30, dst2, 0, 4);
                Buffer.BlockCopy(packetData, 34, array4, 0, 4);
                Buffer.BlockCopy(packetData, 42, array5, 0, 4);
                Buffer.BlockCopy(packetData, 46, dst4, 0, 4);

                var sessionId = BitConverter.ToInt32(array6, 0);
                if (sessionId != SessionID)
                {
                    return null;
                }

                var targetX = BitConverter.ToSingle(array, 0);
                var targetY = BitConverter.ToSingle(array2, 0);
                var currentX = BitConverter.ToSingle(array3, 0);
                var currentY = BitConverter.ToSingle(array4, 0);
                var distanceLeft = BitConverter.ToSingle(array5, 0);

                // Validate basic packet data
                if (distanceLeft == 0.0f || CuaHangCaNhan != null || PlayerTuVong ||
                    NhanVat_HP <= 0 || Exiting || GiaoDich.GiaoDichBenTrong || InTheShop)
                {
                    return null;
                }

                return new MovementData(BitConverter.ToInt32(dst, 0),currentX, currentY, 15f, targetX, targetY, 15f, distanceLeft, 0, sessionId,packetData);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"ParseLegacyMovementPacket error: {ex.Message}");
                return null;
            }
        }

	/// <summary>
	/// Legacy CharacterMove method - now uses secure movement processing
	/// </summary>
	public void CharacterMove(byte[] packetData, int packetSize)
	{
		try
		{
			// Use new secure movement processing
			var movementData = ParseLegacyMovementPacket(packetData, packetSize);
			if (movementData.HasValue)
			{
				ProcessMovementSecure(movementData.Value);
				return;
			}

			// Fallback to legacy processing for compatibility (if needed)
			ProcessLegacyMovement(packetData, packetSize);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"CharacterMove error for player {CharacterName}: {ex.Message}");
		}
	}

	/// <summary>
	/// Legacy movement processing for backward compatibility
	/// </summary>
	private void ProcessLegacyMovement(byte[] packetData, int packetSize)
	{
		try
		{
			if (PlayerTuVong || TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(27))
			{
				return;
			}
			if (TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(8) || TrangThai_BatThuong.ContainsKey(24) || TrangThai_BatThuong.ContainsKey(23))
			{
				HeThongNhacNho("Trạng thái hiện tại không thể dịch chuyển!");
				return;
			}

			// Legacy packet parsing (kept for compatibility)
			var array = new byte[4];
			var array2 = new byte[4];
			var dst = new byte[4];
			var array3 = new byte[4];
			var array4 = new byte[4];
			var dst2 = new byte[4];
			var array5 = new byte[4];
			var dst3 = new byte[4];
			var dst4 = new byte[4];
			var array6 = new byte[4];
			Buffer.BlockCopy(packetData, 4, array6, 0, 2);
			Buffer.BlockCopy(packetData, 10, dst3, 0, 4);
			Buffer.BlockCopy(packetData, 14, array, 0, 4);
			Buffer.BlockCopy(packetData, 18, dst, 0, 4);
			Buffer.BlockCopy(packetData, 22, array2, 0, 4);
			Buffer.BlockCopy(packetData, 26, array3, 0, 4);
			Buffer.BlockCopy(packetData, 30, dst2, 0, 4);
			Buffer.BlockCopy(packetData, 34, array4, 0, 4);
			Buffer.BlockCopy(packetData, 42, array5, 0, 4);
			Buffer.BlockCopy(packetData, 46, dst4, 0, 4);
			var num = BitConverter.ToInt32(array6, 0);
			var dIchuyenCHuotDenToaDoCuoiCungX = BitConverter.ToSingle(array, 0);
			var num2 = BitConverter.ToSingle(array2, 0);
			_dIchuyenCHuotDenToaDoCuoiCungX = dIchuyenCHuotDenToaDoCuoiCungX;
			_dIchuyenCHuotDenToaDoCuoiCungY = num2;
			if (BitConverter.ToSingle(array5, 0) != 0.0 && num == SessionID && CuaHangCaNhan == null && !PlayerTuVong && NhanVat_HP > 0 && !Exiting && !GiaoDich.GiaoDichBenTrong && !InTheShop)
			{
				if (TriggerMapMovementEvent)
				{
					_yxsl = 0;
				}
				tracking_status = false;
				var num3 = BitConverter.ToSingle(array3, 0);
				var num4 = BitConverter.ToSingle(array4, 0);
				var value = BitConverter.ToSingle(array5, 0);
				TocDo_TinhToan(num3, num4);
				_toaDoCuoiCungX = num3;
				_toaDoCuoiCungY = num4;
				if (Offline_TreoMay_Mode_ON_OFF == 1)
				{
					num3 = BitConverter.ToSingle(array, 0);
					num4 = BitConverter.ToSingle(array2, 0);
				}
				PKTuVong = false;
				Player_VoDich = false;
				TargetPositionX = BitConverter.ToSingle(array, 0);
				TargetPositionY = BitConverter.ToSingle(array2, 0);
				if (AutomaticAttack != null)
				{
					AutomaticAttack.Enabled = false;
					AutomaticAttack.Close();
					AutomaticAttack.Dispose();
					AutomaticAttack = null;
				}
				if (AutomaticRecovery != null)
				{
					AutomaticRecovery.Enabled = false;
					AutomaticRecovery.Close();
					AutomaticRecovery.Dispose();
					AutomaticRecovery = null;
				}
				var array7 = new byte[packetSize];
				Buffer.BlockCopy(packetData, 0, array7, 0, packetSize);
				Buffer.BlockCopy(BitConverter.GetBytes(SessionID), 0, array7, 4, 2);
				Buffer.BlockCopy(BitConverter.GetBytes(value), 0, array7, 46, 4);
				array7[6] = 101;
				Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, array7, 18, 4);
				SendCurrentRangeBroadcastData(array7, array7.Length);
				if (MapID == 43001)
				{
					触发人物靠近();
				}
				if (TargetPositionX != PosX && TargetPositionY != PosY)
				{
					TINH_TOAN_NHANVAT_TOADO();
				}
				var num5 = Tinh_Toan_ToDo_Chuan(new Pointx(dIchuyenCHuotDenToaDoCuoiCungX, num2), new Pointx(PosX, PosY));
				if (num5 >= 100.0)
				{
					PosX = num3;
					PosY = num4;
					if (Automatic_Coordinates != null)
					{
						Automatic_Coordinates.Enabled = false;
						Automatic_Coordinates.Close();
						Automatic_Coordinates.Dispose();
						Automatic_Coordinates = null;
					}
					TINH_TOAN_NHANVAT_TOADO();
				}
				if (!MoveAllto(50, ToaDoNew))
				{
					GetTheReviewRangePlayers();
					GetReviewScopeNpc();
					ScanGroundItems();
					TriggerAutomaticAttack();
				}
				PosX = num3;
				PosY = num4;
			}
			if (Event_TrangThai_Lock_DuongDua_F1 == 1 && World.Event_DuongDua_F1_ON_OFF != null)
			{
				var num6 = (int)DateTime.Now.Subtract(Delay_DongBang).TotalMilliseconds;
				if (GMMode == 0 && num6 > 4000)
				{
					Delay_DongBang = DateTime.Now;
					foreach (var value2 in World.allConnectedChars.Values)
					{
						value2.HeThongNhacNho("[" + CharacterName + "] bi loại vi dẫm lên hoa !!", 7, "Thiên cơ các");
					}
					Send_IceBlock(SessionID);
					UpdateMartialArtsAndStatus();
					Event_TrangThai_Lock_DuongDua_F1 = 0;
				}
			}
			Lock_ToaDo_Event_DuongDua_HuyenBotPhai();

			// Hook để xử lý di chuyển trong Zone liên server
			try
			{
				// Gọi hook di chuyển để xử lý Zone liên server
				//HookMove(PosX, PosY, PosZ);

				//// Gọi hook để kiểm tra và cập nhật zone của người chơi dựa theo vị trí
				//Hooks_PlayerMove.AfterPlayerMove(this);
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, "Lỗi khi xử lý hook di chuyển: " + ex.Message);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Legacy moving error: " + ex.Message);
		}
	}

    }
}

