using System;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer;

public class HcItimesClass
{
	private int int_0;

	private byte[] _VatPham;

	private byte[] _VatPham_id;

	private int int_1;

	private int int_2;

	private int int_3;

	private int int_4;

	private int int_5;

	private Itimesx itimesx_0;

	private Itimesx itimesx_1;

	private Itimesx itimesx_2;

	private Itimesx itimesx_3;

	public int Position
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public byte[] VatPham
	{
		get
		{
			return _VatPham;
		}
		set
		{
			_VatPham = value;
		}
	}

	public byte[] VatPham_id
	{
		get
		{
			return DatDuocVatPhamId();
		}
		set
		{
			_VatPham_id = value;
		}
	}

	public byte[] ItemGlobal_ID => DatDuocGlobal_ID();

	public byte[] VatPhamSoLuong
	{
		get
		{
			return DatDuocVatPhamSoLuong();
		}
		set
		{
			ThietLap_VatPhamSoLuong(value);
		}
	}

	public byte[] VatPham_ThuocTinh => DatDuocVatPham_ThuocTinh();

	public int CuongHoaLoaiHinh
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int CuongHoaSoLuong
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int GiaiDoanLoaiHinh
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int KhiCongThuocTinhLoaiHinh
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public int GiaiDoanSoLuong
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public Itimesx ThuocTinh1
	{
		get
		{
			return itimesx_0;
		}
		set
		{
			itimesx_0 = value;
		}
	}

	public Itimesx ThuocTinh2
	{
		get
		{
			return itimesx_1;
		}
		set
		{
			itimesx_1 = value;
		}
	}

	public Itimesx ThuocTinh3
	{
		get
		{
			return itimesx_2;
		}
		set
		{
			itimesx_2 = value;
		}
	}

	public Itimesx ThuocTinh4
	{
		get
		{
			return itimesx_3;
		}
		set
		{
			itimesx_3 = value;
		}
	}

	public int FLD_FJ_ThucTinh
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham, 62, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham, 62, 4);
		}
	}

	public int FLD_FJ_SucManh4ViThan
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham, 71, array, 0, 1);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham, 71, 1);
		}
	}

	public int FLD_FJ_NJ
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(VatPham, 60, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham, 60, 2);
		}
	}

	public int FLD_FJ_TrungCapPhuHon
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(VatPham, 40, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			if (value > 0)
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, VatPham, 38, 2);
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham, 40, 2);
		}
	}

	public byte[] DatDuocVatPham_ThuocTinh()
	{
		var array = new byte[56];
		try
		{
			System.Buffer.BlockCopy(VatPham, 16, array, 0, 56);
			return array;
		}
		catch
		{
			return array;
		}
	}

	public byte[] DatDuocVatPhamId()
	{
		var array = new byte[4];
		try
		{
			System.Buffer.BlockCopy(VatPham, 8, array, 0, 4);
			return array;
		}
		catch
		{
			return array;
		}
	}

	public byte[] DatDuocVatPhamSoLuong()
	{
		var array = new byte[4];
		try
		{
			System.Buffer.BlockCopy(VatPham, 12, array, 0, 4);
			return array;
		}
		catch
		{
			return array;
		}
	}

	public void ThietLap_VatPhamSoLuong(byte[] byte_0)
	{
		System.Buffer.BlockCopy(byte_0, 0, VatPham, 12, 4);
	}

	public void ThietLap_ThuocTinh()
	{
		try
		{
			var s = "00000000";
			var s2 = "00000000";
			var s3 = "00000000";
			var s4 = "00000000";
			if (World.PhaiChang_HoTroMoRong_CacVatPham_ChuSo == 0)
			{
				if (ThuocTinh1.ThuocTinhSoLuong != 0)
				{
					s = ((ThuocTinh1.ThuocTinhSoLuong < 10) ? ((ThuocTinh1.ThuocTinhLoaiHinh != 8 || ThuocTinh1.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh1.ThuocTinhLoaiHinh + "000000" + ThuocTinh1.ThuocTinhSoLuong) : ((ThuocTinh1.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh1.ThuocTinhLoaiHinh + "00" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh1.ThuocTinhSoLuong) : (ThuocTinh1.ThuocTinhLoaiHinh + "000" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh1.ThuocTinhSoLuong))) : ((ThuocTinh1.ThuocTinhLoaiHinh != 8 || ThuocTinh1.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh1.ThuocTinhLoaiHinh + "00000" + ThuocTinh1.ThuocTinhSoLuong) : ((ThuocTinh1.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh1.ThuocTinhLoaiHinh + "00" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh1.ThuocTinhSoLuong) : (ThuocTinh1.ThuocTinhLoaiHinh + "000" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh1.ThuocTinhSoLuong))));
				}
				if (ThuocTinh2.ThuocTinhSoLuong != 0)
				{
					s2 = ((ThuocTinh2.ThuocTinhSoLuong < 10) ? ((ThuocTinh2.ThuocTinhLoaiHinh != 8 || ThuocTinh2.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh2.ThuocTinhLoaiHinh + "000000" + ThuocTinh2.ThuocTinhSoLuong) : ((ThuocTinh2.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh2.ThuocTinhLoaiHinh + "00" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh2.ThuocTinhSoLuong) : (ThuocTinh2.ThuocTinhLoaiHinh + "000" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh2.ThuocTinhSoLuong))) : ((ThuocTinh2.ThuocTinhLoaiHinh != 8 || ThuocTinh2.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh2.ThuocTinhLoaiHinh + "00000" + ThuocTinh2.ThuocTinhSoLuong) : ((ThuocTinh2.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh2.ThuocTinhLoaiHinh + "00" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh2.ThuocTinhSoLuong) : (ThuocTinh2.ThuocTinhLoaiHinh + "000" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh2.ThuocTinhSoLuong))));
				}
				if (ThuocTinh3.ThuocTinhSoLuong != 0)
				{
					s3 = ((ThuocTinh3.ThuocTinhSoLuong < 10) ? ((ThuocTinh3.ThuocTinhLoaiHinh != 8 || ThuocTinh3.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh3.ThuocTinhLoaiHinh + "000000" + ThuocTinh3.ThuocTinhSoLuong) : ((ThuocTinh3.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh3.ThuocTinhLoaiHinh + "00" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh3.ThuocTinhSoLuong) : (ThuocTinh3.ThuocTinhLoaiHinh + "000" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh3.ThuocTinhSoLuong))) : ((ThuocTinh3.ThuocTinhLoaiHinh != 8 || ThuocTinh3.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh3.ThuocTinhLoaiHinh + "00000" + ThuocTinh3.ThuocTinhSoLuong) : ((ThuocTinh3.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh3.ThuocTinhLoaiHinh + "00" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh3.ThuocTinhSoLuong) : (ThuocTinh3.ThuocTinhLoaiHinh + "000" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh3.ThuocTinhSoLuong))));
				}
				if (ThuocTinh4.ThuocTinhSoLuong != 0)
				{
					s4 = ((ThuocTinh4.ThuocTinhSoLuong < 10) ? ((ThuocTinh4.ThuocTinhLoaiHinh != 8 || ThuocTinh4.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh4.ThuocTinhLoaiHinh + "000000" + ThuocTinh4.ThuocTinhSoLuong) : ((ThuocTinh4.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh4.ThuocTinhLoaiHinh + "00" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh4.ThuocTinhSoLuong) : (ThuocTinh4.ThuocTinhLoaiHinh + "000" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh4.ThuocTinhSoLuong))) : ((ThuocTinh4.ThuocTinhLoaiHinh != 8 || ThuocTinh4.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh4.ThuocTinhLoaiHinh + "00000" + ThuocTinh4.ThuocTinhSoLuong) : ((ThuocTinh4.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh4.ThuocTinhLoaiHinh + "00" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh4.ThuocTinhSoLuong) : (ThuocTinh4.ThuocTinhLoaiHinh + "000" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh4.ThuocTinhSoLuong))));
				}
			}
			else
			{
				if (ThuocTinh1.ThuocTinhSoLuong != 0)
				{
					switch (ThuocTinh1.ThuocTinhSoLuong.ToString().Length)
					{
					case 1:
						s = ((ThuocTinh1.ThuocTinhLoaiHinh != 8 || ThuocTinh1.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh1.ThuocTinhLoaiHinh + "000000" + ThuocTinh1.ThuocTinhSoLuong) : ((ThuocTinh1.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh1.ThuocTinhLoaiHinh + "00" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh1.ThuocTinhSoLuong) : (ThuocTinh1.ThuocTinhLoaiHinh + "000" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh1.ThuocTinhSoLuong)));
						break;
					case 2:
						s = ThuocTinh1.ThuocTinhLoaiHinh + "00000" + ThuocTinh1.ThuocTinhSoLuong;
						break;
					case 3:
						s = ThuocTinh1.ThuocTinhLoaiHinh + "0000" + ThuocTinh1.ThuocTinhSoLuong;
						break;
					case 4:
						s = ThuocTinh1.ThuocTinhLoaiHinh + "000" + ThuocTinh1.ThuocTinhSoLuong;
						break;
					case 5:
						s = ThuocTinh1.ThuocTinhLoaiHinh + "00" + ThuocTinh1.ThuocTinhSoLuong;
						break;
					}
				}
				if (ThuocTinh2.ThuocTinhSoLuong != 0)
				{
					switch (ThuocTinh2.ThuocTinhSoLuong.ToString().Length)
					{
					case 1:
						s2 = ((ThuocTinh2.ThuocTinhLoaiHinh != 8 || ThuocTinh2.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh2.ThuocTinhLoaiHinh + "000000" + ThuocTinh2.ThuocTinhSoLuong) : ((ThuocTinh2.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh2.ThuocTinhLoaiHinh + "00" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh2.ThuocTinhSoLuong) : (ThuocTinh2.ThuocTinhLoaiHinh + "000" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh2.ThuocTinhSoLuong)));
						break;
					case 2:
						s2 = ThuocTinh2.ThuocTinhLoaiHinh + "00000" + ThuocTinh2.ThuocTinhSoLuong;
						break;
					case 3:
						s2 = ThuocTinh2.ThuocTinhLoaiHinh + "0000" + ThuocTinh2.ThuocTinhSoLuong;
						break;
					case 4:
						s2 = ThuocTinh2.ThuocTinhLoaiHinh + "000" + ThuocTinh2.ThuocTinhSoLuong;
						break;
					case 5:
						s2 = ThuocTinh2.ThuocTinhLoaiHinh + "00" + ThuocTinh2.ThuocTinhSoLuong;
						break;
					}
				}
				if (ThuocTinh3.ThuocTinhSoLuong != 0)
				{
					switch (ThuocTinh3.ThuocTinhSoLuong.ToString().Length)
					{
					case 1:
						s3 = ((ThuocTinh3.ThuocTinhLoaiHinh != 8 || ThuocTinh3.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh3.ThuocTinhLoaiHinh + "000000" + ThuocTinh3.ThuocTinhSoLuong) : ((ThuocTinh3.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh3.ThuocTinhLoaiHinh + "00" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh3.ThuocTinhSoLuong) : (ThuocTinh3.ThuocTinhLoaiHinh + "000" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh3.ThuocTinhSoLuong)));
						break;
					case 2:
						s3 = ThuocTinh3.ThuocTinhLoaiHinh + "00000" + ThuocTinh3.ThuocTinhSoLuong;
						break;
					case 3:
						s3 = ThuocTinh3.ThuocTinhLoaiHinh + "0000" + ThuocTinh3.ThuocTinhSoLuong;
						break;
					case 4:
						s3 = ThuocTinh3.ThuocTinhLoaiHinh + "000" + ThuocTinh3.ThuocTinhSoLuong;
						break;
					case 5:
						s3 = ThuocTinh3.ThuocTinhLoaiHinh + "00" + ThuocTinh3.ThuocTinhSoLuong;
						break;
					}
				}
				if (ThuocTinh4.ThuocTinhSoLuong != 0)
				{
					switch (ThuocTinh4.ThuocTinhSoLuong.ToString().Length)
					{
					case 1:
						s4 = ((ThuocTinh4.ThuocTinhLoaiHinh != 8 || ThuocTinh4.KhiCongThuocTinhLoaiHinh == 0) ? (ThuocTinh4.ThuocTinhLoaiHinh + "000000" + ThuocTinh4.ThuocTinhSoLuong) : ((ThuocTinh4.KhiCongThuocTinhLoaiHinh.ToString().Length > 2) ? (ThuocTinh4.ThuocTinhLoaiHinh + "00" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh4.ThuocTinhSoLuong) : (ThuocTinh4.ThuocTinhLoaiHinh + "000" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" + ThuocTinh4.ThuocTinhSoLuong)));
						break;
					case 2:
						s4 = ThuocTinh4.ThuocTinhLoaiHinh + "00000" + ThuocTinh4.ThuocTinhSoLuong;
						break;
					case 3:
						s4 = ThuocTinh4.ThuocTinhLoaiHinh + "0000" + ThuocTinh4.ThuocTinhSoLuong;
						break;
					case 4:
						s4 = ThuocTinh4.ThuocTinhLoaiHinh + "000" + ThuocTinh4.ThuocTinhSoLuong;
						break;
					case 5:
						s4 = ThuocTinh4.ThuocTinhLoaiHinh + "00" + ThuocTinh4.ThuocTinhSoLuong;
						break;
					}
				}
			}
			var bytes = BitConverter.GetBytes(int.Parse(s));
			var bytes2 = BitConverter.GetBytes(int.Parse(s2));
			var bytes3 = BitConverter.GetBytes(int.Parse(s3));
			var bytes4 = BitConverter.GetBytes(int.Parse(s4));
			var vatPham = VatPham;
			System.Buffer.BlockCopy(bytes, 0, vatPham, 20, 4);
			System.Buffer.BlockCopy(bytes2, 0, VatPham, 24, 4);
			System.Buffer.BlockCopy(bytes3, 0, VatPham, 28, 4);
			System.Buffer.BlockCopy(bytes4, 0, VatPham, 32, 4);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "sự tổng hợp Thiet Lap_Thuoc Tinh error 1：" + ex);
		}
	}

	public void DatDuocThuocTinh()
	{
		try
		{
			var array = new byte[4];
			var array2 = new byte[4];
			var array3 = new byte[4];
			var array4 = new byte[4];
			System.Buffer.BlockCopy(VatPham, 20, array, 0, 4);
			System.Buffer.BlockCopy(VatPham, 24, array2, 0, 4);
			System.Buffer.BlockCopy(VatPham, 28, array3, 0, 4);
			System.Buffer.BlockCopy(VatPham, 32, array4, 0, 4);
			ThuocTinh1 = new(array);
			ThuocTinh2 = new(array2);
			ThuocTinh3 = new(array3);
			ThuocTinh4 = new(array4);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "sự tổng hợp Thiet Lap_Thuoc Tinh error 2：" + ex);
		}
	}

	public void ThietLap_GiaiDoanThuocTinh()
	{
		try
		{
			var s = "00000000";
			var s2 = "0000000000";
			if (CuongHoaSoLuong != 0)
			{
				string text3;
				if (CuongHoaSoLuong >= 10)
				{
					var text = CuongHoaLoaiHinh.ToString();
					var text2 = CuongHoaSoLuong.ToString();
					text3 = text + "00000" + text2;
				}
				else
				{
					var text4 = CuongHoaLoaiHinh.ToString();
					var text5 = CuongHoaSoLuong.ToString();
					text3 = text4 + "000000" + text5;
				}
				s = text3;
			}
			if (GiaiDoanSoLuong != 0)
			{
				GiaiDoanSoLuong--;
				var text6 = GiaiDoanLoaiHinh.ToString();
				var text7 = GiaiDoanSoLuong.ToString();
				s2 = "100000" + text6 + text7 + "00";
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(int.Parse(s) + int.Parse(s2)), 0, VatPham, 16, 4);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "sự tổng hợp Thiet Lap_Giai Doan Thuoc Tinh error：" + ex);
		}
	}

	public byte[] DatDuocGlobal_ID()
	{
		var array = new byte[8];
		try
		{
			System.Buffer.BlockCopy(VatPham, 0, array, 0, 8);
			return array;
		}
		catch
		{
			return array;
		}
	}

	public void CuongHoaThuocTinhGiaiDoan()
	{
		try
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham, 16, array, 0, 4);
			var text = BitConverter.ToInt32(array, 0).ToString();
			switch (text.Length)
			{
			case 3:
			case 4:
			case 5:
				break;
			case 2:
				GiaiDoanSoLuong = int.Parse(text.Substring(0, 2));
				break;
			case 6:
				GiaiDoanLoaiHinh = int.Parse(text.Substring(0, 1));
				if (GiaiDoanLoaiHinh == 8)
				{
					KhiCongThuocTinhLoaiHinh = int.Parse(text.Substring(1, 3));
					GiaiDoanSoLuong = int.Parse(text.Substring(4, 2));
				}
				else if (World.PhaiChang_HoTroMoRong_CacVatPham_ChuSo == 0)
				{
					GiaiDoanSoLuong = int.Parse(text.Substring(4, 2));
				}
				else
				{
					GiaiDoanSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 1)) * 100000;
				}
				break;
			case 7:
				GiaiDoanLoaiHinh = int.Parse(text.Substring(0, 1));
				if (GiaiDoanLoaiHinh == 2)
				{
					GiaiDoanLoaiHinh = int.Parse(text.Substring(3, 1));
					break;
				}
				GiaiDoanLoaiHinh = int.Parse(text.Substring(0, 2));
				if (World.PhaiChang_HoTroMoRong_CacVatPham_ChuSo == 0)
				{
					GiaiDoanSoLuong = int.Parse(text.Substring(5, 2));
				}
				else
				{
					GiaiDoanSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 2)) * 100000;
				}
				break;
			case 8:
				CuongHoaLoaiHinh = int.Parse(text.Substring(0, 1));
				CuongHoaSoLuong = int.Parse(text.Substring(text.Length - 2, 2));
				break;
			case 9:
				CuongHoaLoaiHinh = int.Parse(text.Substring(0, 2));
				CuongHoaSoLuong = int.Parse(text.Substring(text.Length - 2, 2));
				break;
			case 10:
				GiaiDoanLoaiHinh = int.Parse(text.Substring(6, 1));
				GiaiDoanSoLuong = int.Parse(text.Substring(7, 1)) + 1;
				CuongHoaLoaiHinh = int.Parse(text.Substring(2, 1));
				CuongHoaSoLuong = int.Parse(text.Substring(text.Length - 2, 2));
				break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "sự tổng hợp Cuong Hoa Thuoc Tinh Giai Doan error：" + ex);
		}
	}
}
