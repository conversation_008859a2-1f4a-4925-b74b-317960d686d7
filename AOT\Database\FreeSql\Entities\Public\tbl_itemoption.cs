﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_itemoption {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? bonus_hp { get; set; }

		[JsonProperty]
		public int? bonus_percenthp { get; set; }

		[JsonProperty]
		public int? bonus_mp { get; set; }

		[JsonProperty]
		public int? bonus_percentmp { get; set; }

		[JsonProperty]
		public int? bonus_atk { get; set; }

		[JsonProperty]
		public int? bonus_percentatk { get; set; }

		[JsonProperty]
		public int? bonus_percentdf { get; set; }

		[JsonProperty]
		public int? bonus_df { get; set; }

		[JsonProperty]
		public int? bonus_percentatkskill { get; set; }

		[JsonProperty]
		public int? bonus_defskill { get; set; }

		[JsonProperty]
		public int? bonus_qigong { get; set; }

		[JsonProperty]
		public int? bonus_dropgold { get; set; }

		[JsonProperty]
		public int? bonus_exp { get; set; }

		[JsonProperty]
		public int? bonus_lucky { get; set; }

		[JsonProperty]
		public int? bonus_accuracy { get; set; }

		[JsonProperty]
		public int? bonus_evasion { get; set; }

		[JsonProperty]
		public int? bonus_diemhoangkim { get; set; }

		[JsonProperty]
		public int? bonus_atkmonster { get; set; }

		[JsonProperty]
		public int? bonus_defmonster { get; set; }

	}

}
