using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Services;
using HeroYulgang.Core.Native.Network;
using HeroYulgang.Core.Native.Packets;

namespace HeroYulgang.Core.Native.Sessions
{
    /// <summary>
    /// Player session interface - thay thế ClientActor state management
    /// </summary>
    public interface IPlayerSession
    {
        /// <summary>
        /// Session ID
        /// </summary>
        int SessionId { get; }
        
        /// <summary>
        /// Player instance
        /// </summary>
        object? Player { get; set; }
        
        /// <summary>
        /// Authentication state
        /// </summary>
        bool IsAuthenticated { get; set; }
        
        /// <summary>
        /// Connection state
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// Login timestamp
        /// </summary>
        DateTime? LoginTime { get; set; }
        
        /// <summary>
        /// Account ID
        /// </summary>
        int? AccountId { get; set; }
        
        /// <summary>
        /// Character name
        /// </summary>
        string? CharacterName { get; set; }
        
        /// <summary>
        /// Session properties
        /// </summary>
        IDictionary<string, object> Properties { get; }
        
        /// <summary>
        /// Network session
        /// </summary>
        IClientSession NetworkSession { get; }
        
        /// <summary>
        /// Packet context
        /// </summary>
        IPacketContext PacketContext { get; }
    }

    /// <summary>
    /// Session manager interface - thay thế ClientActor management
    /// </summary>
    public interface ISessionManager : IService
    {
        /// <summary>
        /// Create player session from network session
        /// </summary>
        Task<IPlayerSession> CreatePlayerSessionAsync(IClientSession networkSession);
        
        /// <summary>
        /// Get player session by ID
        /// </summary>
        Task<IPlayerSession?> GetPlayerSessionAsync(int sessionId);
        
        /// <summary>
        /// Get player session by account ID
        /// </summary>
        Task<IPlayerSession?> GetPlayerSessionByAccountAsync(int accountId);
        
        /// <summary>
        /// Get all active player sessions
        /// </summary>
        Task<IEnumerable<IPlayerSession>> GetActiveSessionsAsync();
        
        /// <summary>
        /// Close player session
        /// </summary>
        Task ClosePlayerSessionAsync(int sessionId, string reason = "Session closed");
        
        /// <summary>
        /// Authenticate session
        /// </summary>
        Task<bool> AuthenticateSessionAsync(int sessionId, int accountId, string characterName);
        
        /// <summary>
        /// Set player for session
        /// </summary>
        Task SetPlayerAsync(int sessionId, object player);
        
        /// <summary>
        /// Get session events
        /// </summary>
        IAsyncEnumerable<SessionManagerEvent> GetEventsAsync();
        
        /// <summary>
        /// Session count
        /// </summary>
        int SessionCount { get; }
    }

    /// <summary>
    /// Authentication service interface
    /// </summary>
    public interface IAuthenticationService : IService
    {
        /// <summary>
        /// Authenticate user credentials
        /// </summary>
        Task<AuthenticationResult> AuthenticateAsync(string username, string password, int sessionId);
        
        /// <summary>
        /// Validate session token
        /// </summary>
        Task<bool> ValidateSessionAsync(int sessionId, string token);
        
        /// <summary>
        /// Create session token
        /// </summary>
        Task<string> CreateSessionTokenAsync(int accountId, int sessionId);
        
        /// <summary>
        /// Revoke session
        /// </summary>
        Task RevokeSessionAsync(int sessionId);
        
        /// <summary>
        /// Check if account is already logged in
        /// </summary>
        Task<bool> IsAccountLoggedInAsync(int accountId);
    }

    /// <summary>
    /// Authentication result
    /// </summary>
    public class AuthenticationResult
    {
        public bool Success { get; set; }
        public int AccountId { get; set; }
        public string? CharacterName { get; set; }
        public string? ErrorMessage { get; set; }
        public string? SessionToken { get; set; }
        public object? PlayerData { get; set; }
    }

    /// <summary>
    /// Session manager events
    /// </summary>
    public abstract class SessionManagerEvent
    {
        public DateTime Timestamp { get; } = DateTime.UtcNow;
        public int SessionId { get; }
        public abstract string EventType { get; }
        
        protected SessionManagerEvent(int sessionId)
        {
            SessionId = sessionId;
        }
    }

    public class PlayerSessionCreatedEvent : SessionManagerEvent
    {
        public override string EventType => "PlayerSessionCreated";
        public string RemoteEndPoint { get; }
        
        public PlayerSessionCreatedEvent(int sessionId, string remoteEndPoint) : base(sessionId)
        {
            RemoteEndPoint = remoteEndPoint;
        }
    }

    public class PlayerSessionAuthenticatedEvent : SessionManagerEvent
    {
        public override string EventType => "PlayerSessionAuthenticated";
        public int AccountId { get; }
        public string CharacterName { get; }
        
        public PlayerSessionAuthenticatedEvent(int sessionId, int accountId, string characterName) : base(sessionId)
        {
            AccountId = accountId;
            CharacterName = characterName;
        }
    }

    public class PlayerSessionClosedEvent : SessionManagerEvent
    {
        public override string EventType => "PlayerSessionClosed";
        public string Reason { get; }
        public TimeSpan Duration { get; }
        
        public PlayerSessionClosedEvent(int sessionId, string reason, TimeSpan duration) : base(sessionId)
        {
            Reason = reason;
            Duration = duration;
        }
    }

    public class PlayerConnectedEvent : SessionManagerEvent
    {
        public override string EventType => "PlayerConnected";
        public int AccountId { get; }
        public string CharacterName { get; }
        
        public PlayerConnectedEvent(int sessionId, int accountId, string characterName) : base(sessionId)
        {
            AccountId = accountId;
            CharacterName = characterName;
        }
    }

    public class PlayerDisconnectedEvent : SessionManagerEvent
    {
        public override string EventType => "PlayerDisconnected";
        public int AccountId { get; }
        public string CharacterName { get; }
        public string Reason { get; }
        
        public PlayerDisconnectedEvent(int sessionId, int accountId, string characterName, string reason) : base(sessionId)
        {
            AccountId = accountId;
            CharacterName = characterName;
            Reason = reason;
        }
    }

    /// <summary>
    /// Session configuration
    /// </summary>
    public class SessionConfiguration
    {
        /// <summary>
        /// Session timeout
        /// </summary>
        public TimeSpan SessionTimeout { get; set; } = TimeSpan.FromMinutes(30);
        
        /// <summary>
        /// Authentication timeout
        /// </summary>
        public TimeSpan AuthenticationTimeout { get; set; } = TimeSpan.FromMinutes(5);
        
        /// <summary>
        /// Max sessions per account
        /// </summary>
        public int MaxSessionsPerAccount { get; set; } = 1;
        
        /// <summary>
        /// Enable session persistence
        /// </summary>
        public bool EnableSessionPersistence { get; set; } = false;
        
        /// <summary>
        /// Session cleanup interval
        /// </summary>
        public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromMinutes(5);
        
        /// <summary>
        /// Enable duplicate login prevention
        /// </summary>
        public bool PreventDuplicateLogins { get; set; } = true;
    }

    /// <summary>
    /// Session statistics
    /// </summary>
    public class SessionStatistics
    {
        public int TotalSessions { get; set; }
        public int ActiveSessions { get; set; }
        public int AuthenticatedSessions { get; set; }
        public int PendingAuthentication { get; set; }
        public TimeSpan AverageSessionDuration { get; set; }
        public DateTime StartTime { get; set; }
        public TimeSpan Uptime => DateTime.UtcNow - StartTime;
        
        public Dictionary<string, int> SessionsByState { get; set; } = new();
        public Dictionary<int, int> SessionsByAccount { get; set; } = new();
    }

    /// <summary>
    /// Player session factory interface
    /// </summary>
    public interface IPlayerSessionFactory
    {
        /// <summary>
        /// Create player session
        /// </summary>
        IPlayerSession CreatePlayerSession(IClientSession networkSession, IPacketContext packetContext);
    }

    /// <summary>
    /// Session persistence interface
    /// </summary>
    public interface ISessionPersistence
    {
        /// <summary>
        /// Save session state
        /// </summary>
        Task SaveSessionAsync(IPlayerSession session);
        
        /// <summary>
        /// Load session state
        /// </summary>
        Task<IPlayerSession?> LoadSessionAsync(int sessionId);
        
        /// <summary>
        /// Delete session state
        /// </summary>
        Task DeleteSessionAsync(int sessionId);
        
        /// <summary>
        /// Get all persisted sessions
        /// </summary>
        Task<IEnumerable<IPlayerSession>> GetPersistedSessionsAsync();
    }
}
