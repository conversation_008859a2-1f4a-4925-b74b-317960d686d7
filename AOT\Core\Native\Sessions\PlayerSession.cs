using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Network;
using HeroYulgang.Core.Native.Packets;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Sessions
{
    /// <summary>
    /// Player session implementation - thay thế ClientActor state management
    /// </summary>
    public class PlayerSession : IPlayerSession, IDisposable
    {
        private readonly IClientSession _networkSession;
        private readonly IPacketContext _packetContext;
        private readonly Dictionary<string, object> _properties;
        private volatile bool _isDisposed;

        public PlayerSession(IClientSession networkSession, IPacketContext packetContext)
        {
            _networkSession = networkSession ?? throw new ArgumentNullException(nameof(networkSession));
            _packetContext = packetContext ?? throw new ArgumentNullException(nameof(packetContext));
            _properties = new Dictionary<string, object>();
            
            CreatedAt = DateTime.UtcNow;
            LastActivity = DateTime.UtcNow;
            
            Logger.Instance.Debug($"Created player session {SessionId}");
        }

        public int SessionId => _networkSession.SessionId;
        public object? Player { get; set; }
        public bool IsAuthenticated { get; set; }
        public bool IsConnected => _networkSession.IsActive && !_isDisposed;
        public DateTime? LoginTime { get; set; }
        public int? AccountId { get; set; }
        public string? CharacterName { get; set; }
        public IDictionary<string, object> Properties => _properties;
        public IClientSession NetworkSession => _networkSession;
        public IPacketContext PacketContext => _packetContext;

        /// <summary>
        /// Session creation time
        /// </summary>
        public DateTime CreatedAt { get; }

        /// <summary>
        /// Last activity time
        /// </summary>
        public DateTime LastActivity { get; private set; }

        /// <summary>
        /// Remote endpoint
        /// </summary>
        public string RemoteEndPoint => _networkSession.RemoteEndPoint.ToString();

        /// <summary>
        /// Update last activity
        /// </summary>
        public void UpdateActivity()
        {
            LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Get session property
        /// </summary>
        public T? GetProperty<T>(string key)
        {
            lock (_properties)
            {
                return _properties.TryGetValue(key, out var value) && value is T typedValue ? typedValue : default;
            }
        }

        /// <summary>
        /// Set session property
        /// </summary>
        public void SetProperty<T>(string key, T value)
        {
            lock (_properties)
            {
                if (value != null)
                {
                    _properties[key] = value;
                }
                else
                {
                    _properties.Remove(key);
                }
            }
        }

        /// <summary>
        /// Authenticate session
        /// </summary>
        public async Task<bool> AuthenticateAsync(int accountId, string characterName, object? playerData = null)
        {
            try
            {
                if (IsAuthenticated)
                {
                    Logger.Instance.Warning($"Session {SessionId} already authenticated");
                    return false;
                }

                AccountId = accountId;
                CharacterName = characterName;
                Player = playerData;
                IsAuthenticated = true;
                LoginTime = DateTime.UtcNow;

                SetProperty("AccountId", accountId);
                SetProperty("CharacterName", characterName);
                if (playerData != null)
                {
                    SetProperty("Player", playerData);
                }

                Logger.Instance.Info($"Session {SessionId} authenticated for account {accountId}, character {characterName}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error authenticating session {SessionId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Send data to client
        /// </summary>
        public async Task SendAsync(byte[] data)
        {
            if (_isDisposed || !IsConnected)
            {
                throw new InvalidOperationException($"Session {SessionId} is not connected");
            }

            try
            {
                await _packetContext.SendAsync(data);
                UpdateActivity();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error sending data to session {SessionId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Send encrypted data to client
        /// </summary>
        public async Task SendEncryptedAsync(byte[] data)
        {
            if (_isDisposed || !IsConnected)
            {
                throw new InvalidOperationException($"Session {SessionId} is not connected");
            }

            try
            {
                if (_packetContext is PacketContext context)
                {
                    await context.SendEncryptedAsync(data);
                }
                else
                {
                    await _packetContext.SendAsync(data);
                }
                UpdateActivity();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error sending encrypted data to session {SessionId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Close session
        /// </summary>
        public async Task CloseAsync(string reason = "Session closed")
        {
            if (_isDisposed)
                return;

            try
            {
                Logger.Instance.Info($"Closing session {SessionId}: {reason}");
                
                await _packetContext.CloseAsync();
                
                // Clear sensitive data
                Player = null;
                AccountId = null;
                CharacterName = null;
                IsAuthenticated = false;
                
                lock (_properties)
                {
                    _properties.Clear();
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error closing session {SessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Get session duration
        /// </summary>
        public TimeSpan GetDuration()
        {
            return DateTime.UtcNow - CreatedAt;
        }

        /// <summary>
        /// Get idle time
        /// </summary>
        public TimeSpan GetIdleTime()
        {
            return DateTime.UtcNow - LastActivity;
        }

        /// <summary>
        /// Check if session is idle
        /// </summary>
        public bool IsIdle(TimeSpan timeout)
        {
            return GetIdleTime() > timeout;
        }

        /// <summary>
        /// Get session info for debugging
        /// </summary>
        public SessionInfo GetSessionInfo()
        {
            return new SessionInfo
            {
                SessionId = SessionId,
                AccountId = AccountId,
                CharacterName = CharacterName,
                IsAuthenticated = IsAuthenticated,
                IsConnected = IsConnected,
                CreatedAt = CreatedAt,
                LastActivity = LastActivity,
                Duration = GetDuration(),
                IdleTime = GetIdleTime(),
                RemoteEndPoint = RemoteEndPoint,
                PropertyCount = _properties.Count
            };
        }

        public void Dispose()
        {
            if (_isDisposed)
                return;

            _isDisposed = true;

            try
            {
                // Don't await here to avoid blocking
                _ = Task.Run(async () => await CloseAsync("Session disposed"));
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing session {SessionId}: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Session info for debugging and monitoring
    /// </summary>
    public class SessionInfo
    {
        public int SessionId { get; set; }
        public int? AccountId { get; set; }
        public string? CharacterName { get; set; }
        public bool IsAuthenticated { get; set; }
        public bool IsConnected { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastActivity { get; set; }
        public TimeSpan Duration { get; set; }
        public TimeSpan IdleTime { get; set; }
        public string RemoteEndPoint { get; set; } = string.Empty;
        public int PropertyCount { get; set; }
    }

    /// <summary>
    /// Player session factory implementation
    /// </summary>
    public class PlayerSessionFactory : IPlayerSessionFactory
    {
        /// <summary>
        /// Create player session
        /// </summary>
        public IPlayerSession CreatePlayerSession(IClientSession networkSession, IPacketContext packetContext)
        {
            return new PlayerSession(networkSession, packetContext);
        }
    }
}
