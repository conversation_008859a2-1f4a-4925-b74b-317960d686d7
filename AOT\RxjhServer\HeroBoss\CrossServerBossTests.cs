using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using YulgangServer;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer.HeroBoss
{
    /// <summary>
    /// Test suite cho Cross Server Boss System
    /// </summary>
    public class CrossServerBossTests
    {
        private static CrossServerBossTests _instance;
        private static readonly object _lock = new();

        public static CrossServerBossTests Instance
        {
            get
            {
                if (_instance is null)
                {
                    lock (_lock)
                    {
                        _instance ??= new CrossServerBossTests();
                    }
                }
                return _instance;
            }
        }

        private CrossServerBossTests()
        {
            LogHelper.WriteLine(LogLevel.Info, "CrossServerBossTests initialized");
        }

        /// <summary>
        /// Chạy tất cả tests
        /// </summary>
        public async Task RunAllTests()
        {
            LogHelper.WriteLine(LogLevel.Info, "=== Starting Cross Server Boss Tests ===");

            var testResults = new List<TestResult>();

            // Test 1: Boss Registration
            testResults.Add(await TestBossRegistration());

            // Test 2: Contribution Tracking
            testResults.Add(await TestContributionTracking());

            // Test 3: State Synchronization
            testResults.Add(await TestStateSynchronization());

            // Test 4: Reward Calculation
            testResults.Add(await TestRewardCalculation());

            // Test 5: Protocol Messages
            testResults.Add(await TestProtocolMessages());

            // Test 6: Cleanup Process
            testResults.Add(await TestCleanupProcess());

            // Báo cáo kết quả
            ReportTestResults(testResults);
        }

        /// <summary>
        /// Test boss registration
        /// </summary>
        private async Task<TestResult> TestBossRegistration()
        {
            var testName = "Boss Registration Test";
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"Running {testName}...");

                var bossId = 99999; // Test boss ID
                var serverId = World.ServerID;
                var bossName = "Test Cross Server Boss";
                var mapId = 1;
                var x = 100f;
                var y = 100f;
                var duration = 30;

                // Register boss
                CrossServerBossManager.Instance.RegisterCrossServerBoss(
                    bossId, serverId, bossName, mapId, x, y, duration);

                // Verify registration
                await Task.Delay(1000); // Wait for async operations

                // Check if boss is registered (this would need access to private fields)
                // For now, we'll assume success if no exception is thrown

                LogHelper.WriteLine(LogLevel.Info, $"{testName} PASSED");
                return new TestResult { TestName = testName, Success = true, Message = "Boss registered successfully" };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"{testName} FAILED: {ex.Message}");
                return new TestResult { TestName = testName, Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// Test contribution tracking
        /// </summary>
        private async Task<TestResult> TestContributionTracking()
        {
            var testName = "Contribution Tracking Test";
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"Running {testName}...");

                var bossId = 99998;
                var serverId = World.ServerID;
                var sessionId = 12345;
                var playerName = "TestPlayer";
                var damage = 1000L;
                var attackCount = 5;

                // Update contribution
                CrossServerBossManager.Instance.UpdateContribution(
                    bossId, serverId, sessionId, playerName, damage, attackCount);

                // Verify contribution
                await Task.Delay(500);

                LogHelper.WriteLine(LogLevel.Info, $"{testName} PASSED");
                return new TestResult { TestName = testName, Success = true, Message = "Contribution tracked successfully" };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"{testName} FAILED: {ex.Message}");
                return new TestResult { TestName = testName, Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// Test state synchronization
        /// </summary>
        private async Task<TestResult> TestStateSynchronization()
        {
            var testName = "State Synchronization Test";
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"Running {testName}...");

                var bossId = 99997;
                var currentHP = 5000;
                var maxHP = 10000;
                var state = CrossServerBossState.Active;

                // Update boss state
                CrossServerBossManager.Instance.UpdateBossState(bossId, currentHP, maxHP, state);

                await Task.Delay(500);

                LogHelper.WriteLine(LogLevel.Info, $"{testName} PASSED");
                return new TestResult { TestName = testName, Success = true, Message = "State synchronized successfully" };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"{testName} FAILED: {ex.Message}");
                return new TestResult { TestName = testName, Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// Test reward calculation
        /// </summary>
        private async Task<TestResult> TestRewardCalculation()
        {
            var testName = "Reward Calculation Test";
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"Running {testName}...");

                var bossId = 99996;
                var contributions = new List<CrossServerDamageContribute>
                {
                    new CrossServerDamageContribute(1, 1001, "Player1", 5000, 10),
                    new CrossServerDamageContribute(1, 1002, "Player2", 3000, 8),
                    new CrossServerDamageContribute(2, 2001, "Player3", 2000, 6),
                    new CrossServerDamageContribute(2, 2002, "Player4", 1000, 4)
                };

                // Test reward distribution
                await CrossServerRewardManager.Instance.DistributeRewards(bossId, contributions);

                LogHelper.WriteLine(LogLevel.Info, $"{testName} PASSED");
                return new TestResult { TestName = testName, Success = true, Message = "Rewards calculated successfully" };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"{testName} FAILED: {ex.Message}");
                return new TestResult { TestName = testName, Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// Test protocol messages
        /// </summary>
        private async Task<TestResult> TestProtocolMessages()
        {
            var testName = "Protocol Messages Test";
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"Running {testName}...");

                // Test boss spawn message
                var spawnInfo = new CrossServerBossSpawnInfo
                {
                    BossId = 99995,
                    OriginServerId = 1,
                    BossName = "Test Boss",
                    MapId = 1,
                    X = 100f,
                    Y = 100f,
                    MaxHP = 10000,
                    DurationMinutes = 30,
                    BossType = BossType.WorldBoss
                };

                var spawnMessage = CrossServerBossProtocol.CreateBossSpawnMessage(spawnInfo);
                var parsedSpawnInfo = CrossServerBossProtocol.ParseBossSpawnMessage(spawnMessage.Split('|'));

                if (parsedSpawnInfo == null || parsedSpawnInfo.BossId != spawnInfo.BossId)
                {
                    throw new Exception("Boss spawn message parsing failed");
                }

                // Test HP update message
                var hpInfo = new BossHPUpdateInfo
                {
                    BossId = 99995,
                    CurrentHP = 5000,
                    MaxHP = 10000,
                    DamageDealt = 1000,
                    AttackerServerId = 1,
                    AttackerSessionId = 1001,
                    AttackerName = "TestPlayer"
                };

                var hpMessage = CrossServerBossProtocol.CreateBossHPUpdateMessage(hpInfo);
                var parsedHPInfo = CrossServerBossProtocol.ParseBossHPUpdateMessage(hpMessage.Split('|'));

                if (parsedHPInfo == null || parsedHPInfo.BossId != hpInfo.BossId)
                {
                    throw new Exception("HP update message parsing failed");
                }

                // Test damage contribute message
                var damageMessage = CrossServerBossProtocol.CreateDamageContributeMessage(99995, 1, 1001, "TestPlayer", 1000, 5);
                var (bossId, serverId, sessionId, playerName, damage, attackCount) = 
                    CrossServerBossProtocol.ParseDamageContributeMessage(damageMessage.Split('|'));

                if (bossId != 99995 || damage != 1000)
                {
                    throw new Exception("Damage contribute message parsing failed");
                }

                LogHelper.WriteLine(LogLevel.Info, $"{testName} PASSED");
                return new TestResult { TestName = testName, Success = true, Message = "Protocol messages working correctly" };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"{testName} FAILED: {ex.Message}");
                return new TestResult { TestName = testName, Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// Test cleanup process
        /// </summary>
        private async Task<TestResult> TestCleanupProcess()
        {
            var testName = "Cleanup Process Test";
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"Running {testName}...");

                var bossId = 99994;

                // Start cleanup process
                await CrossServerCleanupCoordinator.Instance.StartCleanupProcess(bossId, 1);

                // Wait for cleanup to complete
                await Task.Delay(3000);

                // Check cleanup state
                var cleanupState = CrossServerCleanupCoordinator.Instance.GetCleanupState(bossId);
                
                LogHelper.WriteLine(LogLevel.Info, $"{testName} PASSED");
                return new TestResult { TestName = testName, Success = true, Message = "Cleanup process working correctly" };
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"{testName} FAILED: {ex.Message}");
                return new TestResult { TestName = testName, Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// Báo cáo kết quả test
        /// </summary>
        private void ReportTestResults(List<TestResult> results)
        {
            LogHelper.WriteLine(LogLevel.Info, "=== Cross Server Boss Test Results ===");
            
            var passedTests = results.Count(r => r.Success);
            var totalTests = results.Count;

            foreach (var result in results)
            {
                var status = result.Success ? "PASSED" : "FAILED";
                LogHelper.WriteLine(LogLevel.Info, $"[{status}] {result.TestName}: {result.Message}");
            }

            LogHelper.WriteLine(LogLevel.Info, $"=== Test Summary: {passedTests}/{totalTests} tests passed ===");

            if (passedTests == totalTests)
            {
                LogHelper.WriteLine(LogLevel.Info, "🎉 All Cross Server Boss tests PASSED! System is ready for production.");
            }
            else
            {
                LogHelper.WriteLine(LogLevel.Warning, $"⚠️ {totalTests - passedTests} tests FAILED. Please review and fix issues before production.");
            }
        }

        /// <summary>
        /// Test individual component
        /// </summary>
        public async Task<bool> TestComponent(string componentName)
        {
            switch (componentName.ToLower())
            {
                case "registration":
                    var result1 = await TestBossRegistration();
                    return result1.Success;

                case "contribution":
                    var result2 = await TestContributionTracking();
                    return result2.Success;

                case "synchronization":
                    var result3 = await TestStateSynchronization();
                    return result3.Success;

                case "rewards":
                    var result4 = await TestRewardCalculation();
                    return result4.Success;

                case "protocol":
                    var result5 = await TestProtocolMessages();
                    return result5.Success;

                case "cleanup":
                    var result6 = await TestCleanupProcess();
                    return result6.Success;

                default:
                    LogHelper.WriteLine(LogLevel.Error, $"Unknown component: {componentName}");
                    return false;
            }
        }
    }

    /// <summary>
    /// Kết quả test
    /// </summary>
    public class TestResult
    {
        public string TestName { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; }
    }
}
