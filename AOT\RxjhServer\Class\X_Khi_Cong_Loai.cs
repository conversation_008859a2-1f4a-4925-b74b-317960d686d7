using System;

namespace RxjhServer;

public class X_<PERSON><PERSON>_Cong_Loai
{
	private byte[] _KhiCong_byte;

	public int KhiCongID;

	public byte[] KhiCong_byte
	{
		get
		{
			return _KhiCong_byte;
		}
		set
		{
			_KhiCong_byte = value;
		}
	}

	public int KhiCong_SoLuong
	{
		get
		{
			return BitConverter.ToInt16(KhiCong_byte, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, KhiCong_byte, 0, 2);
		}
	}

	public X_Khi_Cong_Loai()
	{
	}

	public X_Khi_Cong_Loai(byte[] byte_0)
	{
		KhiCong_byte = byte_0;
	}
}
