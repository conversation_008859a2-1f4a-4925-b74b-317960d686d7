using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer.AOI
{
    /// <summary>
    /// Manages Area of Interest (AOI) system for efficient entity visibility and updates
    /// Handles spatial partitioning using a grid-based approach
    /// </summary>
    public class AOIManager
    {
        #region Singleton Pattern
        
        private static AOIManager _instance;
        private static readonly object _lock = new();
        
        /// <summary>
        /// Get the singleton instance of AOIManager
        /// </summary>
        public static AOIManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new AOIManager();
                    }
                }
                return _instance;
            }
        }
        
        #endregion
        
        #region Configuration Constants
        
        /// <summary>
        /// Size of each grid in world units (1024x1024)
        /// </summary>
        public const int GRID_SIZE = 1024;
        
        /// <summary>
        /// Overlap size for grid boundaries to prevent edge cases
        /// </summary>
        public const int OVERLAP_SIZE = 256;
        
        /// <summary>
        /// Area of Interest radius for player visibility
        /// </summary>
        public const int AOI_RADIUS = 512;
        
        /// <summary>
        /// Number of grids per map dimension (5x5 = 25 grids per map)
        /// </summary>
        public const int GRIDS_PER_DIMENSION = 5;
        
        /// <summary>
        /// Map size in world units
        /// </summary>
        public const int MAP_SIZE = 5120;
        
        /// <summary>
        /// Half map size for coordinate calculations
        /// </summary>
        public const int HALF_MAP_SIZE = MAP_SIZE / 2;
        
        #endregion
        
        #region Map-Specific Configuration
        
        /// <summary>
        /// Special map centers for maps with non-standard center points
        /// </summary>
        private static readonly Dictionary<int, (float centerX, float centerY)> MapCenters = new()
        {
            { 201, (2560f, 0f) },   // Map 201 center at (2560, 0)
            { 301, (-2560f, 0f) }   // Map 301 center at (-2560, 0)
            // Default: (0f, 0f) for other maps
        };
        
        #endregion
        
        #region Data Structures
        
        /// <summary>
        /// Grid storage: MapID -> (GridX, GridY) -> AOIGrid
        /// </summary>
        private readonly ConcurrentDictionary<int, ConcurrentDictionary<(int, int), AOIGrid>> _mapGrids;
        
        /// <summary>
        /// Entity tracking: Player SessionID -> Current Grid
        /// </summary>
        private readonly ConcurrentDictionary<int, AOIGrid> _playerGrids;
        
        /// <summary>
        /// Entity tracking: NPC SessionID -> Current Grid
        /// </summary>
        private readonly ConcurrentDictionary<int, AOIGrid> _npcGrids;
        
        /// <summary>
        /// Entity tracking: Ground Item ID -> Current Grid
        /// </summary>
        private readonly ConcurrentDictionary<long, AOIGrid> _itemGrids;
        
        /// <summary>
        /// Initialized maps to avoid repeated initialization
        /// </summary>
        private readonly ConcurrentDictionary<int, bool> _initializedMaps;
        
        #endregion
        
        #region Constructor
        
        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private AOIManager()
        {
            _mapGrids = new ConcurrentDictionary<int, ConcurrentDictionary<(int, int), AOIGrid>>();
            _playerGrids = new ConcurrentDictionary<int, AOIGrid>();
            _npcGrids = new ConcurrentDictionary<int, AOIGrid>();
            _itemGrids = new ConcurrentDictionary<long, AOIGrid>();
            _initializedMaps = new ConcurrentDictionary<int, bool>();
            
            LogHelper.WriteLine(LogLevel.Info, "AOIManager initialized successfully");
        }
        
        #endregion
        
        #region Grid Coordinate Calculations
        
        /// <summary>
        /// Convert world coordinates to grid coordinates
        /// </summary>
        /// <param name="posX">World X coordinate</param>
        /// <param name="posY">World Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <returns>Grid coordinates (gridX, gridY)</returns>
        public (int gridX, int gridY) GetGridCoordinates(float posX, float posY, int mapID)
        {
            try
            {
                // Get map center offset
                var (centerX, centerY) = MapCenters.GetValueOrDefault(mapID, (0f, 0f));
                
                // Adjust coordinates relative to map center
                float adjustedX = posX - centerX;
                float adjustedY = posY - centerY;
                
                // Calculate grid coordinates
                // Map range: -2560 to +2560 (5120 total)
                // Grid range: 0 to 4 (5x5 grids)
                int gridX = (int)Math.Floor((adjustedX + HALF_MAP_SIZE) / GRID_SIZE);
                int gridY = (int)Math.Floor((adjustedY + HALF_MAP_SIZE) / GRID_SIZE);
                
                // Clamp to valid grid range [0, 4]
                gridX = Math.Clamp(gridX, 0, GRIDS_PER_DIMENSION - 1);
                gridY = Math.Clamp(gridY, 0, GRIDS_PER_DIMENSION - 1);
                
                return (gridX, gridY);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error calculating grid coordinates: {ex.Message}");
                return (2, 2); // Return center grid as fallback
            }
        }
        
        /// <summary>
        /// Convert grid coordinates back to world coordinates (grid center)
        /// </summary>
        /// <param name="gridX">Grid X coordinate</param>
        /// <param name="gridY">Grid Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <returns>World coordinates (worldX, worldY)</returns>
        public (float worldX, float worldY) GetWorldCoordinates(int gridX, int gridY, int mapID)
        {
            try
            {
                // Get map center offset
                var (centerX, centerY) = MapCenters.GetValueOrDefault(mapID, (0f, 0f));
                
                // Convert grid coordinates back to world coordinates (center of grid)
                float worldX = (gridX * GRID_SIZE) - HALF_MAP_SIZE + (GRID_SIZE / 2f) + centerX;
                float worldY = (gridY * GRID_SIZE) - HALF_MAP_SIZE + (GRID_SIZE / 2f) + centerY;
                
                return (worldX, worldY);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error calculating world coordinates: {ex.Message}");
                return (0f, 0f); // Return origin as fallback
            }
        }
        
        #endregion
        
        #region Map Initialization
        
        /// <summary>
        /// Initialize grid system for a specific map
        /// </summary>
        /// <param name="mapID">Map ID to initialize</param>
        public void InitializeMapGrids(int mapID)
        {
            try
            {
                // Check if already initialized
                if (_initializedMaps.ContainsKey(mapID))
                {
                    return;
                }
                
                var mapGrids = new ConcurrentDictionary<(int, int), AOIGrid>();
                var (centerX, centerY) = MapCenters.GetValueOrDefault(mapID, (0f, 0f));
                
                LogHelper.WriteLine(LogLevel.Info, $"Initializing AOI grids for map {mapID} with center ({centerX}, {centerY})");
                
                // Create 5x5 grid for the map
                for (int x = 0; x < GRIDS_PER_DIMENSION; x++)
                {
                    for (int y = 0; y < GRIDS_PER_DIMENSION; y++)
                    {
                        var grid = new AOIGrid(x, y, mapID)
                        {
                            // Calculate world boundaries
                            MinX = (x * GRID_SIZE) - HALF_MAP_SIZE + centerX,
                            MaxX = ((x + 1) * GRID_SIZE) - HALF_MAP_SIZE + centerX,
                            MinY = (y * GRID_SIZE) - HALF_MAP_SIZE + centerY,
                            MaxY = ((y + 1) * GRID_SIZE) - HALF_MAP_SIZE + centerY,
                            
                            // Calculate overlap boundaries
                            OverlapMinX = (x * GRID_SIZE) - HALF_MAP_SIZE + centerX - OVERLAP_SIZE,
                            OverlapMaxX = ((x + 1) * GRID_SIZE) - HALF_MAP_SIZE + centerX + OVERLAP_SIZE,
                            OverlapMinY = (y * GRID_SIZE) - HALF_MAP_SIZE + centerY - OVERLAP_SIZE,
                            OverlapMaxY = ((y + 1) * GRID_SIZE) - HALF_MAP_SIZE + centerY + OVERLAP_SIZE
                        };
                        
                        mapGrids[(x, y)] = grid;
                    }
                }
                
                // Setup adjacent grid relationships
                SetupAdjacentGrids(mapGrids);
                
                // Store the map grids
                _mapGrids[mapID] = mapGrids;
                _initializedMaps[mapID] = true;
                
                LogHelper.WriteLine(LogLevel.Info, $"Successfully initialized {GRIDS_PER_DIMENSION}x{GRIDS_PER_DIMENSION} grids for map {mapID}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error initializing map grids for map {mapID}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Setup adjacent grid relationships for efficient AOI calculations
        /// </summary>
        /// <param name="mapGrids">Map grids to setup relationships for</param>
        private void SetupAdjacentGrids(ConcurrentDictionary<(int, int), AOIGrid> mapGrids)
        {
            try
            {
                foreach (var kvp in mapGrids)
                {
                    var (x, y) = kvp.Key;
                    var grid = kvp.Value;
                    
                    // Add all adjacent grids (including diagonals)
                    for (int dx = -1; dx <= 1; dx++)
                    {
                        for (int dy = -1; dy <= 1; dy++)
                        {
                            if (dx == 0 && dy == 0) continue; // Skip self
                            
                            int adjX = x + dx;
                            int adjY = y + dy;
                            
                            // Check if adjacent grid coordinates are valid
                            if (adjX >= 0 && adjX < GRIDS_PER_DIMENSION && 
                                adjY >= 0 && adjY < GRIDS_PER_DIMENSION)
                            {
                                if (mapGrids.TryGetValue((adjX, adjY), out var adjacentGrid))
                                {
                                    grid.AdjacentGrids.Add(adjacentGrid);
                                }
                            }
                        }
                    }
                }
                
                LogHelper.WriteLine(LogLevel.Debug, "Adjacent grid relationships setup completed");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error setting up adjacent grids: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Grid Access Methods
        
        /// <summary>
        /// Get a specific grid by coordinates
        /// </summary>
        /// <param name="mapID">Map ID</param>
        /// <param name="gridX">Grid X coordinate</param>
        /// <param name="gridY">Grid Y coordinate</param>
        /// <returns>AOIGrid if found, null otherwise</returns>
        public AOIGrid GetGrid(int mapID, int gridX, int gridY)
        {
            try
            {
                if (_mapGrids.TryGetValue(mapID, out var mapGrids))
                {
                    if (mapGrids.TryGetValue((gridX, gridY), out var grid))
                    {
                        return grid;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting grid ({gridX}, {gridY}) for map {mapID}: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// Try to get a specific grid by coordinates
        /// </summary>
        /// <param name="mapID">Map ID</param>
        /// <param name="gridX">Grid X coordinate</param>
        /// <param name="gridY">Grid Y coordinate</param>
        /// <param name="grid">Output grid if found</param>
        /// <returns>True if grid was found</returns>
        public bool TryGetGrid(int mapID, int gridX, int gridY, out AOIGrid grid)
        {
            grid = GetGrid(mapID, gridX, gridY);
            return grid != null;
        }
        
        /// <summary>
        /// Get grid by world position
        /// </summary>
        /// <param name="posX">World X coordinate</param>
        /// <param name="posY">World Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <returns>AOIGrid if found, null otherwise</returns>
        public AOIGrid GetGridByPosition(float posX, float posY, int mapID)
        {
            var (gridX, gridY) = GetGridCoordinates(posX, posY, mapID);
            return GetGrid(mapID, gridX, gridY);
        }
        
        #endregion

        #region AOI Grid Collection Methods

        /// <summary>
        /// Get all grids within AOI range of a position
        /// </summary>
        /// <param name="posX">World X coordinate</param>
        /// <param name="posY">World Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <returns>List of grids within AOI range</returns>
        public List<AOIGrid> GetAOIGrids(float posX, float posY, int mapID)
        {
            try
            {
                // Ensure map is initialized
                if (!_initializedMaps.ContainsKey(mapID))
                {
                    InitializeMapGrids(mapID);
                }

                var grids = new List<AOIGrid>();
                var centerGrid = GetGridByPosition(posX, posY, mapID);

                if (centerGrid != null)
                {
                    // Add center grid
                    grids.Add(centerGrid);

                    // Add adjacent grids
                    grids.AddRange(centerGrid.AdjacentGrids);
                }

                return grids;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting AOI grids for position ({posX}, {posY}) on map {mapID}: {ex.Message}");
                return new List<AOIGrid>();
            }
        }

        /// <summary>
        /// Get all grids for a specific map
        /// </summary>
        /// <param name="mapID">Map ID</param>
        /// <returns>List of all grids on the map</returns>
        public List<AOIGrid> GetAllMapGrids(int mapID)
        {
            try
            {
                if (_mapGrids.TryGetValue(mapID, out var mapGrids))
                {
                    return mapGrids.Values.ToList();
                }
                return new List<AOIGrid>();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting all grids for map {mapID}: {ex.Message}");
                return new List<AOIGrid>();
            }
        }

        #endregion

        #region Entity Management - Players

        /// <summary>
        /// Add a player to the AOI system
        /// </summary>
        /// <param name="player">Player to add</param>
        /// <returns>True if added successfully</returns>
        public bool AddPlayer(Players player)
        {
            try
            {
                if (player == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, "Attempted to add null player to AOI system");
                    return false;
                }

                // Ensure map is initialized
                if (!_initializedMaps.ContainsKey(player.MapID))
                {
                    InitializeMapGrids(player.MapID);
                }

                var grid = GetGridByPosition(player.PosX, player.PosY, player.MapID);
                if (grid != null)
                {
                    if (grid.AddPlayer(player))
                    {
                        _playerGrids[player.SessionID] = grid;
                       // LogHelper.WriteLine(LogLevel.Debug, $"Player {player.CharacterName} added to AOI system at grid ({grid.GridX}, {grid.GridY})");
                        return true;
                    }
                }

                LogHelper.WriteLine(LogLevel.Warning, $"Failed to add player {player.CharacterName} to AOI system");
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding player to AOI system: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove a player from the AOI system
        /// </summary>
        /// <param name="sessionID">Player session ID</param>
        /// <returns>True if removed successfully</returns>
        public bool RemovePlayer(int sessionID)
        {
            try
            {
                if (_playerGrids.TryRemove(sessionID, out var grid))
                {
                    grid.RemovePlayer(sessionID);
                    LogHelper.WriteLine(LogLevel.Debug, $"Player with session ID {sessionID} removed from AOI system");
                    return true;
                }

                LogHelper.WriteLine(LogLevel.Warning, $"Player with session ID {sessionID} not found in AOI system");
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing player from AOI system: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Update player position in the AOI system
        /// </summary>
        /// <param name="player">Player to update</param>
        /// <param name="newX">New X coordinate</param>
        /// <param name="newY">New Y coordinate</param>
        /// <returns>True if position was updated successfully</returns>
        public bool UpdatePlayerPosition(Players player, float newX, float newY)
        {
            try
            {
                if (player == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, "Attempted to update position for null player");
                    return false;
                }

                var newGridCoords = GetGridCoordinates(newX, newY, player.MapID);
                var currentGrid = _playerGrids.GetValueOrDefault(player.SessionID);

                if (currentGrid == null)
                {
                    // Player not in AOI system, add them
                    player.PosX = newX;
                    player.PosY = newY;
                    return AddPlayer(player);
                }

                // Check if player changed grid
                if (currentGrid.GridX != newGridCoords.gridX || currentGrid.GridY != newGridCoords.gridY)
                {
                    // Player moved to a different grid
                    return MovePlayerToGrid(player, newGridCoords.gridX, newGridCoords.gridY);
                }
                else
                {
                    // Same grid, just update position
                    player.PosX = newX;
                    player.PosY = newY;

                    // Mark grid as dirty for next update cycle
                    currentGrid.IsDirty = true;

                    return true;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating player position: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Move a player to a specific grid
        /// </summary>
        /// <param name="player">Player to move</param>
        /// <param name="newGridX">New grid X coordinate</param>
        /// <param name="newGridY">New grid Y coordinate</param>
        /// <returns>True if moved successfully</returns>
        private bool MovePlayerToGrid(Players player, int newGridX, int newGridY)
        {
            try
            {
                // Remove from current grid
                if (_playerGrids.TryGetValue(player.SessionID, out var currentGrid))
                {
                    currentGrid.RemovePlayer(player.SessionID);
                }

                // Add to new grid
                var newGrid = GetGrid(player.MapID, newGridX, newGridY);
                if (newGrid != null)
                {
                    if (newGrid.AddPlayer(player))
                    {
                        _playerGrids[player.SessionID] = newGrid;
                        LogHelper.WriteLine(LogLevel.Debug, $"Player {player.CharacterName} moved from grid ({currentGrid?.GridX}, {currentGrid?.GridY}) to ({newGridX}, {newGridY})");
                        return true;
                    }
                }

                LogHelper.WriteLine(LogLevel.Warning, $"Failed to move player {player.CharacterName} to grid ({newGridX}, {newGridY})");
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error moving player to grid: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Entity Management - NPCs

        /// <summary>
        /// Add an NPC to the AOI system
        /// </summary>
        /// <param name="npc">NPC to add</param>
        /// <returns>True if added successfully</returns>
        public bool AddNPC(NpcClass npc)
        {
            try
            {
                if (npc == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, "Attempted to add null NPC to AOI system");
                    return false;
                }

                // Ensure map is initialized
                if (!_initializedMaps.ContainsKey(npc.Rxjh_Map))
                {
                    InitializeMapGrids(npc.Rxjh_Map);
                }

                var grid = GetGridByPosition(npc.Rxjh_X, npc.Rxjh_Y, npc.Rxjh_Map);
                if (grid != null)
                {
                    if (grid.AddNPC(npc))
                    {
                        _npcGrids[npc.NPC_SessionID] = grid;
                        //LogHelper.WriteLine(LogLevel.Debug, $"NPC {npc.Name} added to AOI system at grid ({grid.GridX}, {grid.GridY})");
                        return true;
                    }
                }

                LogHelper.WriteLine(LogLevel.Warning, $"Failed to add NPC {npc.Name} to AOI system");
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding NPC to AOI system: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove an NPC from the AOI system
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if removed successfully</returns>
        public bool RemoveNPC(int npcSessionID)
        {
            try
            {
                if (_npcGrids.TryRemove(npcSessionID, out var grid))
                {
                    grid.RemoveNPC(npcSessionID);
                    LogHelper.WriteLine(LogLevel.Debug, $"NPC with session ID {npcSessionID} removed from AOI system");
                    return true;
                }

                LogHelper.WriteLine(LogLevel.Warning, $"NPC with session ID {npcSessionID} not found in AOI system");
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing NPC from AOI system: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Update NPC position in the AOI system
        /// </summary>
        /// <param name="npc">NPC to update</param>
        /// <param name="newX">New X coordinate</param>
        /// <param name="newY">New Y coordinate</param>
        /// <returns>True if position was updated successfully</returns>
        public bool UpdateNPCPosition(NpcClass npc, float newX, float newY)
        {
            try
            {
                if (npc == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, "Attempted to update position for null NPC");
                    return false;
                }

                var newGridCoords = GetGridCoordinates(newX, newY, npc.Rxjh_Map);
                var currentGrid = _npcGrids.GetValueOrDefault(npc.NPC_SessionID);

                if (currentGrid == null)
                {
                    // NPC not in AOI system, add them
                    npc.Rxjh_X = newX;
                    npc.Rxjh_Y = newY;
                    return AddNPC(npc);
                }

                // Check if NPC changed grid
                if (currentGrid.GridX != newGridCoords.gridX || currentGrid.GridY != newGridCoords.gridY)
                {
                    // NPC moved to a different grid
                    return MoveNPCToGrid(npc, newGridCoords.gridX, newGridCoords.gridY);
                }
                else
                {
                    // Same grid, just update position
                    npc.Rxjh_X = newX;
                    npc.Rxjh_Y = newY;

                    // Mark grid as dirty for next update cycle
                    currentGrid.IsDirty = true;

                    return true;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating NPC position: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Move an NPC to a specific grid
        /// </summary>
        /// <param name="npc">NPC to move</param>
        /// <param name="newGridX">New grid X coordinate</param>
        /// <param name="newGridY">New grid Y coordinate</param>
        /// <returns>True if moved successfully</returns>
        private bool MoveNPCToGrid(NpcClass npc, int newGridX, int newGridY)
        {
            try
            {
                // Remove from current grid
                if (_npcGrids.TryGetValue(npc.NPC_SessionID, out var currentGrid))
                {
                    currentGrid.RemoveNPC(npc.NPC_SessionID);
                }

                // Add to new grid
                var newGrid = GetGrid(npc.Rxjh_Map, newGridX, newGridY);
                if (newGrid != null)
                {
                    if (newGrid.AddNPC(npc))
                    {
                        _npcGrids[npc.NPC_SessionID] = newGrid;
                        LogHelper.WriteLine(LogLevel.Debug, $"NPC {npc.Name} moved from grid ({currentGrid?.GridX}, {currentGrid?.GridY}) to ({newGridX}, {newGridY})");
                        return true;
                    }
                }

                LogHelper.WriteLine(LogLevel.Warning, $"Failed to move NPC {npc.Name} to grid ({newGridX}, {newGridY})");
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error moving NPC to grid: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Entity Management - Ground Items

        /// <summary>
        /// Add a ground item to the AOI system
        /// </summary>
        /// <param name="item">Ground item to add</param>
        /// <returns>True if added successfully</returns>
        public bool AddGroundItem(GroundItem item)
        {
            try
            {
                if (item == null)
                {
                    LogHelper.WriteLine(LogLevel.Warning, "Attempted to add null ground item to AOI system");
                    return false;
                }

                // Ensure map is initialized
                if (!_initializedMaps.ContainsKey(item.MapID))
                {
                    InitializeMapGrids(item.MapID);
                }

                var grid = GetGridByPosition(item.PosX, item.PosY, item.MapID);
                if (grid != null)
                {
                    if (grid.AddGroundItem(item))
                    {
                        _itemGrids[item.id] = grid;
                       // LogHelper.WriteLine(LogLevel.Debug, $"Ground item {item.id} added to AOI system at grid ({grid.GridX}, {grid.GridY})");
                        return true;
                    }
                }

                LogHelper.WriteLine(LogLevel.Warning, $"Failed to add ground item {item.id} to AOI system");
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding ground item to AOI system: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove a ground item from the AOI system
        /// </summary>
        /// <param name="itemID">Ground item ID</param>
        /// <returns>True if removed successfully</returns>
        public bool RemoveGroundItem(long itemID)
        {
            try
            {
                if (_itemGrids.TryRemove(itemID, out var grid))
                {
                    grid.RemoveGroundItem(itemID);
                    LogHelper.WriteLine(LogLevel.Debug, $"Ground item {itemID} removed from AOI system");
                    return true;
                }

                LogHelper.WriteLine(LogLevel.Warning, $"Ground item {itemID} not found in AOI system");
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing ground item from AOI system: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
