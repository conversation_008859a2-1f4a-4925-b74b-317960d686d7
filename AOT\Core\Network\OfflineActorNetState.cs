using System;
using System.Net;
using HeroYulgang.Helpers;
using HeroYulgang.Core.Managers;
using HeroYulgang.Services;
using RxjhServer;
using HeroYulgang.Utils;

namespace HeroYulgang.Core.Network
{
    /// <summary>
    /// Lớp OfflineActorNetState đặc biệt cho offline player
    /// <PERSON><PERSON> thừa từ ActorNetState nhưng không cần connection thực sự
    /// </summary>
    public class OfflineActorNetState : ActorNetState
    {
        private readonly int _sessionId;
        private readonly IPEndPoint _remoteEndPoint;
        private bool _running = true;
        private bool _treoMay = false;
        private bool _online = false;
        private bool _login = false;
        private bool _thoatGame = false;
        private Players _player;

        /// <summary>
        /// Constructor cho OfflineActorNetState
        /// </summary>
        /// <param name="sessionId">Session ID duy nhất</param>
        /// <param name="remoteEndPoint">Fake endpoint</param>
        
        // TODO base akka offlineActor
        public OfflineActorNetState(int sessionId, IPEndPoint remoteEndPoint)
            : base(Akka.Actor.ActorRefs.NoSender, sessionId, remoteEndPoint)
        {
            _sessionId = sessionId;
            _remoteEndPoint = remoteEndPoint;
            _online = false; // Offline player
            _login = true;   // Đã login
            _running = true;

            LogHelper.WriteLine(LogLevel.Debug, $"Đã tạo OfflineActorNetState cho SessionID: {sessionId}");
        }

        /// <summary>
        /// Lấy hoặc đặt ID phiên
        /// </summary>
        public new int SessionID => _sessionId;

        /// <summary>
        /// Lấy hoặc đặt tham chiếu đến đối tượng Player
        /// </summary>
        public new Players Player
        {
            get => _player;
            set => _player = value;
        }

        /// <summary>
        /// Kiểm tra xem kết nối có đang chạy không
        /// </summary>
        public new bool Running => _running;

        /// <summary>
        /// Trạng thái treo máy
        /// </summary>
        public new bool TreoMay
        {
            get => _treoMay;
            set => _treoMay = value;
        }

        /// <summary>
        /// Trạng thái online
        /// </summary>
        public new bool Online
        {
            get => _online;
            set => _online = value;
        }

        /// <summary>
        /// Trạng thái login
        /// </summary>
        public new bool Login
        {
            get => _login;
            set => _login = value;
        }

        /// <summary>
        /// Trạng thái thoát game
        /// </summary>
        public new bool ThoatGame
        {
            get => _thoatGame;
            set => _thoatGame = value;
        }

        /// <summary>
        /// Gửi dữ liệu - cho offline player thì không làm gì
        /// Override để ngăn chặn việc gửi packet thông qua base class
        /// </summary>
        public new void Send(byte[] data, int length)
        {
            // Offline player không cần gửi dữ liệu thực sự
            // Không gọi base.Send() để tránh gửi packet qua TcpManagerActor

            // Chỉ log nếu cần debug
            if (World.Debug > 1)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"OfflineActorNetState.Send() - SessionID: {_sessionId}, Length: {length} - Bỏ qua gửi packet cho offline player");
            }
        }

        /// <summary>
        /// Gửi single package - cho offline player thì không làm gì
        /// </summary>
        public new void SendSinglePackage(byte[] toSendBuff, int len)
        {
            // Offline player không cần gửi dữ liệu thực sự
            if (World.Debug > 1)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"OfflineActorNetState.SendSinglePackage() - SessionID: {_sessionId}");
            }
        }

        /// <summary>
        /// Gửi map data - cho offline player thì không làm gì
        /// </summary>
        public new void Send_Map_Data(byte[] byte_0, int int_1)
        {
            // Offline player không cần gửi dữ liệu thực sự
            if (World.Debug > 1)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"OfflineActorNetState.Send_Map_Data() - SessionID: {_sessionId}");
            }
        }

        /// <summary>
        /// Gửi packet - cho offline player thì không làm gì
        /// </summary>
        public new void SendPak(SendingClass pak, int id, int wordid, bool bypass = false)
        {
            // Offline player không cần gửi dữ liệu thực sự
            if (World.Debug > 1)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"OfflineActorNetState.SendPak() - SessionID: {_sessionId}");
            }
        }

        /// <summary>
        /// Gửi multiple package - cho offline player thì không làm gì
        /// </summary>
        public new void SendMultiplePackage(byte[] toSendBuff, int len)
        {
            // Offline player không cần gửi dữ liệu thực sự
            if (World.Debug > 1)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"OfflineActorNetState.SendMultiplePackage() - SessionID: {_sessionId}");
            }
        }

        /// <summary>
        /// Thiết lập trạng thái offline
        /// </summary>
        public new void Offline()
        {
            var players = World.FindPlayerBySession(SessionID);
            ThoatGame = true;
            TreoMay = true;

            if (players != null)
            {
                if (TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 1)
                {
                    World.TreoMay_Offline++;
                }
                else if (TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 0)
                {
                    World.OffLine_SoLuong++;
                }
            }

            LogHelper.WriteLine(LogLevel.Debug, $"OfflineActorNetState.Offline() - SessionID: {_sessionId}");
        }

        /// <summary>
        /// Dispose offline connection
        /// </summary>
        public new void Dispose()
        {
            _running = false;

            // Giải phóng SessionID khi dispose
            SessionIdManager.Instance.ReleaseSessionId(_sessionId);

            LogHelper.WriteLine(LogLevel.Debug, $"OfflineActorNetState.Dispose() - SessionID: {_sessionId} - Đã giải phóng SessionID");
        }

        /// <summary>
        /// Disposed offline
        /// </summary>
        public new void DisposedOffline()
        {
            Dispose();
        }
    }
}
