using System;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Configuration;
using HeroYulgang.Services;

namespace HeroYulgang.Core
{
    [JsonSerializable(typeof(ServerConfig))]
    [JsonSerializable(typeof(DatabaseConfig))]
    [JsonSerializable(typeof(PogresConfig))]
    [JsonSerializable(typeof(LoginServerConfig))]
    [JsonSerializable(typeof(AppSettingsConfig))]
    [JsonSerializable(typeof(CustomConfig))]
    public partial class AppJsonContext : JsonSerializerContext
    {
    }

    public class ServerConfig
    {
        public string ServerName { get; set; } = "HeroGSPorted";
        public int ServerId { get; set; } = 1;
        public int GameServerPort { get; set; } = 13000;
        public int MaximumOnline { get; set; } = 1000;
        public int AutomaticConnectionTime { get; set; } = 60;
    }

    public class LoginServerConfig
    {
        public string LoginServerIP { get; set; } = "127.0.0.1";
        public int LoginServerGrpcPort { get; set; } = 6999;
        public int ClusterId { get; set; } = 1;
    }

    public class AppSettingsConfig
    {
        public bool ShowPacketData { get; set; } = true;
        public string Environment { get; set; } = "Development";
    }

    public class DatabaseConfig
    {
        public string AccountDb { get; set; } = string.Empty;
        public string GameDb { get; set; } = string.Empty;
        public string PublicDb { get; set; } = string.Empty;
        public string? BBGDb { get; set; } = null;
    }

    public class PogresConfig
    {
        public string AccountDb { get; set; } = string.Empty;
        public string GameDb { get; set; } = string.Empty;
        public string PublicDb { get; set; } = string.Empty;
        public string BBGDb { get; set; } = string.Empty;
    }

    public class CustomConfig
    {
        public GameServerConfig? GameServer { get; set; }
    }

    public class GameServerConfig
    {
        public int ServerId { get; set; }
        public int GameServerPort { get; set; }
        public string? ServerName { get; set; }
        public int MaximumOnline { get; set; }
        public string? AccountVerificationServerIp { get; set; }
        public int AccountVerificationServerPort { get; set; }
    }

    public class ConfigManager
    {
        private static ConfigManager? _instance;
        private IConfiguration _configuration = null!;

        public ServerConfig ServerSettings { get; private set; }
        public DatabaseConfig ConnectionStrings { get; private set; }
        public PogresConfig PogresSettings { get; private set; }
        public LoginServerConfig LoginServerSettings { get; private set; }
        public AppSettingsConfig AppSettings { get; private set; }

        public static ConfigManager Instance => _instance ??= new ConfigManager();

        private ConfigManager()
        {
            ServerSettings = new ServerConfig();
            ConnectionStrings = new DatabaseConfig();
            PogresSettings = new PogresConfig();
            LoginServerSettings = new LoginServerConfig();
            AppSettings = new AppSettingsConfig();

            try
            {
                _configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                LoadConfigurationFromAppSettings();
                Logger.Instance.Info("Đã tải cấu hình từ appsettings.json thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải cấu hình: {ex.Message}");
            }
        }

        public void SaveConfig()
        {
            try
            {
                var configData = new
                {
                    ConnectionStrings = ConnectionStrings,
                    ServerSettings = ServerSettings,
                    LoginServerSettings = LoginServerSettings,
                    AppSettings = AppSettings
                };

                string json = JsonSerializer.Serialize(configData, AppJsonContext.Default.Options);
                File.WriteAllText("appsettings.json", json);
                Logger.Instance.Info("Đã lưu cấu hình vào appsettings.json thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi lưu cấu hình: {ex.Message}");
            }
        }

        public void LoadCustomAppSettings(string appSettingsPath)
        {
            try
            {
                if (string.IsNullOrEmpty(appSettingsPath))
                {
                    Logger.Instance.Warning("Đường dẫn appsettings không được để trống.");
                    return;
                }

                // Chuyển đổi thành đường dẫn tuyệt đối
                string absolutePath = Path.GetFullPath(appSettingsPath);
                if (File.Exists(absolutePath))
                {
                    Logger.Instance.Info($"Loading custom appsettings from: {absolutePath}");

                    // Lấy thư mục chứa file appsettings
                    string appSettingsDirectory = Path.GetDirectoryName(absolutePath) ?? throw new ArgumentException("Không thể xác định thư mục của appsettings.", nameof(appSettingsPath));

                    _configuration = new ConfigurationBuilder()
                        .SetBasePath(appSettingsDirectory)
                        .AddJsonFile(Path.GetFileName(absolutePath), optional: false, reloadOnChange: true)
                        .Build();

                    LoadConfigurationFromAppSettings();
                    Logger.Instance.Info("Custom appsettings loaded successfully");
                }
                else
                {
                    Logger.Instance.Warning($"Custom appsettings file not found: {absolutePath}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error loading custom appsettings: {ex.Message}");
            }
        }

        private void LoadConfigurationFromAppSettings()
        {
            try
            {
                var connectionSection = _configuration.GetSection("ConnectionStrings");
                if (connectionSection.Exists())
                {
                    ConnectionStrings.AccountDb = connectionSection["AccountDb"] ?? ConnectionStrings.AccountDb;
                    ConnectionStrings.GameDb = connectionSection["GameDb"] ?? ConnectionStrings.GameDb;
                    ConnectionStrings.PublicDb = connectionSection["PublicDb"] ?? ConnectionStrings.PublicDb;
                    ConnectionStrings.BBGDb = connectionSection["BBGDb"];
                }

                var connectionPostgres = _configuration.GetSection("PostgresConnectionStrings");
                if (connectionPostgres.Exists())
                {
                    PogresSettings.AccountDb = connectionPostgres["AccountDb"] ?? PogresSettings.AccountDb;
                    PogresSettings.GameDb = connectionPostgres["GameDb"] ?? PogresSettings.GameDb;
                    PogresSettings.PublicDb = connectionPostgres["PublicDb"] ?? PogresSettings.PublicDb;
                    PogresSettings.BBGDb = connectionPostgres["BBGDb"] ?? PogresSettings.BBGDb;
                }

                var serverSection = _configuration.GetSection("ServerSettings");
                if (serverSection.Exists())
                {
                    ServerSettings.ServerName = serverSection["ServerName"] ?? ServerSettings.ServerName;
                    ServerSettings.ServerId = int.TryParse(serverSection["ServerID"], out int serverId) ? serverId : ServerSettings.ServerId;
                    ServerSettings.GameServerPort = int.TryParse(serverSection["GameServerPort"], out int port) ? port : ServerSettings.GameServerPort;
                    ServerSettings.MaximumOnline = int.TryParse(serverSection["MaximumOnline"], out int maxOnline) ? maxOnline : ServerSettings.MaximumOnline;
                    ServerSettings.AutomaticConnectionTime = int.TryParse(serverSection["AutomaticConnectionTime"], out int autoTime) ? autoTime : ServerSettings.AutomaticConnectionTime;
                }

                var loginServerSection = _configuration.GetSection("LoginServerSettings");
                if (loginServerSection.Exists())
                {
                    LoginServerSettings.LoginServerIP = loginServerSection["LoginServerIP"] ?? LoginServerSettings.LoginServerIP;
                    LoginServerSettings.LoginServerGrpcPort = int.TryParse(loginServerSection["LoginServerGrpcPort"], out int grpcPort) ? grpcPort : LoginServerSettings.LoginServerGrpcPort;
                    LoginServerSettings.ClusterId = int.TryParse(loginServerSection["ClusterId"], out int clusterId) ? clusterId : LoginServerSettings.ClusterId;
                }

                var appSettingsSection = _configuration.GetSection("AppSettings");
                if (appSettingsSection.Exists())
                {
                    AppSettings.ShowPacketData = bool.TryParse(appSettingsSection["ShowPacketData"], out bool showPacketData) ? showPacketData : AppSettings.ShowPacketData;
                    AppSettings.Environment = appSettingsSection["Environment"] ?? AppSettings.Environment;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error loading configuration from appsettings: {ex.Message}");
            }
        }

        public void LoadCustomConfig(string configPath)
        {
            try
            {
                if (File.Exists(configPath))
                {
                    Logger.Instance.Info($"Loading custom config from: {configPath}");

                    var jsonContent = File.ReadAllText(configPath);
                    var customConfig = JsonSerializer.Deserialize<CustomConfig>(jsonContent, AppJsonContext.Default.CustomConfig);

                    if (customConfig?.GameServer != null)
                    {
                        var gameServer = customConfig.GameServer;
                        ServerSettings.ServerId = gameServer.ServerId != 0 ? gameServer.ServerId : ServerSettings.ServerId;
                        ServerSettings.GameServerPort = gameServer.GameServerPort != 0 ? gameServer.GameServerPort : ServerSettings.GameServerPort;
                        ServerSettings.ServerName = gameServer.ServerName ?? ServerSettings.ServerName;
                        ServerSettings.MaximumOnline = gameServer.MaximumOnline != 0 ? gameServer.MaximumOnline : ServerSettings.MaximumOnline;
                        LoginServerSettings.LoginServerIP = gameServer.AccountVerificationServerIp ?? LoginServerSettings.LoginServerIP;
                        LoginServerSettings.LoginServerGrpcPort = gameServer.AccountVerificationServerPort != 0 ? gameServer.AccountVerificationServerPort : LoginServerSettings.LoginServerGrpcPort;

                        Logger.Instance.Info($"Updated ServerId to: {ServerSettings.ServerId}");
                        Logger.Instance.Info($"Updated GameServerPort to: {ServerSettings.GameServerPort}");
                        Logger.Instance.Info($"Updated ServerName to: {ServerSettings.ServerName}");
                        Logger.Instance.Info($"Updated MaximumOnline to: {ServerSettings.MaximumOnline}");
                        Logger.Instance.Info($"Updated LoginServerIP to: {LoginServerSettings.LoginServerIP}");
                        Logger.Instance.Info($"Updated LoginServerGrpcPort to: {LoginServerSettings.LoginServerGrpcPort}");
                    }

                    Logger.Instance.Info("Custom config loaded successfully");
                }
                else
                {
                    Logger.Instance.Warning($"Custom config file not found: {configPath}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error loading custom config: {ex.Message}");
            }
        }
    }
}