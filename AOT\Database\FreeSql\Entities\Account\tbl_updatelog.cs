﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Account {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_updatelog {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public DateTime? time { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_id { get; set; }

		[JsonProperty]
		public int? old_piont { get; set; }

		[JsonProperty]
		public int? nel_piont { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string hosname { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string dsc { get; set; }

		[JsonProperty]
		public DateTime? regtime { get; set; }

	}

}
