﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_vome {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public int? map { get; set; }

		[JsonProperty]
		public double? x { get; set; }

		[JsonProperty]
		public double? y { get; set; }

		[JsonProperty]
		public double? z { get; set; }

		[JsonProperty]
		public int? tomap { get; set; }

		[JsonProperty]
		public double? tox { get; set; }

		[JsonProperty]
		public double? toy { get; set; }

		[JsonProperty]
		public double? toz { get; set; }

	}

}
