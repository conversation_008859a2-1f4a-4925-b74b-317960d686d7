using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Messaging;
using HeroYulgang.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HeroYulgang.Core.Native.Services
{
    /// <summary>
    /// Service manager implementation - thay thế ActorSystem
    /// </summary>
    public class ServiceManager : IServiceManager, IServiceSupervisor, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IEventBus _eventBus;
        private readonly ConcurrentDictionary<Type, IService> _services;
        private readonly ConcurrentDictionary<Type, Func<IServiceProvider, IService>> _serviceFactories;
        private readonly ConcurrentDictionary<Type, RestartPolicy> _restartPolicies;
        private readonly ConcurrentDictionary<Type, ServiceHealthStatus> _healthStatuses;
        private readonly Timer _healthCheckTimer;
        private volatile bool _isDisposed;

        public ServiceManager(IServiceProvider serviceProvider, IEventBus eventBus)
        {
            _serviceProvider = serviceProvider;
            _eventBus = eventBus;
            _services = new ConcurrentDictionary<Type, IService>();
            _serviceFactories = new ConcurrentDictionary<Type, Func<IServiceProvider, IService>>();
            _restartPolicies = new ConcurrentDictionary<Type, RestartPolicy>();
            _healthStatuses = new ConcurrentDictionary<Type, ServiceHealthStatus>();

            // Health check timer
            _healthCheckTimer = new Timer(PerformHealthChecks, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }

        /// <summary>
        /// Get service instance
        /// </summary>
        public async Task<T> GetServiceAsync<T>() where T : class, IService
        {
            var serviceType = typeof(T);

            // Nếu service đã tồn tại, return nó
            if (_services.TryGetValue(serviceType, out var existingService))
            {
                return (T)existingService;
            }

            // Tạo service mới
            var service = CreateService<T>();
            _services[serviceType] = service;

            // Auto-start nếu chưa start
            if (service.State == ServiceState.NotStarted)
            {
                await StartServiceAsync<T>();
            }

            return (T)service;
        }

        /// <summary>
        /// Start service
        /// </summary>
        public async Task StartServiceAsync<T>() where T : class, IService
        {
            var service = await GetServiceAsync<T>();
            
            if (service.State == ServiceState.Running)
                return;

            try
            {
                await service.StartAsync();
                await _eventBus.PublishAsync(new ServiceStartedEvent(service.ServiceId));
                Logger.Instance.Info($"Service {typeof(T).Name} started successfully");
            }
            catch (Exception ex)
            {
                await _eventBus.PublishAsync(new ServiceFailedEvent(service.ServiceId, ex));
                Logger.Instance.Error($"Failed to start service {typeof(T).Name}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Stop service
        /// </summary>
        public async Task StopServiceAsync<T>() where T : class, IService
        {
            var serviceType = typeof(T);
            
            if (!_services.TryGetValue(serviceType, out var service))
                return;

            try
            {
                await service.StopAsync();
                await _eventBus.PublishAsync(new ServiceStoppedEvent(service.ServiceId));
                Logger.Instance.Info($"Service {typeof(T).Name} stopped successfully");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error stopping service {typeof(T).Name}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Restart service
        /// </summary>
        public async Task RestartServiceAsync<T>() where T : class, IService
        {
            var serviceType = typeof(T);
            
            if (!_services.TryGetValue(serviceType, out var service))
            {
                await StartServiceAsync<T>();
                return;
            }

            try
            {
                await _eventBus.PublishAsync(new ServiceRestartingEvent(service.ServiceId, 1));
                
                await service.StopAsync();
                await Task.Delay(TimeSpan.FromSeconds(1)); // Brief delay
                await service.StartAsync();
                
                Logger.Instance.Info($"Service {typeof(T).Name} restarted successfully");
            }
            catch (Exception ex)
            {
                await _eventBus.PublishAsync(new ServiceFailedEvent(service.ServiceId, ex));
                Logger.Instance.Error($"Failed to restart service {typeof(T).Name}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Register service factory
        /// </summary>
        public void RegisterService<T>(Func<IServiceProvider, T> factory) where T : class, IService
        {
            _serviceFactories[typeof(T)] = provider => factory(provider);
        }

        /// <summary>
        /// Register service instance
        /// </summary>
        public void RegisterService<T>(T instance) where T : class, IService
        {
            _services[typeof(T)] = instance;
        }

        /// <summary>
        /// Get all services of type
        /// </summary>
        public async Task<T[]> GetServicesAsync<T>() where T : class, IService
        {
            var services = _services.Values.OfType<T>().ToArray();
            return services;
        }

        /// <summary>
        /// Check if service is registered
        /// </summary>
        public bool IsServiceRegistered<T>() where T : class, IService
        {
            return _services.ContainsKey(typeof(T)) || _serviceFactories.ContainsKey(typeof(T));
        }

        /// <summary>
        /// Get service state
        /// </summary>
        public ServiceState GetServiceState<T>() where T : class, IService
        {
            var serviceType = typeof(T);
            return _services.TryGetValue(serviceType, out var service) ? service.State : ServiceState.NotStarted;
        }

        /// <summary>
        /// Create service instance
        /// </summary>
        private T CreateService<T>() where T : class, IService
        {
            var serviceType = typeof(T);

            // Thử factory trước
            if (_serviceFactories.TryGetValue(serviceType, out var factory))
            {
                return (T)factory(_serviceProvider);
            }

            // Thử DI container
            var service = _serviceProvider.GetService<T>();
            if (service != null)
            {
                return service;
            }

            // Tạo instance trực tiếp
            try
            {
                return (T)Activator.CreateInstance(serviceType)!;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Cannot create service {serviceType.Name}: {ex.Message}", ex);
            }
        }

        #region IServiceSupervisor Implementation

        /// <summary>
        /// Monitor service health
        /// </summary>
        public async Task MonitorServiceAsync<T>(T service) where T : IService
        {
            var serviceType = typeof(T);
            
            try
            {
                var isHealthy = await service.IsHealthyAsync();
                var healthStatus = _healthStatuses.GetOrAdd(serviceType, _ => new ServiceHealthStatus());
                
                healthStatus.IsHealthy = isHealthy;
                healthStatus.LastCheckTime = DateTime.UtcNow;
                
                if (!isHealthy)
                {
                    healthStatus.FailureCount++;
                    healthStatus.LastFailureTime = DateTime.UtcNow;
                    
                    Logger.Instance.Warning($"Service {service.ServiceId} health check failed");
                    
                    // Handle failure based on restart policy
                    if (_restartPolicies.TryGetValue(serviceType, out var policy))
                    {
                        await HandleServiceFailureAsync(service, new Exception("Health check failed"));
                    }
                }
                else
                {
                    healthStatus.FailureCount = 0;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error monitoring service {service.ServiceId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Set restart policy
        /// </summary>
        public void SetRestartPolicy<T>(RestartPolicy policy) where T : IService
        {
            _restartPolicies[typeof(T)] = policy;
        }

        /// <summary>
        /// Handle service failure
        /// </summary>
        public async Task HandleServiceFailureAsync<T>(T service, Exception exception) where T : IService
        {
            var serviceType = typeof(T);
            
            if (!_restartPolicies.TryGetValue(serviceType, out var policy))
                policy = RestartPolicy.OnFailure;

            switch (policy)
            {
                case RestartPolicy.Never:
                    Logger.Instance.Info($"Service {service.ServiceId} failed, restart policy is Never");
                    break;
                    
                case RestartPolicy.OnFailure:
                case RestartPolicy.Always:
                    Logger.Instance.Info($"Restarting service {service.ServiceId} due to failure");
                    try
                    {
                        await RestartServiceAsync<T>();
                    }
                    catch (Exception restartEx)
                    {
                        Logger.Instance.Error($"Failed to restart service {service.ServiceId}: {restartEx.Message}");
                    }
                    break;
                    
                case RestartPolicy.Exponential:
                    // Implement exponential backoff
                    var healthStatus = _healthStatuses.GetOrAdd(serviceType, _ => new ServiceHealthStatus());
                    var delay = TimeSpan.FromSeconds(Math.Pow(2, Math.Min(healthStatus.FailureCount, 6))); // Max 64 seconds
                    
                    Logger.Instance.Info($"Restarting service {service.ServiceId} after {delay.TotalSeconds} seconds delay");
                    await Task.Delay(delay);
                    
                    try
                    {
                        await RestartServiceAsync<T>();
                    }
                    catch (Exception restartEx)
                    {
                        Logger.Instance.Error($"Failed to restart service {service.ServiceId}: {restartEx.Message}");
                    }
                    break;
            }
        }

        /// <summary>
        /// Get service health status
        /// </summary>
        public async Task<ServiceHealthStatus> GetServiceHealthAsync<T>() where T : IService
        {
            var serviceType = typeof(T);
            return _healthStatuses.GetOrAdd(serviceType, _ => new ServiceHealthStatus());
        }

        #endregion

        /// <summary>
        /// Perform health checks for all services
        /// </summary>
        private async void PerformHealthChecks(object? state)
        {
            if (_isDisposed)
                return;

            var tasks = new List<Task>();
            
            foreach (var service in _services.Values.ToList())
            {
                if (service.State == ServiceState.Running)
                {
                    tasks.Add(MonitorServiceAsync(service));
                }
            }

            if (tasks.Count > 0)
            {
                try
                {
                    await Task.WhenAll(tasks);
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"Error in health check cycle: {ex.Message}");
                }
            }
        }

        public void Dispose()
        {
            if (_isDisposed)
                return;

            _isDisposed = true;

            try
            {
                _healthCheckTimer?.Dispose();

                // Stop all services
                var stopTasks = _services.Values.Select(service => service.StopAsync()).ToArray();
                Task.WaitAll(stopTasks, TimeSpan.FromSeconds(30));

                // Dispose services
                foreach (var service in _services.Values)
                {
                    if (service is IDisposable disposable)
                    {
                        disposable.Dispose();
                    }
                }

                _services.Clear();
                _serviceFactories.Clear();
                _restartPolicies.Clear();
                _healthStatuses.Clear();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error disposing ServiceManager: {ex.Message}");
            }
        }
    }
}
