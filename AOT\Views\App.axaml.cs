using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using HeroYulgang.Services;

namespace HeroYulgang.Views;

public partial class App : Application
{
    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
    }

    public override void OnFrameworkInitializationCompleted()
    {
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            desktop.MainWindow = new MainWindow();

            // Đ<PERSON><PERSON> bảo SeriLog được đóng đúng cách khi ứng dụng tắt
            desktop.ShutdownRequested += (sender, e) =>
            {
                Logger.CloseAndFlush();
            };
        }

        base.OnFrameworkInitializationCompleted();
    }
}