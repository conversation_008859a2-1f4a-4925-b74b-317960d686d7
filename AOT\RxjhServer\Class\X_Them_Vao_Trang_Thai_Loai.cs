using System;
using System.Timers;
using HeroYulgang.Helpers;
using LogLevel = HeroYulgang.Services.LogLevel;
namespace RxjhServer;

public class X_Them_Vao_Trang_Thai_Loai : IDisposable
{
	public System.Timers.Timer npcyd;

	public DateTime time;

	public Players Play;

	private double _vale;

	private int _FLD_PID;

	private int _FLD_RESIDE1;

	private double vale
	{
		get
		{
			return _vale;
		}
		set
		{
			_vale = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public int FLD_RESIDE1
	{
		get
		{
			return _FLD_RESIDE1;
		}
		set
		{
			_FLD_RESIDE1 = value;
		}
	}

	public int FLD_sj => getsj();

	public void Dispose()
	{
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		if (Play != null)
		{
			Play = null;
		}
	}

	~X_Them_Vao_Trang_Thai_Loai()
	{
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
	}

	public X_Them_Vao_Trang_Thai_Loai(Players Play_, int ThoiGian, int VatPham_ID, int FLD_RESIDE1)
	{
		FLD_PID = VatPham_ID;
		this.FLD_RESIDE1 = FLD_RESIDE1;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Play = Play_;
		npcyd = new(ThoiGian);
		npcyd.Elapsed += ThoiGianKetThucSuKien2;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
	}

	public X_Them_Vao_Trang_Thai_Loai(Players Play_, double ThoiGian, double VatPham_ID, double FLD_RESIDE1)
	{
		FLD_PID = (int)VatPham_ID;
		this.FLD_RESIDE1 = (int)FLD_RESIDE1;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Play = Play_;
		npcyd = new(ThoiGian);
		npcyd.Elapsed += ThoiGianKetThucSuKien3;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
	}

	public X_Them_Vao_Trang_Thai_Loai(Players Play_, int ThoiGian, int VatPham_ID, int FLD_RESIDE1, double vale)
	{
		FLD_PID = VatPham_ID;
		this.FLD_RESIDE1 = FLD_RESIDE1;
		this.vale = vale;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Play = Play_;
		npcyd = new(ThoiGian);
		npcyd.Elapsed += ThoiGianKetThucSuKien2;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		ThoiGianKetThucSuKien();
	}

	public void ThoiGianKetThucSuKien3(object sender, ElapsedEventArgs e)
	{
		ThoiGianKetThucSuKien1();
	}

	public void ThoiGianKetThucSuKien1()
	{
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		var num = 25;
		if (Play != null && !Play.Client.TreoMay)
		{
			if (!Play.Exiting && Play.Client.Running)
			{
				try
				{
					if (Play.GetAddState(FLD_PID))
					{
						Play.AppendStatusList.Remove(FLD_PID);
					}
					num = 10;
					Dispose();
					return;
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "thêm vào VoCongTrangThai tốt bụng ThoiGianKetThucSuKien error：" + num + "[" + FLD_PID + "]" + ex);
					return;
				}
				finally
				{
					Dispose();
				}
			}
			if (Play.AppendStatusList != null)
			{
				Play.AppendStatusList.Clear();
			}
			Dispose();
		}
		else
		{
			Dispose();
		}
	}

	public void ThoiGianKetThucSuKien()
	{
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		if (Play != null)
		{
			if (!Play.Exiting && Play.Client.Running)
			{
				try
				{
					switch (FLD_PID)
					{
					case 94:
						Play.AppendStatusList.Remove(94);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 95:
						Play.AppendStatusList.Remove(95);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 96:
						Play.AppendStatusList.Remove(96);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 200:
						Play.AppendStatusList.Remove(200);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008001797:
						Play.DCH_StackA = 0;
						break;
					case 1008001798:
						Play.DCH_StackB = 0;
						break;
					case 1008001799:
						Play.DCH_StackC = 0;
						break;
					case 574:
						Play.AppendStatusList.Remove(574);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 700662:
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1000000954:
						Play.AppendStatusList.Remove(1000000954);
						break;
					case 700291:
						Play.TriggerAttributePromotion = 0;
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1000000815:
					case 1000000816:
						return;
					case 201201:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.05);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 242:
						Play.FLD_NhanVat_ThemVao_CongKich -= 15;
						Play.FLD_NhanVat_ThemVao_PhongNgu -= 15;
						Play.CharactersToAddMax_HP -= 300;
						Play.CharactersToAddMax_MP -= 300;
						Play.FLD_NhanVat_ThemVao_KinhNghiem_KetHon = 0.0;
						Play.FLD_KetHonLeVat_ThemVaoThuocTinhThach = 0;
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 401203:
						Play.FLD_NhanVat_ThemVao_TrungDich += 20;
						Play.FLD_NhanVat_ThemVao_NeTranh -= 40;
						Play.UpdateMartialArtsAndStatus();
						break;
					case 401202:
						Play.FLD_NhanVat_ThemVao_NeTranh += 20;
						Play.FLD_NhanVat_ThemVao_TrungDich -= 40;
						Play.UpdateMartialArtsAndStatus();
						break;
					case 301201:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.02);
						Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.02;
						if (Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.UpdateMartialArtsAndStatus();
						break;
					case 501301:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(vale);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 501302:
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(vale);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 501303:
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(vale);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 401401:
						Play.FLD_ThemVaoTiLePhanTram_TrungDich -= 0.05;
						Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.02;
						if (Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
						}
						if (Play.FLD_ThemVaoTiLePhanTram_TrungDich < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.CapNhat_HP_MP_SP();
						break;
					case 401301:
						Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.1;
						if (Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.CapNhat_HP_MP_SP();
						break;
					case 401302:
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.UpdateMartialArtsAndStatus();
						break;
					case 401303:
						switch (Play.Player_Job)
						{
						case 1:
							Play.ChanVu_TuyetKich -= 10.0;
							if (Play.ChanVu_TuyetKich < 0.0)
							{
								Play.ChanVu_TuyetKich = 0.0;
							}
							break;
						case 2:
							Play.KIEM_NoHai_CuongLan -= 10.0;
							if (Play.KIEM_NoHai_CuongLan < 0.0)
							{
								Play.KIEM_NoHai_CuongLan = 0.0;
							}
							break;
						case 3:
							Play.THUONG_NoYChiHong -= 10.0;
							if (Play.THUONG_NoYChiHong < 0.0)
							{
								Play.THUONG_NoYChiHong = 0.0;
							}
							break;
						case 4:
							Play.CUNG_TriMenhTuyetSat -= 10.0;
							if (Play.CUNG_TriMenhTuyetSat < 0.0)
							{
								Play.CUNG_TriMenhTuyetSat = 0.0;
							}
							break;
						case 5:
							Play.ChanVu_TuyetKich -= 10.0;
							if (Play.ChanVu_TuyetKich < 0.0)
							{
								Play.ChanVu_TuyetKich = 0.0;
							}
							break;
						case 6:
							Play.NINJA_TamThanNgungTu -= 10.0;
							if (Play.NINJA_TamThanNgungTu < 0.0)
							{
								Play.NINJA_TamThanNgungTu = 0.0;
							}
							break;
						case 7:
							Play.CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet -= 10.0;
							if (Play.CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet < 0.0)
							{
								Play.CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet = 0.0;
							}
							break;
						case 8:
							Play.ChanVu_TuyetKich -= 10.0;
							if (Play.ChanVu_TuyetKich < 0.0)
							{
								Play.ChanVu_TuyetKich = 0.0;
							}
							break;
						case 9:
							Play.DamHoaLien_NoHai_CuongLan -= 10.0;
							if (Play.DamHoaLien_NoHai_CuongLan < 0.0)
							{
								Play.DamHoaLien_NoHai_CuongLan = 0.0;
							}
							break;
						case 12:
							Play.ChanVu_TuyetKich -= 10.0;
							if (Play.ChanVu_TuyetKich < 0.0)
							{
								Play.ChanVu_TuyetKich = 0.0;
							}
							break;
						}
						break;
					case 501502:
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(vale);
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 501501:
						Play.AppendStatusList.Remove(FLD_PID);
						Play.delFLD_ThemVaoTiLePhanTram_Attack(vale);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 501401:
						Play.FLD_ThemVaoTiLePhanTram_TrungDich -= vale;
						if (Play.FLD_ThemVaoTiLePhanTram_TrungDich < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 501402:
						Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= vale;
						if (Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh < 0.0)
						{
							Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 501403:
						Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= vale;
						if (Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0)
						{
							Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CapNhat_HP_MP_SP();
						break;
					case 601101:
						Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
						Play.WalkingStatusId = 1;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateMovementSpeed();
						Dispose();
						return;
					case 601102:
						Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
						Play.WalkingStatusId = 1;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateMovementSpeed();
						Dispose();
						return;
					case 601103:
						if (Play == null)
						{
							return;
						}
						Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
						Play.WalkingStatusId = 1;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateMovementSpeed();
						break;
					case 501601:
						Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= vale;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.CapNhat_HP_MP_SP();
						break;
					case 501602:
						Play.FLD_ThemVaoTiLePhanTram_TrungDich -= vale;
						Play.UpdateMartialArtsAndStatus();
						break;
					case 501603:
						Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= vale;
						Play.UpdateMartialArtsAndStatus();
						break;
					case 700014:
						Play.NoKhi = false;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						if (Play.Player_Job == 3)
						{
							Play.CPVP_ThemVao_Dame = 0.0;
							Play.CPVP_ThemVao_Def = 0.0;
						}
						else if (Play.Player_Job == 4)
						{
							Play.CPVP_ThemVao_Dame = 0.0;
							Play.CPVP_ThemVao_Def = 0.0;
						}
						else if (Play.Player_Job == 5)
						{
							Play.CPVP_ThemVao_Dame = 0.0;
							Play.CPVP_ThemVao_Def = 0.0;
						}
						else if (Play.Player_Job == 10)
						{
							Play.CPVP_ThemVao_Dame = 0.0;
							Play.CPVP_ThemVao_Def = 0.0;
						}
						else if (Play.Player_Job == 11)
						{
							Play.CPVP_ThemVao_Dame = 0.0;
							Play.CharacterIsBasicallyTheLargest_Barrier = (int)(Play.CharacterIsBasicallyTheLargest_Barrier / 1.2 + 0.5);
							Play.MaiLieuChan_ChuongLucVanDung -= vale;
							Play.CapNhat_HP_MP_SP();
						}
						else
						{
							Play.CPVP_ThemVao_Dame = 0.0;
							Play.CPVP_ThemVao_Def = 0.0;
						}
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 601202:
						Play.FLD_PhuThe_HoTro_ThemVao_VuKhiThuocTinh = 0;
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 601201:
						Play.FLD_PhuThe_HoTro_ThemVao_DoPhongNguThuocTinh = 0;
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 700310:
					{
						var play = Play;
						play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
						Play.FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.3;
						if (Play.FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateMartialArtsAndStatus();
						break;
					}
					case 700301:
						Play.CharacterBeast.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.1;
						Play.CharacterBeast.FLD_ThemVaoTiLePhanTram_MPCaoNhat -= 0.1;
						Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateSpiritBeastHP_MP_SP();
						break;
					case 700302:
						Play.CharacterBeast.FLD_SpiritBeast_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
						break;
					case 700303:
						Play.CharacterBeast.FLD_SpiritBeast_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
						Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
						break;
					case 700201:
						Play.CharacterBeast.FLD_ThemVaoTiLePhanTram_NeTranh -= 0.1;
						Play.CharacterBeast.FLD_ThemVaoTiLePhanTram_TrungDich -= 0.1;
						Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateSpiritBeastMartialArtsAndStatus();
						break;
					case 700202:
						Play.CharacterBeast.dllFLD_ThemVaoTiLePhanTram_Attack(0.1);
						Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateSpiritBeastMartialArtsAndStatus();
						break;
					case 700203:
						Play.CharacterBeast.dllFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
						Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateSpiritBeastMartialArtsAndStatus();
						break;
					case 801201:
						Play.FLD_CongKichTocDo = 100;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.Update_CongKichTocDo();
						break;
					case 700401:
						Play.WalkingStateCharacterSpiritBeast(BitConverter.GetBytes(FLD_PID), 1);
						Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 300000);
						break;
					case 700402:
						Play.WalkingStateCharacterSpiritBeast(BitConverter.GetBytes(FLD_PID), 1);
						Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 300000);
						break;
					case 700403:
						Play.WalkingStateCharacterSpiritBeast(BitConverter.GetBytes(FLD_PID), 1);
						Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 300000);
						break;
					case 700350:
						Play.AppendStatusList.Remove(FLD_PID);
						Play.FLD_ThemVao_PhanTram_Def_HoanVuVanHoa = 0.0;
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1000001508:
						if (Play.AppendStatusList != null)
						{
							Play.NhanVat_BatTu = 0;
							Play.AppendStatusList.Remove(FLD_PID);
							Play.UpdateCharacterData(Play);
							Play.UpdateBroadcastCharacterData();
							Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						}
						break;
					case 900401:
						Play.CamSu_TrangThai = 0;
						Play.AppendStatusList.Remove(900401);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.StatusEffect(BitConverter.GetBytes(900401), 0, 0);
						break;
					case 900402:
						Play.CamSu_TrangThai = 0;
						Play.FLD_CamSu_HieuUng_SieuAmThanh_DameDef = 0.0;
						Play.AppendStatusList.Remove(900402);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.StatusEffect(BitConverter.GetBytes(900402), 0, 0);
						break;
					case 900403:
						Play.CamSu_TrangThai = 0;
						Play.AppendStatusList.Remove(900403);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.StatusEffect(BitConverter.GetBytes(900403), 0, 0);
						break;
					case 801302:
						Play.addFLD_ThemVaoTiLePhanTram_PhongNgu(0.2);
						Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= 1.0;
						if (Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh < 0.0)
						{
							Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.0;
						}
						Play.AppendStatusList.Remove(801302);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1001201:
						if (Play.GetAddState(1001101))
						{
							Play.WalkingStatusId = 6;
							Play.WalkingState(BitConverter.GetBytes(1001101), 6);
							Play.TocDoDiChuyen_Max = 78f;
						}
						else if (Play.GetAddState(1001102))
						{
							Play.WalkingStatusId = 7;
							Play.WalkingState(BitConverter.GetBytes(1001102), 7);
							Play.TocDoDiChuyen_Max = 90f;
						}
						else
						{
							Play.WalkingStatusId = 1;
							Play.WalkingState(BitConverter.GetBytes(1001101), 1);
							Play.TocDoDiChuyen_Max = 50f;
							Play.UpdateMovementSpeed();
						}
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						break;
					case 1001102:
						Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
						Play.WalkingStatusId = 1;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateMovementSpeed();
						Dispose();
						return;
					case 1001101:
						Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
						Play.WalkingStatusId = 1;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						Play.UpdateMovementSpeed();
						Dispose();
						return;
					case 2001301:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.03);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1001301:
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1001302:
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1001303:
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.15;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1001202:
						if (Play.GetAddState(1001101))
						{
							Play.WalkingStatusId = 6;
							Play.WalkingState(BitConverter.GetBytes(1001101), 6);
							Play.TocDoDiChuyen_Max = 78f;
						}
						else if (Play.GetAddState(1001102))
						{
							Play.WalkingStatusId = 7;
							Play.WalkingState(BitConverter.GetBytes(1001102), 7);
							Play.TocDoDiChuyen_Max = 90f;
						}
						else
						{
							Play.WalkingStatusId = 1;
							Play.WalkingState(BitConverter.GetBytes(1001101), 1);
							Play.TocDoDiChuyen_Max = 50f;
						}
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						break;
					case 9000088:
					case 9000104:
					case 9000105:
					case 9000144:
					case 9000145:
					case 9000168:
					case 9001214:
					case 9009077:
					case 9009092:
					case 9009151:
					case 9009788:
					case 9009789:
					case 9009790:
					case 9009820:
					case 9009964:
						Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem -= 0.5;
						break;
					case 9000085:
					case 9000120:
					case 9000121:
					case 9000165:
					case 9000166:
					case 9000317:
					case 9001207:
					case 9001211:
					case 9001215:
					case 9009076:
					case 9009150:
					case 9009217:
					case 9009309:
					case 9009839:
					case 9009941:
					case 9009943:
					case 9009965:
					case 9009966:
					case 9009970:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
						break;
					case 900000619:
						Play.SafeMode = 0;
						break;
					case 1000000167:
					case 1000000168:
					case 1000000170:
					case 1000000171:
					case 1000000173:
					case 1000000174:
					case 1000000176:
					case 1000000177:
						if (Play.CharacterBeast != null)
						{
							if (FLD_PID != 1000000167 && FLD_PID != 1000000170 && FLD_PID != 1000000173 && FLD_PID != 1000000176)
							{
								Play.CharacterBeast.FLD_VatPham_ThemVao_HP -= 200;
								Play.CharacterBeast.FLD_VatPham_ThemVao_MP -= 100;
								if (Play.CharacterBeast.FLD_HP > Play.CharacterBeast.Beast_BasicallyTheLargest_HP)
								{
									Play.CharacterBeast.FLD_HP = Play.CharacterBeast.Beast_BasicallyTheLargest_HP;
								}
								if (Play.CharacterBeast.FLD_MP > Play.CharacterBeast.Beast_BasicallyTheLargest_MP)
								{
									Play.CharacterBeast.FLD_MP = Play.CharacterBeast.Beast_BasicallyTheLargest_MP;
								}
							}
							else
							{
								Play.CharacterBeast.FLD_VatPham_ThemVao_HP -= 100;
								Play.CharacterBeast.FLD_VatPham_ThemVao_MP -= 90;
								if (Play.CharacterBeast.FLD_HP > Play.CharacterBeast.Beast_BasicallyTheLargest_HP)
								{
									Play.CharacterBeast.FLD_HP = Play.CharacterBeast.Beast_BasicallyTheLargest_HP;
								}
								if (Play.CharacterBeast.FLD_MP > Play.CharacterBeast.Beast_BasicallyTheLargest_MP)
								{
									Play.CharacterBeast.FLD_MP = Play.CharacterBeast.Beast_BasicallyTheLargest_MP;
								}
							}
							Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
							Play.UpdateSpiritBeastHP_MP_SP();
							if (Play.AppendStatusList != null)
							{
								Play.AppendStatusList.Remove(FLD_PID);
							}
							Dispose();
						}
						return;
					case 1000000030:
						Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem -= 10.0;
						if (Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem = 0.0;
						}
						break;
					case 900000072:
					case 900000073:
					case 900000074:
					case 900000258:
					case 999000163:
					case 999000164:
					case 999000165:
						if (Play.QueryThienQuanDiaDoMap(Play.MapID))
						{
							Play.Mobile(529f, 1528f, 15f, 101, 0);
						}
						break;
					case 1000000775:
						Play.FLD_NhanVat_ThemVao_CongKich_HP_MP = 0;
						if (Play.FLD_NhanVat_ThemVao_CongKich_HP_MP < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich_HP_MP = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram_HP_MP = 0.0;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram_HP_MP < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram_HP_MP = 0.0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1000000776:
						Play.FLD_NhanVat_ThemVao_PhongNgu_HP_MP = 0;
						if (Play.FLD_NhanVat_ThemVao_PhongNgu_HP_MP < 0)
						{
							Play.FLD_NhanVat_ThemVao_PhongNgu_HP_MP = 0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram_HP_MP = 0.0;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram_HP_MP < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram_HP_MP = 0.0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1000000646:
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008000106:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008000107:
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008000163:
						Play.CharactersToAddMax_HP -= 500;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.05;
						Play.AppendStatusList.Remove(1008000163);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008000169:
						Play.CharactersToAddMax_HP -= 500;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.CharactersToAddMax_MP -= 300;
						if (Play.NhanVat_MP > Play.CharacterMax_MP)
						{
							Play.NhanVat_MP = Play.CharacterMax_MP;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.05;
						Play.AppendStatusList.Remove(1008000169);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008000246:
						Play.CharactersToAddMax_HP -= 1000;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.05);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
						Play.AppendStatusList.Remove(1008000246);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000162:
						Play.CharactersToAddMax_HP -= 700;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1000000830:
						Play.CharactersToAddMax_HP -= 100;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1000000831:
						Play.CharactersToAddMax_HP -= 50;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1000000832:
						Play.CharactersToAddMax_HP -= 100;
						Play.CharactersToAddMax_HP -= 100;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						if (Play.NhanVat_MP > Play.CharacterMax_MP)
						{
							Play.NhanVat_MP = Play.CharacterMax_MP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1000000835:
						Play.FLD_ThemVaoTiLePhanTram_MPCaoNhat -= 0.05;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1000000836:
						Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.05;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000016:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
						Play.AppendStatusList.Remove(1008000016);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008000017:
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
						Play.AppendStatusList.Remove(1008000017);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1000000011:
					case 1008000018:
						Play.delFLD_TrangBi_ThemVao_VuKhi_CuongHoa(2);
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1000000012:
					case 1008000019:
						Play.delFLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa(1);
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008000055:
						Play.CharactersToAddMax_HP -= 300;
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.2);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.2);
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1007000005:
						Play.CharactersToAddMax_HP -= 300;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1007000006:
						Play.CharactersToAddMax_HP -= 500;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.CapNhat_HP_MP_SP();
						break;
					case 1007000007:
						Play.CharactersToAddMax_HP -= 700;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(1007000007);
						Play.CapNhat_HP_MP_SP();
						break;
					case 1007000014:
						Play.CharactersToAddMax_HP -= 700;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008001139:
						Play.CharactersToAddMax_HP -= 10000;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008001140:
						Play.CharactersToAddMax_HP -= 5000;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CalculateCharacterEquipmentData();
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000183:
						Play.AppendStatusList.Remove(1008000183);
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.CharactersToAddMax_HP -= 1000;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.05);
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008000193:
						Play.AppendStatusList.Remove(1008000193);
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.1;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.CharactersToAddMax_HP -= 300;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008000156:
						Play.AppendStatusList.Remove(1008000156);
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.1;
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
						Play.FLD_NhanVat_ThemVao_TrungDich -= 40;
						Play.FLD_NhanVat_ThemVao_NeTranh += 20;
						Play.CapNhat_HP_MP_SP();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008000095:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						break;
					case 1008000096:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.3;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						break;
					case 1008000097:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						break;
					case 1008000195:
					case 1008001100:
						Play.CharactersToAddMax_HP -= 300;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000187:
						Play.AppendStatusList.Remove(1008000187);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
						Play.FLD_NhanVat_KhiCong_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
						Play.CharactersToAddMax_HP -= 1000;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008000188:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.15);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.15);
						Play.CharactersToAddMax_HP -= 300;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.CharactersToAddMax_MP -= 300;
						if (Play.CharactersToAddMax_MP < 0)
						{
							Play.CharactersToAddMax_MP = 0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList?.Remove(1008000188);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008001021:
						Play.AppendStatusList.Remove(1008001021);
						Play.UpdateKhiCong();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008001031:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.05);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
						Play.CharactersToAddMax_HP -= 600;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.FLD_NhanVat_ThemVao_KhiCong--;
						if (Play.FLD_NhanVat_ThemVao_KhiCong < 0)
						{
							Play.FLD_NhanVat_ThemVao_KhiCong = 0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(1008001031);
						Play.UpdateKhiCong();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000212:
					case 1008001545:
						Play.CharactersToAddMax_HP -= 500;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.15);
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.15;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_TrungDich -= 20;
						Play.FLD_NhanVat_ThemVao_NeTranh -= 20;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008000213:
					case 1008001546:
						Play.CharactersToAddMax_HP -= 1000;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.15;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.15);
						Play.FLD_ThemVaoTiLePhanTram_TrungDich -= 0.02;
						Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= 0.02;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008001030:
						Play.AppendStatusList.Remove(1008001030);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008000321:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						break;
					case 1008000322:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						break;
					case 1008000323:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.0;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						break;
					case 1008000325:
						Play.FLD_NhanVat_ThemVao_CongKich -= 30;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000326:
						Play.FLD_NhanVat_ThemVao_CongKich -= 30;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000304:
						Play.FLD_NhanVat_ThemVao_CongKich -= 30;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_ThemVao_PhongNgu -= 30;
						if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0)
						{
							Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(1008000304);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000305:
						Play.FLD_NhanVat_ThemVao_CongKich -= 30;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(1008000305);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000306:
						Play.FLD_NhanVat_ThemVao_CongKich -= 40;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_ThemVao_PhongNgu -= 40;
						if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0)
						{
							Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(1008000306);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000307:
						Play.FLD_NhanVat_ThemVao_CongKich -= 40;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_ThemVao_PhongNgu -= 40;
						if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0)
						{
							Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(1008000307);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000232:
						Play.CharactersToAddMax_HP -= 300;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 0.2;
						if (Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram = 0.0;
						}
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(1008000232);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 999000900:
						Play.CharactersToAddMax_HP -= 500;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.1;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -= 0.2;
						if (Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 0.2;
						if (Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.1;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem -= 0.3;
						if (Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem = 0.0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(999000900);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 999000485:
						Play.CharactersToAddMax_HP -= 250;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.AppendStatusList.Remove(999000485);
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.05);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 999000291:
						Play.CharactersToAddMax_HP -= 500;
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.AppendStatusList.Remove(999000291);
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.05);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008000387:
						Play.AppendStatusList.Remove(1008000387);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000909:
						Play.CharactersToAddMax_HP -= 100;
						Play.CharactersToAddMax_MP -= 300;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						if (Play.CharactersToAddMax_MP < 0)
						{
							Play.CharactersToAddMax_MP = 0;
						}
						Play.FLD_NhanVat_ThemVao_CongKich -= 20;
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1000000882:
						Play.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram -= 0.04;
						Play.AppendStatusList.Remove(1000000882);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1000000908:
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.CapNhat_HP_MP_SP();
						break;
					case 900000174:
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000240:
						Play.FLD_NhanVat_ThemVao_CongKich -= 30;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000241:
						Play.FLD_NhanVat_ThemVao_CongKich -= 30;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000242:
						Play.FLD_NhanVat_ThemVao_CongKich -= 30;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000245:
						Play.CharactersToAddMax_HP -= 100;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.FLD_NhanVat_ThemVao_NeTranh -= (int)(Play.FLD_NeTranh * 0.05);
						if (Play.FLD_NhanVat_ThemVao_NeTranh < 0)
						{
							Play.FLD_NhanVat_ThemVao_NeTranh = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_KhiCong--;
						if (Play.FLD_NhanVat_ThemVao_KhiCong < 0)
						{
							Play.FLD_NhanVat_ThemVao_KhiCong = 0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(1008000245);
						Play.UpdateKhiCong();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000248:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.0;
						Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -= 1.0;
						Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 1.0;
						Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem -= 1.0;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						break;
					case 1008000250:
						Play.FLD_NhanVat_ThemVao_CongKich -= 30;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000251:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.0;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_CongKich -= 30;
						if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
						{
							Play.FLD_NhanVat_ThemVao_CongKich = 0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(1008000251);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008000252:
						Play.FLD_NhanVat_ThemVao_PhongNgu--;
						if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0)
						{
							Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
						}
						Play.CharactersToAddMax_HP--;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.FLD_NhanVat_ThemVao_NeTranh--;
						if (Play.FLD_NhanVat_ThemVao_NeTranh < 0)
						{
							Play.FLD_NhanVat_ThemVao_NeTranh = 0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.01;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.2;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						if (Play.NhanVat_HP > Play.CharacterMax_HP)
						{
							Play.NhanVat_HP = Play.CharacterMax_HP;
						}
						Play.AppendStatusList.Remove(1008000252);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008000388:
						Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien -= 3.0;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien = 0.0;
						}
						Play.AppendStatusList.Remove(1008000388);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008001836:
						Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien -= 3.0;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien = 0.0;
						}
						Play.AppendStatusList.Remove(1008001836);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000363:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.5;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.AppendStatusList.Remove(1008000363);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000362:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.5;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.AppendStatusList.Remove(1008000362);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000324:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.5;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.AppendStatusList.Remove(1008000324);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000239:
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.0;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.AppendStatusList.Remove(1008000239);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008002191:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
						Play.CharactersToAddMax_HP -= 500;
						Play.CharactersToAddMax_MP -= 500;
						Play.AppendStatusList.Remove(1008002191);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008001111:
						Play.CharactersToAddMax_HP -= 500;
						Play.CharactersToAddMax_MP -= 500;
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_CongKich -= 50;
						Play.FLD_NhanVat_ThemVao_PhongNgu -= 100;
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(1008001111);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008001113:
						Play.CharactersToAddMax_HP -= 500;
						Play.CharactersToAddMax_MP -= 500;
						Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
						if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_CongKich -= 50;
						Play.FLD_NhanVat_ThemVao_PhongNgu -= 100;
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(1008001113);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					default:
						if (FLD_PID == World.IdItemX2)
						{
							Play.HeThongNhacNho(" [+" + Play.FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan * 100.0 + "%] Nội công tu vi [Nhật Nguyệt] đã đi vào trầm tĩnh!", 10, "Thiên Cơ Lệnh");
							Play.FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan = 0.0;
							Play.AppendStatusList.Remove(World.IdItemX2);
							Play.UpdateMartialArtsAndStatus();
							Play.CapNhat_HP_MP_SP();
						}
						if (FLD_PID == World.IdItemX3)
						{
							Play.HeThongNhacNho(" [+" + Play.FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan * 100.0 + "%] Nội công tu vi [Âm Dương] đã kết thúc chu kỳ!", 10, "Thiên Cơ Lệnh");
							Play.FLD_NhanVat_ThemVao_KinhNghiem_CuoiTuan = 0.0;
							Play.AppendStatusList.Remove(World.IdItemX3);
							Play.UpdateMartialArtsAndStatus();
							Play.CapNhat_HP_MP_SP();
						}
						if (FLD_PID == World.IdItem_Bonus)
						{
							Play.HeThongNhacNho(" [+" + Play.FLD_NhanVat_ThemVao_KinhNghiem_Bonus * 100.0 + "%] Thiên địa linh khí [Đặc Biệt] đã tản đi!", 10, "Thiên Cơ Lệnh");
							Play.FLD_NhanVat_ThemVao_KinhNghiem_Bonus = 0.0;
							Play.AppendStatusList.Remove(World.IdItem_Bonus);
							Play.UpdateMartialArtsAndStatus();
							Play.CapNhat_HP_MP_SP();
						}
						if (FLD_PID == World.IdItem_Party)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_Party = 0.0;
							Play.AppendStatusList.Remove(World.IdItem_Party);
							Play.UpdateMartialArtsAndStatus();
							Play.CapNhat_HP_MP_SP();
						}
						if (FLD_PID == *********)
						{
							Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -= 1.5;
							Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 1.5;
							Play.AppendStatusList.Remove(*********);
							Play.UpdateMartialArtsAndStatus();
							Play.CapNhat_HP_MP_SP();
						}
						if (FLD_PID == *********)
						{
							Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -= 1.0;
							Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 1.0;
							Play.AppendStatusList.Remove(*********);
							Play.UpdateMartialArtsAndStatus();
							Play.CapNhat_HP_MP_SP();
						}
						if (FLD_PID == 999000234)
						{
							Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -= 0.5;
							Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 0.5;
							Play.AppendStatusList.Remove(999000234);
							Play.UpdateMartialArtsAndStatus();
							Play.CapNhat_HP_MP_SP();
						}
						if (FLD_PID == 1000000920 && (!Play.PublicDrugs.ContainsKey(1008000311) || !Play.PublicDrugs.ContainsKey(1008000312)))
						{
							Play.FLD_NhanVat_ThemVao_Exp_CTP = 0.0;
							Play.AppendStatusList.Remove(1000000920);
							Play.UpdateMartialArtsAndStatus();
							Play.CapNhat_HP_MP_SP();
						}
						if (FLD_PID == 242)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_KetHon = 0.0;
							Play.AppendStatusList.Remove(FLD_PID);
							Play.UpdateMartialArtsAndStatus();
							Play.CapNhat_HP_MP_SP();
						}
						break;
					case 1008000001:
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.CalculateCharacterEquipmentData();
						break;
					case 1008000389:
						Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien -= 1.5;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien = 0.0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008001837:
						Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien -= 1.5;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien = 0.0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008000397:
						Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien -= 0.5;
						if (Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien < 0.0)
						{
							Play.FLD_NhanVat_ThemVao_KinhNghiem_TheLucChien = 0.0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1000000891:
					case 1000000892:
					case 1000000893:
					case 1008001382:
					case 1008001383:
					case 1008001384:
					case 1008001385:
						switch (Play.FLD_loveDegreeLevel)
						{
						case 1:
							Play.CharactersToAddMax_HP -= 150;
							Play.FLD_NhanVat_ThemVao_CongKich -= 15;
							Play.FLD_NhanVat_ThemVao_PhongNgu -= 15;
							Play.FLD_NhanVat_ThemVao_KhiCong--;
							Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
							Play.UpdateKhiCong();
							break;
						case 2:
							Play.CharactersToAddMax_HP -= 150;
							Play.FLD_NhanVat_ThemVao_CongKich -= 15;
							Play.FLD_NhanVat_ThemVao_PhongNgu -= 15;
							Play.FLD_NhanVat_ThemVao_KhiCong--;
							Play.UpdateKhiCong();
							break;
						case 3:
							Play.CharactersToAddMax_HP -= 150;
							Play.FLD_NhanVat_ThemVao_CongKich -= 15;
							Play.FLD_NhanVat_ThemVao_PhongNgu -= 15;
							break;
						case 4:
							Play.CharactersToAddMax_HP -= 150;
							Play.FLD_NhanVat_ThemVao_CongKich -= 10;
							Play.FLD_NhanVat_ThemVao_PhongNgu -= 10;
							break;
						case 5:
							Play.CharactersToAddMax_HP -= 150;
							Play.FLD_NhanVat_ThemVao_CongKich -= 10;
							Play.FLD_NhanVat_ThemVao_PhongNgu -= 5;
							break;
						case 6:
							Play.CharactersToAddMax_HP -= 150;
							Play.FLD_NhanVat_ThemVao_CongKich -= 5;
							Play.FLD_NhanVat_ThemVao_PhongNgu -= 5;
							break;
						case 7:
							Play.CharactersToAddMax_HP -= 150;
							Play.FLD_NhanVat_ThemVao_CongKich -= 5;
							break;
						case 8:
							Play.CharactersToAddMax_HP -= 150;
							break;
						case 9:
							Play.CharactersToAddMax_HP -= 100;
							break;
						case 10:
							Play.CharactersToAddMax_HP -= 50;
							break;
						}
						Play.CapNhat_HP_MP_SP();
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008001169:
						Play.AppendStatusList.Remove(FLD_PID);
						Play.addFLD_ThemVaoTiLePhanTram_PhongNgu(0.15);
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008001170:
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008001171:
						Play.NoKhi = false;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.ManNguyetCuongPhong_ThemVao_Dame_Def = 0;
						Play.StatusEffect(BitConverter.GetBytes(700014), 0, 0);
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008001172:
						Play.HongNguyetCuongPhong_ThemVao_Dame_Def = 0;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008001694:
						Play.HongNguyetCuongPhong_ThemVao_Dame_Def = 0;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008001855:
						Play.KhiCong_16x_DaiPhu = 0;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008001173:
						Play.TruongHongQuanThien_ThemVao_HP_MP = 0;
						Play.TruongHongQuanThien_ThemVao_Dame_Def = 0;
						Play.CapNhat_HP_MP_SP();
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008001174:
						Play.CharactersToAddMax_HP -= 1000;
						if (Play.CharactersToAddMax_HP < 0)
						{
							Play.CharactersToAddMax_HP = 0;
						}
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008001175:
						Play.CaoCotLieuThuong_ThemVao_Def = 0;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008001176:
						Play.FLD_ThemVaoAiHongPhienDa_GioiHanHPCaoNhat += 0.15;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						Play.CapNhat_HP_MP_SP();
						break;
					case 1008001112:
						Play.CharactersToAddMax_HP -= 800;
						Play.FLD_NhanVat_ThemVao_NeTranh -= 10;
						Play.FLD_NhanVat_ThemVao_CongKich -= 100;
						Play.FLD_NhanVat_ThemVao_PhongNgu -= 50;
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.01;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008001114:
						Play.CharactersToAddMax_HP -= 500;
						Play.FLD_NhanVat_ThemVao_NeTranh -= 10;
						Play.FLD_NhanVat_ThemVao_CongKich -= 100;
						Play.FLD_NhanVat_ThemVao_PhongNgu -= 50;
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_LucPhongNguVoCong -= 50.0;
						Play.YeuHoaThanhThao = false;
						Play.AppendStatusList.Remove(FLD_PID);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					case 1008002169:
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateMartialArtsAndStatus();
						break;
					case 1008002012:
						Play.AppendStatusList.Remove(FLD_PID);
						Play.UpdateCharacterData(Play);
						Play.UpdateBroadcastCharacterData();
						break;
					case 1008001815:
						Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
						Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
						Play.FLD_NhanVat_ThemVao_CongKich -= 80;
						Play.FLD_NhanVat_ThemVao_PhongNgu -= 80;
						Play.CharactersToAddMax_HP -= 1000;
						Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
						if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.15;
						if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
						{
							Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
						}
						Play.FLD_NhanVat_ThemVao_KhiCong--;
						Play.AppendStatusList.Remove(1008001815);
						Play.CapNhat_HP_MP_SP();
						Play.UpdateMartialArtsAndStatus();
						Play.UpdateBroadcastCharacterData();
						Play.UpdateCharacterData(Play);
						Play.UpdateEquipmentEffects();
						break;
					}
					if (Play != null)
					{
						if (Play.GetAddState(FLD_PID))
						{
							Play.AppendStatusList.Remove(FLD_PID);
						}
						Play.UpdateCharacterData(Play);
						Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
						Dispose();
					}
					return;
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Xóa Pill Lỗi [" + FLD_PID + "] - " + ex.ToString());
					return;
				}
				finally
				{
					Dispose();
				}
			}
			if (Play.AppendStatusList != null)
			{
				Play.AppendStatusList.Clear();
			}
			Play.UpdateCharacterData(Play);
			Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
			Dispose();
		}
		else
		{
			Dispose();
		}
	}

	public int getsj()
	{
		return (int)time.Subtract(DateTime.Now).TotalMilliseconds;
	}
}
