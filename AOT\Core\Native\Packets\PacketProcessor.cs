using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Core.Native.Services;
using HeroYulgang.Core.Native.Messaging;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Native.Packets
{
    /// <summary>
    /// Packet processor implementation - thay thế PacketHandlerActor
    /// </summary>
    public class PacketProcessor : ServiceBase, IPacketProcessor
    {
        private readonly PacketProcessorConfiguration _configuration;
        private readonly IPriorityPacketQueue _packetQueue;
        private readonly IEventBus _eventBus;
        private readonly ConcurrentDictionary<PacketType, Func<IPacket, IPacketContext, Task>> _handlers;
        private readonly PacketProcessingStatistics _statistics;
        private readonly Timer _statisticsTimer;
        private readonly Task[] _workerTasks;
        private readonly CancellationTokenSource _workerCancellation;

        public PacketProcessor(
            PacketProcessorConfiguration configuration,
            IPriorityPacketQueue packetQueue,
            IEventBus eventBus) : base("PacketProcessor")
        {
            _configuration = configuration;
            _packetQueue = packetQueue;
            _eventBus = eventBus;
            _handlers = new ConcurrentDictionary<PacketType, Func<IPacket, IPacketContext, Task>>();
            _statistics = new PacketProcessingStatistics { StartTime = DateTime.UtcNow };
            _workerCancellation = new CancellationTokenSource();

            // Create worker tasks
            _workerTasks = new Task[_configuration.WorkerThreadCount];

            // Statistics timer
            _statisticsTimer = new Timer(UpdateStatistics, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

            Logger.Instance.Info($"PacketProcessor created with {_configuration.WorkerThreadCount} workers");
        }

        /// <summary>
        /// Process incoming packet
        /// </summary>
        public async Task ProcessAsync(IPacket packet, IPacketContext context)
        {
            if (State != ServiceState.Running)
            {
                Logger.Instance.Warning($"PacketProcessor not running, dropping packet {packet.Type}");
                return;
            }

            try
            {
                // Enqueue packet for processing
                await _packetQueue.EnqueueAsync(packet, context);
                
                // Update statistics
                Interlocked.Increment(ref _statistics.TotalPacketsProcessed);
                
                if (!_statistics.PacketTypeCounters.ContainsKey(packet.Type))
                {
                    _statistics.PacketTypeCounters[packet.Type] = 0;
                }
                _statistics.PacketTypeCounters[packet.Type]++;
                
                if (!_statistics.PriorityCounters.ContainsKey(packet.Priority))
                {
                    _statistics.PriorityCounters[packet.Priority] = 0;
                }
                _statistics.PriorityCounters[packet.Priority]++;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error processing packet {packet.Type}: {ex.Message}");
                Interlocked.Increment(ref _statistics.ErrorCount);
                
                await _eventBus.PublishAsync(new PacketErrorEvent(packet.SessionId, packet.Type, ex));
            }
        }

        /// <summary>
        /// Register packet handler
        /// </summary>
        public void RegisterHandler<T>(IPacketHandler<T> handler) where T : IPacket
        {
            var packetType = GetPacketTypeFromHandler<T>();
            
            _handlers[packetType] = async (packet, context) =>
            {
                if (packet is T typedPacket)
                {
                    await handler.HandleAsync(typedPacket, context);
                }
                else
                {
                    throw new InvalidOperationException($"Packet type mismatch: expected {typeof(T)}, got {packet.GetType()}");
                }
            };

            Logger.Instance.Info($"Registered handler for packet type {packetType}");
        }

        /// <summary>
        /// Register packet handler với function
        /// </summary>
        public void RegisterHandler<T>(Func<T, IPacketContext, Task> handler) where T : IPacket
        {
            var packetType = GetPacketTypeFromHandler<T>();
            
            _handlers[packetType] = async (packet, context) =>
            {
                if (packet is T typedPacket)
                {
                    await handler(typedPacket, context);
                }
                else
                {
                    throw new InvalidOperationException($"Packet type mismatch: expected {typeof(T)}, got {packet.GetType()}");
                }
            };

            Logger.Instance.Info($"Registered function handler for packet type {packetType}");
        }

        /// <summary>
        /// Unregister packet handler
        /// </summary>
        public void UnregisterHandler<T>() where T : IPacket
        {
            var packetType = GetPacketTypeFromHandler<T>();
            
            if (_handlers.TryRemove(packetType, out _))
            {
                Logger.Instance.Info($"Unregistered handler for packet type {packetType}");
            }
        }

        /// <summary>
        /// Get processing statistics
        /// </summary>
        public PacketProcessingStatistics GetStatistics()
        {
            _statistics.QueueSize = _packetQueue.Count;
            return _statistics;
        }

        /// <summary>
        /// Get packet type from handler generic parameter
        /// </summary>
        private PacketType GetPacketTypeFromHandler<T>() where T : IPacket
        {
            // This is a simplified approach - in real implementation you might need
            // more sophisticated type mapping
            if (typeof(T) == typeof(IPacket))
            {
                throw new ArgumentException("Cannot register handler for base IPacket interface");
            }

            // For now, we'll use a simple mapping based on type name
            // In a real implementation, you might want to use attributes or other mechanisms
            var typeName = typeof(T).Name;
            if (Enum.TryParse<PacketType>(typeName.Replace("Packet", ""), out var packetType))
            {
                return packetType;
            }

            throw new ArgumentException($"Cannot determine packet type for {typeof(T)}");
        }

        /// <summary>
        /// Main service execution - start worker tasks
        /// </summary>
        protected override async Task OnRunningAsync(CancellationToken cancellationToken)
        {
            // Start worker tasks
            for (int i = 0; i < _workerTasks.Length; i++)
            {
                var workerId = i;
                _workerTasks[i] = Task.Run(() => WorkerLoop(workerId, _workerCancellation.Token), cancellationToken);
            }

            Logger.Instance.Info($"Started {_workerTasks.Length} packet processing workers");

            // Wait for cancellation
            try
            {
                await Task.Delay(Timeout.Infinite, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when stopping
            }
        }

        /// <summary>
        /// Worker loop for processing packets
        /// </summary>
        private async Task WorkerLoop(int workerId, CancellationToken cancellationToken)
        {
            Logger.Instance.Debug($"Worker {workerId} started");

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        // Dequeue packet
                        var item = await _packetQueue.DequeueAsync(cancellationToken);
                        if (!item.HasValue)
                            continue;

                        var (packet, context) = item.Value;
                        
                        // Process packet
                        await ProcessPacketInternal(packet, context, workerId);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Worker {workerId} error: {ex.Message}");
                        Interlocked.Increment(ref _statistics.ErrorCount);
                    }
                }
            }
            finally
            {
                Logger.Instance.Debug($"Worker {workerId} stopped");
            }
        }

        /// <summary>
        /// Process individual packet
        /// </summary>
        private async Task ProcessPacketInternal(IPacket packet, IPacketContext context, int workerId)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                // Log packet if enabled
                if (_configuration.EnablePacketLogging)
                {
                    Logger.Instance.Debug($"Worker {workerId} processing packet {packet.Type} from session {packet.SessionId}");
                }

                // Find and execute handler
                if (_handlers.TryGetValue(packet.Type, out var handler))
                {
                    // Execute with timeout
                    using var timeoutCts = new CancellationTokenSource(_configuration.ProcessingTimeout);
                    
                    await handler(packet, context);
                }
                else
                {
                    Logger.Instance.Warning($"No handler for packet type {packet.Type} from session {packet.SessionId}");
                }

                stopwatch.Stop();

                // Update statistics
                var processingTime = stopwatch.Elapsed;
                UpdateProcessingTime(processingTime);

                // Publish event
                await _eventBus.PublishAsync(new PacketProcessedEvent(packet.SessionId, packet.Type, processingTime));

                if (_configuration.EnablePacketLogging)
                {
                    Logger.Instance.Debug($"Worker {workerId} completed packet {packet.Type} in {processingTime.TotalMilliseconds:F2}ms");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                Logger.Instance.Error($"Worker {workerId} error processing packet {packet.Type}: {ex.Message}");
                Interlocked.Increment(ref _statistics.ErrorCount);
                
                await _eventBus.PublishAsync(new PacketErrorEvent(packet.SessionId, packet.Type, ex));
            }
        }

        /// <summary>
        /// Update processing time statistics
        /// </summary>
        private void UpdateProcessingTime(TimeSpan processingTime)
        {
            // Simple moving average - in production you might want more sophisticated statistics
            var currentAverage = _statistics.AverageProcessingTime;
            var newAverage = TimeSpan.FromTicks((currentAverage.Ticks + processingTime.Ticks) / 2);
            _statistics.AverageProcessingTime = newAverage;
        }

        /// <summary>
        /// Update statistics periodically
        /// </summary>
        private void UpdateStatistics(object? state)
        {
            try
            {
                var now = DateTime.UtcNow;
                var elapsed = now - _statistics.StartTime;
                
                if (elapsed.TotalSeconds > 0)
                {
                    _statistics.PacketsPerSecond = (long)(_statistics.TotalPacketsProcessed / elapsed.TotalSeconds);
                }

                _statistics.QueueSize = _packetQueue.Count;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error updating statistics: {ex.Message}");
            }
        }

        protected override async Task OnStoppingAsync(CancellationToken cancellationToken)
        {
            Logger.Instance.Info("Stopping packet processor...");

            // Signal workers to stop
            _workerCancellation.Cancel();

            // Wait for workers to complete
            try
            {
                await Task.WhenAll(_workerTasks).WaitAsync(TimeSpan.FromSeconds(10));
            }
            catch (TimeoutException)
            {
                Logger.Instance.Warning("Some workers did not stop gracefully");
            }

            _statisticsTimer?.Dispose();
            
            await base.OnStoppingAsync(cancellationToken);
        }

        public override void Dispose()
        {
            _workerCancellation?.Dispose();
            _statisticsTimer?.Dispose();
            _packetQueue?.Dispose();
            
            base.Dispose();
        }
    }
}
