﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public
{
	/// <summary>
	/// Item template entity from PublicDb
	/// Contains all item definitions and properties
	/// </summary>
	[JsonObject(MemberSerialization.OptIn), Table(Name = "tbl_xwwl_item", DisableSyncStructure = true)]
	public partial class ItemTemplate
	{

		/// <summary>
		/// Item ID (Primary Key)
		/// </summary>
		[JsonProperty, Column(IsPrimary = true)]
		public int? fld_pid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_reside1 { get; set; }

		[JsonProperty]
		public int? fld_reside2 { get; set; }

		[JsonProperty]
		public int? fld_sex { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty]
		public int? fld_up_level { get; set; }

		[JsonProperty]
		public int? fld_recycle_money { get; set; }

		[JsonProperty]
		public int? fld_sale_money { get; set; }

		[JsonProperty]
		public int? fld_questitem { get; set; }

		[JsonProperty]
		public int? fld_nj { get; set; }

		[JsonProperty]
		public int? fld_df { get; set; }

		[JsonProperty]
		public int? fld_at1 { get; set; }

		[JsonProperty]
		public int? fld_at2 { get; set; }

		[JsonProperty]
		public int? fld_ap { get; set; }

		[JsonProperty]
		public int? fld_job_level { get; set; }

		[JsonProperty]
		public int? fld_zx { get; set; }

		[JsonProperty]
		public int? fld_el { get; set; }

		[JsonProperty]
		public int? fld_wx { get; set; }

		[JsonProperty]
		public int? fld_wxjd { get; set; }

		[JsonProperty]
		public int? fld_money { get; set; }

		[JsonProperty]
		public int? fld_weight { get; set; }

		[JsonProperty]
		public int? fld_type { get; set; }

		[JsonProperty]
		public int? fld_need_money { get; set; }

		[JsonProperty]
		public int? fld_need_fightexp { get; set; }

		[JsonProperty]
		public int? fld_magic1 { get; set; }

		[JsonProperty]
		public int? fld_magic2 { get; set; }

		[JsonProperty]
		public int? fld_magic3 { get; set; }

		[JsonProperty]
		public int? fld_magic4 { get; set; }

		[JsonProperty]
		public int? fld_magic5 { get; set; }

		[JsonProperty]
		public int? fld_side { get; set; }

		[JsonProperty]
		public int? fld_sell_type { get; set; }

		[JsonProperty]
		public int? fld_lock { get; set; }

		[JsonProperty]
		public int? fld_series { get; set; }

		[JsonProperty]
		public int? fld_integration { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_des { get; set; }

		[JsonProperty]
		public int? fld_head_wear { get; set; }

	}

}
